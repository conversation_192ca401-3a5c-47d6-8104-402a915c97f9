"use client";

import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { SubscriptionState, SubscriptionManagerProps } from "./types";
import {
  cancelSubscription,
  pauseSubscription,
  activateSubscription
} from "@/lib/actions/subscription";
import { useSubscriptionProcessing } from "../../context/SubscriptionProcessingContext";

interface UseSubscriptionActionsProps {
  subscriptionState: SubscriptionState;
  subscriptionProps: SubscriptionManagerProps;
}

export const useSubscriptionActions = ({
  subscriptionState,
  subscriptionProps,
}: UseSubscriptionActionsProps) => {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    setSubscriptionCancelled,
    setSubscriptionPaused,
    setSubscriptionResumed,
    // Removed unused variables
    // setPaymentMethodUpdated,
    // setRefundProcessing,
    resetProcessing
  } = useSubscriptionProcessing();

  const { isLoading, setIsLoading, cancelImmediately, setShowCancelDialog } = subscriptionState;
  const { subscriptionId } = subscriptionProps;

  // Handle cancel subscription
  const handleCancelSubscription = useCallback(async () => {
    if (!subscriptionId || isLoading || isProcessing) return;

    setIsLoading(true);
    setIsProcessing(true);

    try {
      const result = await cancelSubscription(cancelImmediately);

      if (result.success) {
        setSubscriptionCancelled();
        toast.success("Subscription cancelled successfully");
        setShowCancelDialog(false);
        router.refresh();
      } else {
        toast.error(result.error || "Failed to cancel subscription");
        resetProcessing();
      }
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      toast.error("An unexpected error occurred");
      resetProcessing();
    } finally {
      setIsLoading(false);
      setIsProcessing(false);
    }
  }, [subscriptionId, isLoading, isProcessing, cancelImmediately, setIsLoading, setShowCancelDialog, router, setSubscriptionCancelled, resetProcessing]);

  // Handle pause subscription
  const handlePauseSubscription = useCallback(async () => {
    if (!subscriptionId || isLoading || isProcessing) return;

    setIsLoading(true);
    setIsProcessing(true);

    try {
      const result = await pauseSubscription();

      if (result.success) {
        setSubscriptionPaused();
        toast.success("Subscription paused successfully");
        router.refresh();
      } else {
        toast.error(result.error || "Failed to pause subscription");
        resetProcessing();
      }
    } catch (error) {
      console.error("Error pausing subscription:", error);
      toast.error("An unexpected error occurred");
      resetProcessing();
    } finally {
      setIsLoading(false);
      setIsProcessing(false);
    }
  }, [subscriptionId, isLoading, isProcessing, setIsLoading, router, setSubscriptionPaused, resetProcessing]);

  // Handle resume subscription
  const handleResumeSubscription = useCallback(async () => {
    if (!subscriptionId || isLoading || isProcessing) return;

    setIsLoading(true);
    setIsProcessing(true);

    try {
      const result = await activateSubscription();

      // Check if result exists and has success property
      if (!result || result.success === undefined) {
        console.error("Invalid response from activateSubscription:", result);
        throw new Error("Received invalid response from server");
      }

      if (result.success) {
        setSubscriptionResumed();
        toast.success("Subscription resumed successfully");
        router.refresh();
      } else {
        toast.error(result.error || "Failed to resume subscription");
        resetProcessing();
      }
    } catch (error) {
      console.error("Error resuming subscription:", error);
      toast.error("An unexpected error occurred");
      resetProcessing();
    } finally {
      setIsLoading(false);
      setIsProcessing(false);
    }
  }, [subscriptionId, isLoading, isProcessing, setIsLoading, router, setSubscriptionResumed, resetProcessing]);

  // Payment method update is no longer supported
  // This function has been removed

  // Handle request refund - now just shows a message to contact support
  const handleRequestRefund = useCallback(async (_speed: "normal" | "optimum") => {
    // Show a toast message directing users to contact support
    toast.info(
      "Refund Request Process",
      {
        description: "Please contact our support team via email to request a refund for payment issues or subscription problems.",
        duration: 5000,
      }
    );

    // Reset any processing state
    resetProcessing();
  }, [resetProcessing]);

  // Plan change is now handled directly in the EnhancedPlanPageWithManager component
  // using the switchAuthenticatedSubscription function

  return {
    handleCancelSubscription,
    handlePauseSubscription,
    handleResumeSubscription,
    handleRequestRefund
  };
};
