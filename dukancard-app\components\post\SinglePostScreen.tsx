import React, { useRef, useCallback, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { useSinglePost } from '@/hooks/useSinglePost';
import { useColorScheme } from '@/hooks/useColorScheme';
import { PostCard } from '@/components/feed/PostCard';
import PostOptionsBottomSheet, { PostOptionsBottomSheetRef } from '@/components/feed/PostOptionsBottomSheet';
import { CustomerPostEditModal } from '@/components/feed/CustomerPostEditModal';
import { BusinessPostEditModal } from '@/components/feed/BusinessPostEditModal';
import { sharePost } from '@/lib/services/postInteractions';
import PostErrorBoundary from './PostErrorBoundary';
import { AlertCircle, Wifi, WifiOff } from 'lucide-react-native';
import { AlertDialog } from '@/components/ui/AlertDialog';
import { useAlert } from '@/hooks/useAlert';
import { deleteCustomerPost } from '@/lib/actions/customerPosts';
import { deleteBusinessPost } from '@/lib/actions/businessPosts';

interface SinglePostScreenProps {
  postId: string;
}

/**
 * Main screen component for displaying a single post in React Native
 * Integrates with existing PostCard for consistent styling
 */
export default function SinglePostScreen({ postId }: SinglePostScreenProps) {
  const { post, loading, error, refetch } = useSinglePost(postId);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Bottom sheet state
  const postOptionsRef = useRef<PostOptionsBottomSheetRef>(null);
  const [isOwner, setIsOwner] = useState(false);

  // Edit modal state
  const [showEditModal, setShowEditModal] = useState(false);

  // Alert dialog state
  const { alertState, showAlert, hideAlert, showConfirm } = useAlert();

  // Theme colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const errorColor = isDark ? '#EF4444' : '#DC2626';

  // Handle more press from PostCard
  const handleMorePress = useCallback((_post: any, isPostOwner: boolean) => {
    setIsOwner(isPostOwner);
    postOptionsRef.current?.present();
  }, []);

  // Handle share post
  const handleSharePost = useCallback(async () => {
    if (!post) return;

    const result = await sharePost(
      post.id,
      post.content,
      post.author_name || 'Unknown',
      post.business_slug || undefined
    );

    if (!result.success && result.message !== 'Share cancelled') {
      showAlert({
        type: 'error',
        title: 'Error',
        message: result.message,
        buttons: [
          {
            text: 'OK',
            onPress: hideAlert
          }
        ]
      });
    }

    postOptionsRef.current?.dismiss();
  }, [post, showAlert, hideAlert]);

  // Handle edit post
  const handleEditPost = useCallback(() => {
    setShowEditModal(true);
    postOptionsRef.current?.dismiss();
  }, [setShowEditModal]);

  // Handle delete post
  const handleDeletePost = useCallback(() => {
    if (!post) return;

    showConfirm(
      'Delete Post',
      'Are you sure you want to delete this post? This action cannot be undone.',
      async () => {
        try {
          let result;
          if (post.post_source === 'customer') {
            result = await deleteCustomerPost(post.id);
          } else {
            result = await deleteBusinessPost(post.id);
          }

          if (result.success) {
            showAlert({
              type: 'success',
              title: 'Success',
              message: 'Post deleted successfully',
              buttons: [
                {
                  text: 'OK',
                  onPress: () => {
                    hideAlert();
                    // Navigate back or refresh
                    // You might want to navigate back to the feed here
                  }
                }
              ]
            });
          } else {
            showAlert({
              type: 'error',
              title: 'Error',
              message: result.message || 'Failed to delete post',
              buttons: [
                {
                  text: 'OK',
                  onPress: hideAlert
                }
              ]
            });
          }
        } catch (error) {
          console.error('Error deleting post:', error);
          showAlert({
            type: 'error',
            title: 'Error',
            message: 'Failed to delete post. Please try again.',
            buttons: [
              {
                text: 'OK',
                onPress: hideAlert
              }
            ]
          });
        }
      },
      undefined,
      {
        confirmText: 'Delete',
        cancelText: 'Cancel',
        destructive: true
      }
    );
    postOptionsRef.current?.dismiss();
  }, [post, showConfirm, showAlert, hideAlert]);

  // Handle post updated
  const handlePostUpdated = useCallback((_postId: string, _newContent: string, _newImageUrl?: string | null, _newProductIds?: string[]) => {
    setShowEditModal(false);
    // Refresh the post data
    refetch();
  }, [refetch, setShowEditModal]);

  // Loading state
  if (loading) {
    return (
      <View style={[styles.centerContainer, { backgroundColor }]}>
        <ActivityIndicator size="large" color="#D4AF37" />
        <Text style={[styles.loadingText, { color: textColor }]}>
          Loading post...
        </Text>
      </View>
    );
  }

  // Error state
  if (error || !post) {
    const isNetworkError = error?.includes('network') || error?.includes('fetch') || error?.includes('timeout');

    return (
      <View style={[styles.centerContainer, { backgroundColor }]}>
        {isNetworkError ? (
          <WifiOff size={48} color={errorColor} />
        ) : (
          <AlertCircle size={48} color={errorColor} />
        )}
        <Text style={[styles.errorTitle, { color: textColor }]}>
          {error === 'Post not found' ? 'Post Not Found' :
           isNetworkError ? 'Connection Error' : 'Error Loading Post'}
        </Text>
        <Text style={[styles.errorMessage, { color: textColor }]}>
          {isNetworkError ? 'Please check your internet connection and try again.' :
           error || 'The post could not be loaded'}
        </Text>

        {/* Retry button for network errors */}
        {isNetworkError && (
          <TouchableOpacity
            style={[styles.retryButton, { borderColor: textColor }]}
            onPress={refetch}
          >
            <Wifi size={20} color={textColor} />
            <Text style={[styles.retryButtonText, { color: textColor }]}>
              Try Again
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <PostErrorBoundary>
      <ScrollView
        style={[styles.container, { backgroundColor }]}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={refetch}
            tintColor="#D4AF37"
            colors={['#D4AF37']}
          />
        }
      >
        {/* Main post content using existing PostCard - no extra padding to match feed */}
        <PostCard
          post={post}
          index={0}
          onMorePress={handleMorePress}
          showActualAspectRatio={true}
          disablePostClick={true}
          enableImageFullscreen={true}
        />
      </ScrollView>

      {/* Post Options Bottom Sheet */}
      {post && (
        <PostOptionsBottomSheet
          ref={postOptionsRef}
          postSource={post.post_source}
          isOwner={isOwner}
          onEditPost={handleEditPost}
          onEditProducts={undefined} // Products can be edited directly in edit post modal
          onDeletePost={handleDeletePost}
          onSharePost={handleSharePost}
        />
      )}

      {/* Edit Modals */}
      {post && post.post_source === 'customer' ? (
        <CustomerPostEditModal
          visible={showEditModal}
          onClose={() => setShowEditModal(false)}
          postId={post.id}
          initialContent={post.content}
          initialImageUrl={post.image_url}
          customerName={post.author_name || undefined}
          onPostUpdated={handlePostUpdated}
        />
      ) : post && post.post_source === 'business' ? (
        <BusinessPostEditModal
          visible={showEditModal}
          onClose={() => setShowEditModal(false)}
          postId={post.id}
          initialContent={post.content}
          initialImageUrl={post.image_url}
          initialProductIds={post.product_ids || []}
          businessName={post.author_name || undefined}
          onPostUpdated={handlePostUpdated}
        />
      ) : null}

      {/* Alert Dialog */}
      <AlertDialog
        visible={alertState.visible}
        type={alertState.type}
        title={alertState.title}
        message={alertState.message}
        buttons={alertState.buttons || []}
        onClose={hideAlert}
        showCloseButton={alertState.showCloseButton}
        customIcon={alertState.customIcon}
      />
    </PostErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  errorTitle: {
    marginTop: 16,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  errorMessage: {
    marginTop: 8,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },



  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderWidth: 1,
    borderRadius: 8,
    gap: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
