"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Bell } from "lucide-react";
import { useRouter } from "next/navigation";
import SubscriptionListClient from "../SubscriptionListClient";

import { SubscriptionSearch, SubscriptionPagination, SubscriptionListSkeleton } from "@/app/components/shared/subscriptions";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { cn, formatIndianNumberShort } from "@/lib/utils";
import { SubscriptionWithProfile } from "../actions";

interface SubscriptionsPageClientProps {
  initialSubscriptions: SubscriptionWithProfile[];
  totalCount: number;
  currentPage: number;
  searchTerm: string;
}

export default function SubscriptionsPageClient({
  initialSubscriptions,
  totalCount,
  currentPage,
  searchTerm,
}: SubscriptionsPageClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  // Calculate total pages
  const itemsPerPage = 10;
  const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));

  // Reset loading state when component receives new data
  useEffect(() => {
    setIsLoading(false);
  }, [initialSubscriptions]);

  // Handle search - use useCallback to prevent infinite re-renders
  const handleSearch = useCallback((term: string) => {
    setIsLoading(true); // Show loading state
    const params = new URLSearchParams();
    if (term) {
      params.set('search', term);
    }
    params.set('page', '1'); // Reset to page 1 on new search
    router.push(`/dashboard/customer/subscriptions?${params.toString()}`);
  }, [router]);

  // Handle page change - use useCallback to prevent infinite re-renders
  const handlePageChange = useCallback((page: number) => {
    setIsLoading(true); // Show loading state
    const params = new URLSearchParams();
    if (searchTerm) {
      params.set('search', searchTerm);
    }
    params.set('page', page.toString());
    router.push(`/dashboard/customer/subscriptions?${params.toString()}`);
  }, [router, searchTerm]);

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="relative space-y-6 max-w-6xl mx-auto"
    >
      <motion.div variants={itemVariants} className="mb-6 relative z-10">
        {/* Main card container using shadcn Card component */}
        <Card className={cn(
          "border shadow-md transition-all duration-300 hover:shadow-lg",
          "bg-white dark:bg-black border-neutral-200 dark:border-neutral-800"
        )}>
          <CardHeader className="pb-4 border-b border-neutral-100 dark:border-neutral-800">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-500 dark:text-blue-400 self-start">
                <Bell className="w-5 h-5" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Subscribed Businesses ({formatIndianNumberShort(totalCount)})
                </h3>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                  Businesses you&apos;re following for updates
                </p>
              </div>
            </div>

            {/* Search component */}
            <div className="mt-4">
              <SubscriptionSearch
                onSearch={handleSearch}
                initialSearchTerm={searchTerm}
                placeholder="Search businesses..."
              />
            </div>
          </CardHeader>

          <CardContent className="pt-4">
            {/* Subscription count */}
            {searchTerm && !isLoading && (
              <div className="mb-4 text-sm text-neutral-500 dark:text-neutral-400">
                Found {formatIndianNumberShort(totalCount)} {totalCount === 1 ? 'business' : 'businesses'}
                {searchTerm ? ` matching "${searchTerm}"` : ''}
              </div>
            )}

            {/* Show skeleton loader when loading */}
            {isLoading ? (
              <SubscriptionListSkeleton />
            ) : (
              <>
                {/* Subscription List */}
                <SubscriptionListClient initialSubscriptions={initialSubscriptions} />

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-6">
                    <SubscriptionPagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
