"use client";

import React, { useState, useCallback, useEffect } from "react";
import <PERSON><PERSON><PERSON>, { Point, Area } from "react-easy-crop";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { Loader2, Crop } from "lucide-react";
import { Slider } from "@/components/ui/slider";

// Helper function to create an image element
const createImage = (url: string): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener("load", () => resolve(image));
    image.addEventListener("error", (error) => reject(error));
    image.setAttribute("crossOrigin", "anonymous");
    image.src = url;
  });

// Helper function to get the cropped image blob
async function getCroppedImgBlob(
  imageSrc: string,
  pixelCrop: Area
): Promise<Blob | null> {
  const image = await createImage(imageSrc);
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  if (!ctx) {
    return null;
  }

  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;
  const pixelRatio = window.devicePixelRatio || 1;

  canvas.width = pixelCrop.width * pixelRatio * scaleX;
  canvas.height = pixelCrop.height * pixelRatio * scaleY;

  ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);
  ctx.imageSmoothingQuality = "high";

  ctx.drawImage(
    image,
    pixelCrop.x * scaleX,
    pixelCrop.y * scaleY,
    pixelCrop.width * scaleX,
    pixelCrop.height * scaleY,
    0,
    0,
    pixelCrop.width * scaleX,
    pixelCrop.height * scaleY
  );

  return new Promise((resolve) => {
    canvas.toBlob(
      resolve,
      "image/png" // Output as PNG from canvas
    );
  });
}

interface CustomAdCropDialogProps {
  imgSrc: string | null;
  onCropComplete: (_blob: Blob | null) => void | Promise<void>;
  onClose: () => void;
  isOpen: boolean;
  isUploading?: boolean; // Add external upload state
}

export default function CustomAdCropDialog({
  imgSrc,
  onCropComplete,
  onClose,
  isOpen,
  isUploading = false,
}: CustomAdCropDialogProps) {
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [isCropping, setIsCropping] = useState(false);

  const onCropCompleteCallback = useCallback((_croppedArea: Area, croppedAreaPixels: Area) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const handleCrop = async () => {
    if (!imgSrc || !croppedAreaPixels) {
      console.warn("Image source or crop area not available.");
      onCropComplete(null);
      return;
    }
    setIsCropping(true);
    try {
      const croppedBlob = await getCroppedImgBlob(imgSrc, croppedAreaPixels);
      onCropComplete(croppedBlob);
    } catch (e) {
      console.error("Error cropping image:", e);
      onCropComplete(null);
    } finally {
      setIsCropping(false);
      // Note: Dialog will stay open until parent component sets isUploading to false
    }
  };

  // Reset zoom when dialog opens
  useEffect(() => {
    if (isOpen) {
      setZoom(1);
      setCrop({ x: 0, y: 0 });
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && !isUploading && !isCropping && onClose()}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Crop className="h-5 w-5 text-primary" />
            Crop Your Advertisement
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Crop area */}
          <div className="relative h-[45vh] md:h-[55vh] w-full bg-neutral-100 dark:bg-neutral-800 rounded-lg overflow-hidden border-2 border-primary/20">
            {imgSrc ? (
              <Cropper
                image={imgSrc}
                crop={crop}
                zoom={zoom}
                aspect={16 / 9} // 16:9 aspect ratio for custom ads
                cropShape="rect"
                showGrid={true}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={onCropCompleteCallback}
                style={{
                  containerStyle: {
                    borderRadius: '8px',
                    overflow: 'hidden',
                  },
                  cropAreaStyle: {
                    border: '2px solid hsl(var(--primary))',
                    borderRadius: '4px',
                  },
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">Loading image...</p>
                </div>
              </div>
            )}
          </div>

          {/* Zoom control */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Zoom: {Math.round(zoom * 100)}%
            </label>
            <Slider
              value={[zoom]}
              onValueChange={(value) => setZoom(value[0])}
              min={1}
              max={3}
              step={0.1}
              className="w-full"
            />
          </div>

          {/* Preview info */}
          <div className="bg-neutral-50 dark:bg-neutral-900 rounded-lg p-3 border">
            <div className="flex items-center justify-center text-sm">
              <span className="text-muted-foreground">Cropping to 16:9 aspect ratio for advertisement display</span>
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isCropping || isUploading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleCrop}
            disabled={isCropping || isUploading || !croppedAreaPixels}
            className="min-w-[120px]"
          >
            {isCropping || isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isCropping ? "Processing..." : "Uploading..."}
              </>
            ) : (
              <>
                <Crop className="mr-2 h-4 w-4" />
                Upload Ad
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
