"use client";

import { PaymentStatusType } from "@/lib/types/subscription";

/**
 * Subscription state interface for managing UI state
 */
export interface SubscriptionState {
  isLoading: boolean;
  showCancelDialog: boolean;
  setShowCancelDialog: (_show: boolean) => void;
  cancelImmediately: boolean;
  setCancelImmediately: (_immediately: boolean) => void;
  isWithinRefundWindow: boolean;
  setIsWithinRefundWindow: (_isWithin: boolean) => void;
  subscriptionDetails: Record<string, unknown> | null;
  setSubscriptionDetails: (_details: Record<string, unknown> | null) => void;
  // These properties were used for the old plan switching UI
  // Now handled directly in EnhancedPlanPageWithManager
  showUpdateOptionsDialog: boolean;
  setShowUpdateOptionsDialog: (_show: boolean) => void;
  updateOptions: SubscriptionUpdateOptions | null;
  setUpdateOptions: (_options: SubscriptionUpdateOptions | null) => void;
  setIsLoading: (_loading: boolean) => void;
  showTrialWarningDialog?: boolean;
  setShowTrialWarningDialog?: (_show: boolean) => void;
  trialSubscriptionId?: string | null;
  setTrialSubscriptionId?: (_id: string) => void;
}

/**
 * Subscription update options interface
 *
 * Note: This interface is kept for backward compatibility but is no longer actively used.
 * Plan switching is now handled directly in EnhancedPlanPageWithManager using the
 * switchAuthenticatedSubscription function.
 */
export interface SubscriptionUpdateOptions {
  planId?: string;
  planCycle?: "monthly" | "yearly";
  updateImmediately?: boolean;
  paymentMethod?: string;
  currentPlan?: string;
  currentCycle?: "monthly" | "yearly";
  newPlan?: string;
  newCycle?: "monthly" | "yearly";
  subscriptionId?: string;
  currentEndDate?: string;
}

/**
 * Payment interface for subscription payments
 */
export interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: PaymentStatusType;
  date: string;
  paymentMethod: string;
  refundId?: string;
  refundStatus?: string;
  refundAmount?: number;
  invoiceUrl?: string;
}

/**
 * Subscription manager props interface
 */
export interface SubscriptionManagerProps {
  userId?: string;
  subscriptionId: string | null;
  status: string | null;
  subscriptionStatus?: string;
  planName: string;
  planCycle: "monthly" | "yearly";
  amount: number;
  currency?: string;
  nextBillingDate?: string | null;
  lastPaymentDate?: string | null;
  lastPaymentStatus?: string | null;
  lastPaymentMethod?: string | null;
  createdAt?: string | null;
  expiresAt?: string | null;
  pausedAt?: string | null;
  cancellationRequestedAt?: string | null;
  cancellationScheduledAt?: string | null;
  cancelledAt?: string | null;
  isEligibleForRefund?: boolean;
  authenticatedSubscription?: {
    id: string;
    [key: string]: unknown;
  };
  currentPlanDetails?: {
    id: string;
    name: string;
  };
  selectedPlan?: {
    id: string;
    name: string;
  };
  selectedCycle?: "monthly" | "yearly";
}
