# Tasks: Single Post Page Implementation (React Native)

## Relevant Files

- `app/post/[postId].tsx` - Main single post screen using Expo Router ✅
- `components/post/SinglePostScreen.tsx` - Single post display component ✅
- `components/post/SinglePostScreen.test.tsx` - Unit tests for SinglePostScreen component
- `components/post/PostShareButton.tsx` - Native share button component for posts ✅
- `components/post/PostShareButton.test.tsx` - Unit tests for PostShareButton component
- `lib/actions/posts/fetchSinglePost.ts` - Function to fetch individual post data ✅
- `lib/actions/posts/fetchSinglePost.test.ts` - Unit tests for fetchSinglePost function
- `lib/utils/postUrl.ts` - Utility functions for post URL generation and deep linking ✅
- `lib/utils/postUrl.test.ts` - Unit tests for postUrl utilities
- `hooks/useSinglePost.ts` - Custom hook for single post data management ✅
- `hooks/useSinglePost.test.ts` - Unit tests for useSinglePost hook
- `components/feed/PostCard.tsx` - Updated to include navigation to single post screens ✅
- `components/post/PostErrorBoundary.tsx` - Error boundary component for post-related screens ✅
- `app.config.js` - Updated with deep linking configuration ✅

### Notes

- Unit tests should be placed alongside the code files they are testing
- Use `npx jest [optional/path/to/test/file]` to run tests
- Use Expo Router for navigation and dynamic routing
- Implement native sharing using React Native's Share API
- Follow existing app patterns for data fetching and state management

## Tasks

- [x] 1.0 Create Single Post Screen Route Structure
  - [x] 1.1 Create `app/post/[postId].tsx` with Expo Router dynamic route
  - [x] 1.2 Set up proper TypeScript interfaces for route params and navigation
  - [x] 1.3 Configure screen options (header, title, back button) for single post screen
  - [x] 1.4 Implement proper parameter validation and type safety
  - [x] 1.5 Set up screen layout with proper SafeAreaView and styling
  - [ ] 1.6 Test dynamic routing with various post IDs and navigation scenarios

- [x] 2.0 Implement Post Data Fetching Logic
  - [x] 2.1 Create `lib/actions/posts/fetchSinglePost.ts` function for mobile
  - [x] 2.2 Implement post fetching using unified_posts view with Supabase client
  - [x] 2.3 Create `hooks/useSinglePost.ts` custom hook for data management
  - [x] 2.4 Add proper loading states and error handling in the hook
  - [x] 2.5 Implement caching strategy using React Query or similar
  - [x] 2.6 Handle edge cases (network errors, invalid IDs, deleted posts)
  - [x] 2.7 Add proper TypeScript types for single post data and states

- [x] 3.0 Build Single Post Display Components
  - [x] 3.1 Create `components/post/SinglePostScreen.tsx` main screen component
  - [x] 3.2 Integrate existing `PostCard` component for consistent styling
  - [x] 3.3 Ensure all post data is displayed (content, image, author, timestamp, location)
  - [x] 3.4 Add proper responsive design for various screen sizes and orientations
  - [x] 3.5 Implement proper image loading with React Native Image component
  - [x] 3.6 Add linked products display using existing ProductCard components
  - [x] 3.7 Display mentioned businesses with proper native styling
  - [x] 3.8 Add proper scrolling behavior for long content

- [x] 4.0 Add Native Post Sharing Functionality
  - [x] 4.1 Create `components/post/PostShareButton.tsx` native component
  - [x] 4.2 Implement React Native Share API for native sharing
  - [x] 4.3 Add deep link URL generation for shared posts
  - [x] 4.4 Create `lib/utils/postUrl.ts` for URL and deep link utilities
  - [x] 4.5 Add proper error handling for sharing failures
  - [x] 4.6 Style share button with native icons and proper touch feedback
  - [ ] 4.7 Test sharing functionality on both iOS and Android platforms

- [x] 5.0 Implement Navigation and Deep Linking
  - [x] 5.1 Update existing `PostCard` component to handle tap navigation
  - [x] 5.2 Add proper TouchableOpacity wrapper for post content
  - [x] 5.3 Implement navigation using Expo Router's navigation methods
  - [x] 5.4 Configure deep linking for `/post/[postId]` URLs
  - [ ] 5.5 Test navigation from various feed screens (customer, business)
  - [x] 5.6 Add proper back navigation and header configuration
  - [ ] 5.7 Test deep link handling from external apps and browsers

- [x] 6.0 Add Error Handling and Loading States
  - [x] 6.1 Implement comprehensive error boundaries for single post screens
  - [x] 6.2 Create native loading indicators and skeletons
  - [x] 6.3 Add error screens for network failures, invalid post IDs
  - [x] 6.4 Implement pull-to-refresh functionality for post reloading
  - [x] 6.5 Add proper offline handling and retry mechanisms
  - [x] 6.6 Create user-friendly error messages with native styling
  - [ ] 6.7 Test error scenarios on both iOS and Android platforms

- [ ] 7.0 Write Tests and Documentation
  - [ ] 7.1 Write unit tests for `fetchSinglePost` function
  - [ ] 7.2 Write tests for `useSinglePost` custom hook
  - [ ] 7.3 Write component tests for `SinglePostScreen` component
  - [ ] 7.4 Write tests for `PostShareButton` functionality
  - [ ] 7.5 Write integration tests for complete navigation flow
  - [ ] 7.6 Test deep linking and URL generation utilities
  - [ ] 7.7 Add JSDoc comments and documentation for all new functions
  - [ ] 7.8 Update project README with single post screen feature documentation
