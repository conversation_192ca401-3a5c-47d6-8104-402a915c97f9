"use client";

import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useRouter } from "next/navigation";

interface ErrorDialogProps {
  open: boolean;
  onOpenChange: (_open: boolean) => void;
  errorMessage: string;
}

export default function ErrorDialog({
  open,
  onOpenChange,
  errorMessage,
}: ErrorDialogProps) {
  const router = useRouter();

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="bg-card border-border">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-foreground flex items-center gap-2">
            <AlertCircle className="w-6 h-6 text-[var(--brand-gold)]" />
            Error
          </AlertDialogTitle>
          <AlertDialogDescription className="text-muted-foreground">
            {errorMessage}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <Button
            onClick={() => {
              onOpenChange(false);
              router.push("/?view=home");
            }}
            className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] cursor-pointer"
          >
            Okay
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
