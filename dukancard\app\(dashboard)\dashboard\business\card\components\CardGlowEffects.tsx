export default function CardGlowEffects() {
  return (
    <>
      {/* Subtle glow effects */}
      <div className="absolute -top-24 -right-24 w-48 h-48 rounded-full bg-[var(--theme-color-10)] blur-3xl dark:bg-[var(--theme-color-20)] dark:blur-2xl opacity-20 dark:opacity-15"></div>
      <div className="absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-[var(--theme-color-10)] blur-3xl dark:bg-[var(--theme-color-20)] dark:blur-2xl opacity-20 dark:opacity-15"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full bg-[var(--theme-color-5)] blur-3xl opacity-15 dark:opacity-10"></div>

      {/* Enhanced inner glow effect with theme color */}
      <div className="absolute inset-0 rounded-xl shadow-[inset_0_0_20px_rgba(0,0,0,0.1),inset_0_0_5px_var(--theme-color-20)] dark:shadow-[inset_0_0_20px_rgba(255,255,255,0.03),inset_0_0_5px_var(--theme-color-30)] pointer-events-none z-25"></div>

      {/* Vibrant edge glow effect */}
      <div
        className="absolute inset-x-0 bottom-0 h-[3px] pointer-events-none z-30"
        style={{
          background: `linear-gradient(to top, var(--theme-color), transparent)`,
          opacity: 0.8,
        }}
      ></div>
      <div
        className="absolute inset-y-0 right-0 w-[3px] pointer-events-none z-30"
        style={{
          background: `linear-gradient(to left, var(--theme-color), transparent)`,
          opacity: 0.8,
        }}
      ></div>
      <div
        className="absolute inset-x-0 top-0 h-[3px] pointer-events-none z-30"
        style={{
          background: `linear-gradient(to bottom, var(--theme-color), transparent)`,
          opacity: 0.8,
        }}
      ></div>
      <div
        className="absolute inset-y-0 left-0 w-[3px] pointer-events-none z-30"
        style={{
          background: `linear-gradient(to right, var(--theme-color), transparent)`,
          opacity: 0.8,
        }}
      ></div>

      {/* Enhanced edge effects to simulate premium card thickness */}
      <div className="absolute inset-x-0 bottom-0 h-4 bg-gradient-to-t from-black/40 to-transparent pointer-events-none z-25"></div>
      <div className="absolute inset-y-0 right-0 w-4 bg-gradient-to-l from-black/40 to-transparent pointer-events-none z-25"></div>

      {/* Enhanced highlight on top edge for premium look */}
      <div className="absolute inset-x-0 top-0 h-[4px] bg-gradient-to-b from-white/60 to-transparent pointer-events-none z-25"></div>
      <div className="absolute inset-y-0 left-0 w-[4px] bg-gradient-to-r from-white/60 to-transparent pointer-events-none z-25"></div>

      {/* Colorful edge highlight */}
      <div className="absolute inset-x-0 bottom-0 h-1 bg-gradient-to-t from-[var(--theme-color-30)] to-transparent pointer-events-none z-26 opacity-70"></div>
      <div className="absolute inset-y-0 right-0 w-1 bg-gradient-to-l from-[var(--theme-color-30)] to-transparent pointer-events-none z-26 opacity-70"></div>

      {/* Enhanced shadow for depth with theme color influence */}
      <div className="absolute inset-0 shadow-[0_15px_60px_rgba(0,0,0,0.3),0_5px_20px_var(--theme-color-20)] dark:shadow-[0_15px_60px_rgba(0,0,0,0.5),0_5px_20px_var(--theme-color-30)] rounded-xl pointer-events-none z-25"></div>
    </>
  );
}
