"use client";

import { motion, AnimatePresence } from "framer-motion";
import React, { useState, useEffect, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  pincodeSchema,
  citySchema,
  LocationSearchFormData,
  CitySearchFormData,
} from "@/lib/schemas/locationSchemas";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  MapPin,
  Loader2,
  Building,
  ArrowRight,
  X
} from "lucide-react";
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList
} from "@/components/ui/command";
import { getCitySuggestionsClient } from "@/lib/client/locationUtils";
// Removed PopularCategoriesSection import as it's now in a separate component

interface ModernSearchSectionProps {
  minimal?: boolean;
}

export default function ModernSearchSection({ }: ModernSearchSectionProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingCities, setIsLoadingCities] = useState(false);
  const [searchType, setSearchType] = useState<"pincode" | "city">("pincode");
  const [citySuggestions, setCitySuggestions] = useState<string[]>([]);
  const [cityQuery, setCityQuery] = useState("");
  const [_selectedCity, setSelectedCity] = useState("");
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const cityInputRef = useRef<HTMLInputElement>(null);
  const [_isHovered, setIsHovered] = useState(false);

  // Create a unique ID for the layout animation to ensure it works on initial render
  const [layoutId] = useState(`homepage-search-background-${Math.random()}`);

  // Pincode search form using the existing schema
  const pincodeForm = useForm<LocationSearchFormData>({
    resolver: zodResolver(pincodeSchema),
    defaultValues: {
      pincode: "",
      locality: null,
    },
    mode: "onChange",
  });

  // City search form using the city schema
  const cityForm = useForm<CitySearchFormData>({
    resolver: zodResolver(citySchema),
    defaultValues: {
      city: "",
      locality: null,
    },
    mode: "onChange",
  });

  // Fetch city suggestions when user types
  useEffect(() => {
    const fetchCitySuggestions = async () => {
      if (cityQuery.length < 2) {
        setCitySuggestions([]);
        setIsLoadingCities(false);
        return;
      }

      setIsLoadingCities(true);
      try {
        const result = await getCitySuggestionsClient(cityQuery);

        if (result.cities) {
          // Log the city suggestions for debugging
          console.log("City suggestions:", result.cities);
          setCitySuggestions(result.cities);
        } else if (result.error) {
          console.error("Error fetching city suggestions:", result.error);
        }
      } catch (error) {
        console.error("Error fetching city suggestions:", error);
      } finally {
        setIsLoadingCities(false);
      }
    };

    fetchCitySuggestions();
  }, [cityQuery]);

  // Handle pincode form submission
  const onPincodeSubmit = (data: LocationSearchFormData) => {
    setIsLoading(true);

    // Use a slight delay to ensure the loading state is visible
    setTimeout(() => {
      try {
        // Create the URL with search parameters
        const params = new URLSearchParams();
        params.set("pincode", data.pincode);

        // Add locality if present
        if (data.locality) {
          params.set("locality", data.locality);
        }

        const url = `/discover?${params.toString()}`;

        // Use window.location for a full page navigation
        window.location.href = url;
      } catch (error) {
        console.error("Navigation failed:", error);
        setIsLoading(false);
      }
    }, 500); // Small delay for better UX
  };

  // Handle city form submission
  const onCitySubmit = (data: CitySearchFormData) => {
    setIsLoading(true);

    // Use a slight delay to ensure the loading state is visible
    setTimeout(() => {
      try {
        // Create the URL with search parameters
        const params = new URLSearchParams();
        params.set("city", data.city);

        // Add locality if present
        if (data.locality) {
          params.set("locality", data.locality);
        }

        const url = `/discover?${params.toString()}`;

        // Use window.location for a full page navigation
        window.location.href = url;
      } catch (error) {
        console.error("Navigation failed:", error);
        setIsLoading(false);
      }
    }, 500); // Small delay for better UX
  };

  // Handle city selection from dropdown
  const handleCitySelect = (city: string) => {
    setSelectedCity(city);
    cityForm.setValue("city", city, { shouldValidate: true });
    setCityQuery(city);
    setIsCityDropdownOpen(false);
  };

  // Animation is handled directly in the components

  return (
    <div className="w-full">
      {/* Centered header text */}
      <motion.div
        className="text-center mb-6"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7 }}
      >
        <h2 className="text-2xl md:text-3xl font-bold mb-2 relative inline-block">
          Find <span className="text-[var(--brand-gold)]">Businesses</span> Near You
          <motion.div
            className="absolute -bottom-1 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-[var(--brand-gold)]/70 to-transparent"
            initial={{ width: "0%", left: "50%" }}
            animate={{ width: "100%", left: "0%" }}
            transition={{ duration: 1, delay: 0.3 }}
          />
        </h2>

        <p className="text-neutral-600 dark:text-neutral-400">
          Search across <span className="font-medium">2.8 Lakh+</span> businesses and services
        </p>
      </motion.div>

      {/* Main search container */}
      <motion.div
        className="relative w-full"
        initial={{ opacity: 1 }}
        animate={{ opacity: 1 }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        key={`homepage-search-container-${searchType}`}
      >
        {/* Background elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Animated background elements */}
          <motion.div
            className="absolute top-1/3 left-1/4 w-32 h-32 rounded-full bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 blur-xl"
            animate={{
              y: [0, -15, 0],
              scale: [1, 1.1, 1],
              opacity: [0.5, 0.7, 0.5],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />

          <motion.div
            className="absolute bottom-1/4 right-1/4 w-40 h-40 rounded-full bg-blue-500/5 dark:bg-blue-400/10 blur-xl"
            animate={{
              y: [0, 20, 0],
              scale: [1, 1.15, 1],
              opacity: [0.4, 0.6, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          />
        </div>

        {/* Search type selector */}
        <motion.div
          className="flex justify-center mb-5"
          initial={{ opacity: 1, y: 0 }}
          animate={{ opacity: 1, y: 0 }}
          layoutRoot
        >
          <div className="inline-flex rounded-full p-1 bg-white/30 dark:bg-neutral-800/30 backdrop-blur-md border border-neutral-200/50 dark:border-neutral-700/50">
            <motion.button
              className={`relative px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                searchType === "pincode"
                  ? "text-[var(--brand-gold-foreground)]"
                  : "text-neutral-600 dark:text-neutral-400"
              }`}
              onClick={() => setSearchType("pincode")}
              initial={{ opacity: 1 }}
              animate={searchType === "pincode" ? { opacity: 1, scale: 1.05 } : { opacity: 0.7, scale: 1 }}
              whileHover={{ scale: searchType === "pincode" ? 1.05 : 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Always render the background for pincode button */}
              {searchType === "pincode" && (
                <motion.div
                  className="absolute inset-0 bg-[var(--brand-gold)] rounded-full -z-10"
                  layoutId={layoutId}
                  initial={{ opacity: 1 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", duration: 0.5 }}
                />
              )}
              <div className="flex items-center gap-1.5">
                <MapPin className="h-3.5 w-3.5" />
                <span>Pincode</span>
              </div>
            </motion.button>

            <motion.button
              className={`relative px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                searchType === "city"
                  ? "text-[var(--brand-gold-foreground)]"
                  : "text-neutral-600 dark:text-neutral-400"
              }`}
              onClick={() => setSearchType("city")}
              initial={{ opacity: 1 }}
              animate={searchType === "city" ? { opacity: 1, scale: 1.05 } : { opacity: 0.7, scale: 1 }}
              whileHover={{ scale: searchType === "city" ? 1.05 : 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Always render the background for city button */}
              {searchType === "city" && (
                <motion.div
                  className="absolute inset-0 bg-[var(--brand-gold)] rounded-full -z-10"
                  layoutId={layoutId}
                  initial={{ opacity: 1 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", duration: 0.5 }}
                />
              )}
              <div className="flex items-center gap-1.5">
                <Building className="h-3.5 w-3.5" />
                <span>City</span>
              </div>
            </motion.button>
          </div>
        </motion.div>

        {/* Search input area */}
        <div className="w-full">
          <AnimatePresence mode="wait" initial={false}>
          {searchType === "pincode" ? (
            <motion.div
              key="homepage-pincode-search-form"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
              className="w-full"
            >
              <form onSubmit={pincodeForm.handleSubmit(onPincodeSubmit)} className="w-full">
                <div className="relative w-full">
                  <div className="relative flex flex-col md:flex-row items-center gap-3 w-full">
                    <div className="relative w-full flex-1 group">
                      <motion.div
                        className="absolute -inset-0.5 rounded-lg bg-gradient-to-r from-[var(--brand-gold)]/20 to-blue-500/20 opacity-0 group-hover:opacity-100 blur transition-opacity duration-300"
                        whileHover={{ opacity: 0.8 }}
                      />

                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 pointer-events-none text-neutral-400 dark:text-neutral-500">
                        <MapPin className="h-5 w-5" />
                      </div>

                      <Input
                        id="pincode"
                        type="tel"
                        inputMode="numeric"
                        maxLength={6}
                        placeholder="Enter pincode to find nearby businesses"
                        {...pincodeForm.register("pincode")}
                        onKeyDown={(e) => {
                          // Allow only numbers, backspace, delete, tab, arrow keys, and enter
                          if (
                            !/^\d$/.test(e.key) && // Allow digits
                            e.key !== 'Backspace' &&
                            e.key !== 'Delete' &&
                            e.key !== 'Tab' &&
                            e.key !== 'Enter' &&
                            !e.key.includes('Arrow')
                          ) {
                            e.preventDefault();
                          }
                        }}
                        className="pl-12 pr-4 py-6 h-14 bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm border-neutral-200/50 dark:border-neutral-800/50 focus-visible:ring-1 focus-visible:ring-[var(--brand-gold)] focus-visible:border-[var(--brand-gold)] text-base rounded-lg w-full shadow-sm relative z-0"
                      />
                    </div>

                    <motion.div
                      className="w-full md:w-auto mt-3 md:ml-4 md:mt-0 px-0"
                    >
                      <Button
                        type="submit"
                        disabled={isLoading || !pincodeForm.getValues("pincode")}
                        className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] font-medium h-12 px-4 md:px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden w-full text-sm"
                      >
                        {/* Shimmer effect */}
                        <motion.div
                          className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent"
                          initial={{ x: "-100%" }}
                          animate={{ x: "100%" }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "linear",
                            repeatDelay: 0.5
                          }}
                        />

                        {isLoading ? (
                          <Loader2 className="h-5 w-5 animate-spin" />
                        ) : (
                          <div className="flex items-center gap-1">
                            <span>Search</span>
                            <ArrowRight className="h-5 w-5" />
                          </div>
                        )}
                      </Button>
                    </motion.div>
                  </div>

                  {pincodeForm.formState.errors.pincode && (
                    <motion.p
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-red-500 dark:text-red-400 mt-2 ml-1"
                    >
                      {pincodeForm.formState.errors.pincode.message}
                    </motion.p>
                  )}
                </div>
              </form>
            </motion.div>
          ) : (
            <motion.div
              key="homepage-city-search-form"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
              className="w-full"
            >
              <form onSubmit={cityForm.handleSubmit(onCitySubmit)} className="w-full">
                <div className="relative w-full">
                  <div className="relative flex flex-col md:flex-row items-center gap-3 w-full">
                    <div className="relative w-full flex-1 group">
                      <motion.div
                        className="absolute -inset-0.5 rounded-lg bg-gradient-to-r from-[var(--brand-gold)]/20 to-blue-500/20 opacity-0 group-hover:opacity-100 blur transition-opacity duration-300"
                        whileHover={{ opacity: 0.8 }}
                      />

                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 pointer-events-none text-neutral-400 dark:text-neutral-500">
                        <Building className="h-5 w-5" />
                      </div>

                      <div className="relative w-full">
                        <Input
                          id="city"
                          placeholder="Enter city name"
                          value={cityQuery}
                          onChange={(e) => {
                            setCityQuery(e.target.value);
                            cityForm.setValue("city", e.target.value, { shouldValidate: true });
                            if (e.target.value.length >= 2) {
                              setIsCityDropdownOpen(true);
                            } else {
                              setIsCityDropdownOpen(false);
                            }
                          }}
                          onFocus={() => {
                            if (cityQuery.length >= 2) {
                              setIsCityDropdownOpen(true);
                            }
                          }}
                          className="pl-12 pr-4 py-6 h-14 bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm border-neutral-200/50 dark:border-neutral-800/50 focus-visible:ring-1 focus-visible:ring-[var(--brand-gold)] focus-visible:border-[var(--brand-gold)] text-base rounded-lg w-full shadow-sm relative z-0"
                          ref={cityInputRef}
                        />

                        {cityQuery && (
                          <button
                            type="button"
                            onClick={() => {
                              setCityQuery("");
                              cityForm.setValue("city", "", { shouldValidate: true });
                              setIsCityDropdownOpen(false);
                            }}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 z-10 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300 transition-colors"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        )}

                        {isCityDropdownOpen && (cityQuery.length >= 2) && (
                          <div className="absolute z-50 w-full mt-1 bg-white/90 dark:bg-neutral-900/90 backdrop-blur-md rounded-lg shadow-lg border border-neutral-200/50 dark:border-neutral-700/50 max-h-60 overflow-auto">
                            <Command>
                              <CommandList>
                                <CommandGroup>
                                  {isLoadingCities ? (
                                    // Skeleton loading UI
                                    <>
                                      {[1, 2, 3, 4, 5].map((index) => (
                                        <div key={index} className="p-3 flex items-center animate-pulse">
                                          <div className="h-4 w-4 rounded-full bg-[var(--brand-gold)]/20 dark:bg-[var(--brand-gold)]/10 mr-2 flex-shrink-0"></div>
                                          <div className="h-4 w-full max-w-[120px] bg-neutral-200 dark:bg-neutral-700 rounded"></div>
                                        </div>
                                      ))}
                                    </>
                                  ) : citySuggestions.length > 0 ? (
                                    // Results
                                    citySuggestions.map((city) => (
                                      <CommandItem
                                        key={city}
                                        onSelect={() => handleCitySelect(city)}
                                        className="cursor-pointer p-3 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
                                      >
                                        <Building className="mr-2 h-4 w-4 text-[var(--brand-gold)]" />
                                        <span>{city}</span>
                                      </CommandItem>
                                    ))
                                  ) : (
                                    // No results
                                    <div className="p-3 text-center text-neutral-500 dark:text-neutral-400">
                                      No cities found
                                    </div>
                                  )}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </div>
                        )}
                      </div>
                    </div>

                    <motion.div
                      className="w-full md:w-auto mt-3 md:ml-4 md:mt-0 px-0"
                    >
                      <Button
                        type="submit"
                        disabled={isLoading || !cityForm.getValues("city") || cityForm.formState.errors.city !== undefined}
                        className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] font-medium h-12 px-4 md:px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden w-full text-sm"
                      >
                        {/* Shimmer effect */}
                        <motion.div
                          className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent"
                          initial={{ x: "-100%" }}
                          animate={{ x: "100%" }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "linear",
                            repeatDelay: 0.5
                          }}
                        />

                        {isLoading ? (
                          <Loader2 className="h-5 w-5 animate-spin" />
                        ) : (
                          <div className="flex items-center gap-1">
                            <span>Search</span>
                            <ArrowRight className="h-5 w-5" />
                          </div>
                        )}
                      </Button>
                    </motion.div>
                  </div>

                  {cityForm.formState.errors.city && (
                    <motion.p
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-red-500 dark:text-red-400 mt-2 ml-1"
                    >
                      {cityForm.formState.errors.city.message}
                    </motion.p>
                  )}
                </div>
              </form>
            </motion.div>
          )}
        </AnimatePresence>

        </div>
        {/* Popular Categories section removed - now in a separate component */}
      </motion.div>
    </div>
  );
}
