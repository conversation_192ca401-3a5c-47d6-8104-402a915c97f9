import React from 'react';
import { Text, View, ScrollView, Switch } from 'react-native';
import { DashboardLayout } from '@/components/shared/layout/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Bell, Heart, MessageCircle, Users, Star, Mail, Smartphone, Settings } from 'lucide-react-native';
import { notificationsStyles as styles } from '@/styles/dashboard/customer/notifications';

interface NotificationItemProps {
  icon: React.ComponentType<any>;
  title: string;
  description: string;
  iconColor: string;
  isComingSoon?: boolean;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  icon: Icon,
  title,
  description,
  iconColor,
  isComingSoon = true,
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View style={[styles.notificationItem, { borderBottomColor: isDark ? '#333' : '#f0f0f0' }]}>
      <View style={styles.notificationContent}>
        <View style={[styles.notificationIcon, { backgroundColor: `${iconColor}20` }]}>
          <Icon size={20} color={iconColor} />
        </View>
        <View style={styles.notificationText}>
          <Text style={[styles.notificationTitle, { color: isDark ? '#fff' : '#000' }]}>
            {title}
          </Text>
          <Text style={[styles.notificationDescription, { color: isDark ? '#999' : '#666' }]}>
            {description}
          </Text>
          {isComingSoon && (
            <Text style={[styles.comingSoonText, { color: '#D4AF37' }]}>
              Coming Soon
            </Text>
          )}
        </View>
      </View>
      <Switch
        value={false}
        disabled={isComingSoon}
        trackColor={{ false: isDark ? '#444' : '#767577', true: '#D4AF37' }}
        thumbColor={isDark ? '#666' : '#f4f3f4'}
        ios_backgroundColor={isDark ? '#444' : '#3e3e3e'}
        style={[styles.switch, isComingSoon && styles.disabledSwitch]}
      />
    </View>
  );
};

export default function NotificationsScreen() {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Get customer name from user
  const customerName = user?.user_metadata?.name || user?.email?.split('@')[0] || 'Valued Customer';

  return (
    <DashboardLayout
      userName={customerName}
      showNotifications={true}
    >
      <ScrollView
        style={[styles.container, { backgroundColor: isDark ? '#000' : '#fff' }]}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={[styles.headerIcon, { backgroundColor: 'rgba(212, 175, 55, 0.1)' }]}>
            <Settings size={32} color="#D4AF37" />
          </View>
          <Text style={[styles.headerTitle, { color: isDark ? '#fff' : '#000' }]}>
            Notification Preferences
          </Text>
          <Text style={[styles.headerSubtitle, { color: isDark ? '#999' : '#666' }]}>
            Choose what notifications you want to receive
          </Text>
        </View>

        {/* Coming Soon Notice */}
        <View style={[styles.comingSoonNotice, { 
          backgroundColor: isDark ? '#1a1a1a' : '#fff8e1',
          borderColor: '#D4AF37'
        }]}>
          <Bell size={24} color="#D4AF37" />
          <View style={styles.comingSoonTextContainer}>
            <Text style={[styles.comingSoonTitle, { color: isDark ? '#D4AF37' : '#D4AF37' }]}>
              Notifications Coming Soon!
            </Text>
            <Text style={[styles.comingSoonDescription, { color: isDark ? '#999' : '#666' }]}>
              We&apos;re working on bringing you personalized notifications. Stay tuned for updates!
            </Text>
          </View>
        </View>

        {/* Activity Notifications Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: isDark ? '#D4AF37' : '#D4AF37' }]}>
            Activity Notifications
          </Text>
          <View style={[styles.sectionCard, { backgroundColor: isDark ? '#1a1a1a' : '#fff' }]}>
            <NotificationItem
              icon={Heart}
              title="Likes"
              description="When someone likes your posts or reviews"
              iconColor="#ff4444"
            />
            <NotificationItem
              icon={MessageCircle}
              title="Comments"
              description="When someone comments on your posts"
              iconColor="#3B82F6"
            />
            <NotificationItem
              icon={Users}
              title="Subscriptions"
              description="Updates from businesses you follow"
              iconColor="#10B981"
            />
            <NotificationItem
              icon={Star}
              title="Reviews"
              description="When your reviews receive responses"
              iconColor="#F59E0B"
            />
          </View>
        </View>

        {/* Business Updates Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: isDark ? '#D4AF37' : '#D4AF37' }]}>
            Business Updates
          </Text>
          <View style={[styles.sectionCard, { backgroundColor: isDark ? '#1a1a1a' : '#fff' }]}>
            <NotificationItem
              icon={Bell}
              title="New Posts"
              description="When businesses you follow post new content"
              iconColor="#8B5CF6"
            />
            <NotificationItem
              icon={Star}
              title="Special Offers"
              description="Exclusive deals and promotions"
              iconColor="#F59E0B"
            />
          </View>
        </View>

        {/* Delivery Methods Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: isDark ? '#D4AF37' : '#D4AF37' }]}>
            Delivery Methods
          </Text>
          <View style={[styles.sectionCard, { backgroundColor: isDark ? '#1a1a1a' : '#fff' }]}>
            <NotificationItem
              icon={Smartphone}
              title="Push Notifications"
              description="Receive notifications on your device"
              iconColor="#D4AF37"
            />
            <NotificationItem
              icon={Mail}
              title="Email Notifications"
              description="Receive notifications via email"
              iconColor="#6B7280"
            />
          </View>
        </View>
      </ScrollView>
    </DashboardLayout>
  );
}


