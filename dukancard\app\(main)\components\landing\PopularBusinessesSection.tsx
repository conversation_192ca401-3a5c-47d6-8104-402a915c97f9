"use client";

import React, { useEffect, useState } from "react";
import <PERSON> from "next/link";
import { Button } from "@/components/ui/button";
import { fetchBusinessesBySearch } from "@/app/(main)/discover/actions/businessActions";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import AnimatedBusinessCard from "@/app/(main)/discover/components/AnimatedBusinessCard"; // Import AnimatedBusinessCard
import { motion } from "framer-motion";
import AnimatedBusinessGridSkeleton from "@/app/(main)/discover/components/AnimatedBusinessGridSkeleton";

export default function PopularBusinessesSection() {
  const [businesses, setBusinesses] = useState<BusinessCardData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getPopularBusinesses = async () => {
      setLoading(true);
      setError(null);
      try {
        const { data, error } = await fetchBusinessesBySearch({
          page: 1,
          limit: 6, // Fetch 6 popular businesses
          sortBy: "likes_desc", // Sort by most liked in descending order
        });

        if (error) {
          setError(error);
        } else if (data) {
          setBusinesses(data.businesses);
        }
      } catch (err) {
        console.error("Failed to fetch popular businesses:", err);
        setError("Failed to load popular businesses.");
      } finally {
        setLoading(false);
      }
    };

    getPopularBusinesses();
  }, []);

  return (
    <section className="py-8 md:py-12">
      <div className="container px-2 sm:px-4 md:px-6 mx-auto">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">Popular Businesses</h2>
            <p className="max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              Explore the most popular businesses on Dukancard.
            </p>
          </div>
        </div>
        {loading ? (
          <div className="mx-auto max-w-7xl py-8">
            <AnimatedBusinessGridSkeleton />
          </div>
        ) : (
          <div className="mx-auto grid max-w-7xl items-start gap-2 sm:gap-3 md:gap-4 py-8 grid-cols-2 md:grid-cols-3 lg:grid-cols-6 justify-items-stretch">
            {error && <p className="text-red-500 col-span-full text-center">{error}</p>}
            {!error && businesses.length === 0 && (
              <p className="col-span-full text-center text-gray-500">No popular businesses found.</p>
            )}
            {businesses.map((business, index) => (
              <motion.div
                key={business.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                className="w-full h-full"
              >
                <AnimatedBusinessCard business={business} index={index} />
              </motion.div>
            ))}
          </div>
        )}
        <div className="flex justify-center mt-8 w-full">
          <Link href="/discover?tab=cards" passHref>
            <Button size="lg">View All Businesses</Button>
          </Link>
        </div>
      </div>
    </section>
  );
}