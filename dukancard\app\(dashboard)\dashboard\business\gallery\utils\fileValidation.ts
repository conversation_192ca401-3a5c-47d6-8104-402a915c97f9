import { toast } from "sonner";

export const validateFile = (file: File): boolean => {
  // Check file size (max 15MB)
  if (file.size > 15 * 1024 * 1024) {
    toast.error("File too large", {
      description: "Please select an image under 15MB",
    });
    return false;
  }

  // Check file type
  const allowedTypes = ["image/jpeg", "image/png", "image/webp", "image/gif"];
  if (!allowedTypes.includes(file.type)) {
    toast.error("Invalid file type", {
      description: "Please select a JPG, PNG, WebP, or GIF image",
    });
    return false;
  }

  return true;
};

export const createPreviewUrl = (file: File): string => {
  return URL.createObjectURL(file);
};

export const revokePreviewUrl = (url: string): void => {
  URL.revokeObjectURL(url);
};

export const getGalleryLimit = (userPlan: string): number => {
  switch (userPlan) {
    case "free":
      return 1;
    case "basic":
      return 3;
    case "growth":
      return 10;
    case "pro":
      return 50;
    case "enterprise":
      return 100;
    default:
      return 1; // Default to free plan limit
  }
};
