import { notFound } from "next/navigation";
import { Metadata } from "next";
import { Suspense } from "react";
import { fetchSinglePost } from "@/lib/actions/posts/fetchSinglePost";
import SinglePostView from "@/components/post/SinglePostView";
import PostCardSkeleton from "@/components/feed/shared/PostCardSkeleton";
import BackNavigation from "@/components/post/BackNavigation";
import ConditionalPostLayout from "@/components/post/ConditionalPostLayout";

// Define TypeScript interfaces for page props and params
interface SinglePostPageProps {
  params: Promise<{
    postId: string;
  }>;
}

// Main page component with server-side rendering
export default async function SinglePostPage({ params }: SinglePostPageProps) {
  const { postId } = await params;

  // Validate postId parameter
  if (!postId || typeof postId !== 'string') {
    notFound();
  }

  // Fetch post data using server action
  const postResult = await fetchSinglePost(postId);

  // Handle post not found or error
  if (!postResult.success || !postResult.data) {
    notFound();
  }

  return (
    <ConditionalPostLayout>
      <BackNavigation />
      <Suspense fallback={<PostCardSkeleton showImage={true} showProducts={true} />}>
        <SinglePostView post={postResult.data} />
      </Suspense>
    </ConditionalPostLayout>
  );
}

// Generate metadata for SEO optimization
export async function generateMetadata({
  params,
}: {
  params: Promise<{ postId: string }>;
}): Promise<Metadata> {
  const { postId } = await params;
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/post/${postId}`;

  // Fetch post data for metadata
  const postResult = await fetchSinglePost(postId);

  if (!postResult.success || !postResult.data) {
    return {
      title: "Post Not Found - Dukancard",
      description: "The requested post could not be found.",
    };
  }

  const post = postResult.data;
  const title = `${post.author_name || 'Post'} - Dukancard`;
  const description = post.content.length > 160 
    ? `${post.content.substring(0, 157)}...` 
    : post.content;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url: pageUrl,
      type: 'article',
      images: post.image_url ? [
        {
          url: post.image_url,
          width: 1200,
          height: 630,
          alt: `Post by ${post.author_name}`,
        }
      ] : [],
      publishedTime: post.created_at,
      authors: post.author_name ? [post.author_name] : [],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: post.image_url ? [post.image_url] : [],
    },
    alternates: {
      canonical: pageUrl,
    },
  };
}

// Configure route segment config for performance
export const dynamic = 'force-dynamic';
export const revalidate = 3600; // Revalidate every hour

// Enable static generation for frequently accessed posts
export const generateStaticParams = async () => {
  // Return empty array to enable ISR for all post IDs
  return [];
};
