"use client";

import { useState, useEffect } from "react";
import { debounce } from "lodash";
import { checkSlugAvailability } from "../../actions";

export function useSlugCheck() {
  const [slugStatus, setSlugStatus] = useState<{
    checking: boolean;
    available: boolean | null;
    message: string | null;
  }>({ checking: false, available: null, message: null });

  // Debounced slug check function
  const debouncedCheckSlug = debounce(async (slug: string) => {
    if (!slug || slug.length < 3) {
      setSlugStatus({
        checking: false,
        available: null,
        message: "Slug must be at least 3 characters."
      });
      return;
    }

    const slugFormatRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
    if (!slugFormatRegex.test(slug)) {
      setSlugStatus({
        checking: false,
        available: false,
        message: "Invalid format (lowercase, numbers, hyphens only)."
      });
      return;
    }

    setSlugStatus({ checking: true, available: null, message: "Checking..." });

    const result = await checkSlugAvailability(slug);

    if (result.error) {
      setSlugStatus({
        checking: false,
        available: false,
        message: result.error
      });
    } else {
      setSlugStatus({
        checking: false,
        available: result.available,
        message: result.available ? "Slug is available!" : "Slug is already taken."
      });
    }
  }, 500);

  // Clean up debounce on unmount
  useEffect(() => {
    return () => {
      debouncedCheckSlug.cancel();
    };
  }, [debouncedCheckSlug]);

  return {
    slugStatus,
    checkSlug: debouncedCheckSlug,
    resetSlugStatus: () => setSlugStatus({ checking: false, available: null, message: null })
  };
}
