"use client";

import { motion } from "framer-motion";
import BusinessCardPreview from "@/app/(dashboard)/dashboard/business/card/components/BusinessCardPreview";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

interface EnhancedCardShowcaseProps {
  cardData: BusinessCardData;
  isDemo?: boolean;
  isAuthenticated?: boolean;
  userPlan?: "free" | "basic" | "growth" | "pro" | "enterprise" | "trial";
}

export default function EnhancedCardShowcase({
  cardData,
  isDemo = false,
  isAuthenticated = false,
  userPlan,
}: EnhancedCardShowcaseProps) {
  return (
    <div className="relative w-full flex justify-center items-center">
      {/* Premium product-like glow effect behind the card */}
      <div className="absolute inset-0 -z-10">
        {/* Main central glow - further reduced intensity */}
        <motion.div
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[90%] h-[90%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/8 rounded-full blur-[50px]"
          animate={{
            opacity: [0.3, 0.4, 0.3],
            scale: [0.95, 1.01, 0.95],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />

        {/* Secondary glow for depth - further reduced intensity */}
        <motion.div
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[70%] h-[70%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-[30px]"
          animate={{
            opacity: [0.2, 0.3, 0.2],
            scale: [1, 1.03, 1],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />

        {/* Ambient corner glows - further reduced intensity */}
        <motion.div
          className="absolute top-0 left-0 w-[40%] h-[40%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/8 rounded-full blur-[40px]"
          animate={{
            opacity: [0.1, 0.2, 0.1],
            x: [-5, 5, -5],
            y: [-5, 5, -5],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-0 right-0 w-[40%] h-[40%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/8 rounded-full blur-[40px]"
          animate={{
            opacity: [0.1, 0.2, 0.1],
            x: [5, -5, 5],
            y: [5, -5, 5],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />

        {/* Product spotlight effect - reduced intensity */}
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[40%] bg-white/5 dark:bg-white/3 blur-[60px] opacity-40"></div>
      </div>

      {/* Card container - centered with fixed width */}
      <motion.div
        className="w-full max-w-sm mx-auto"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <BusinessCardPreview
          data={cardData}
          userPlan={userPlan || "basic"}
          isAuthenticated={isAuthenticated}
          totalLikes={isDemo ? 50251 : (cardData.total_likes ?? 0)}
          totalSubscriptions={isDemo ? 32053 : (cardData.total_subscriptions ?? 0)}
          averageRating={isDemo ? 4.8 : (cardData.average_rating ?? 0)}
          isDemo={isDemo}
        />
      </motion.div>
    </div>
  );
}
