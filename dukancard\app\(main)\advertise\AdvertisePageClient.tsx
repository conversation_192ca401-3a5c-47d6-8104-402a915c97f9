"use client";

import { useEffect } from "react";
import HeroSec<PERSON> from "./components/HeroSection";
import BenefitsSection from "./components/BenefitsSection";
import ContactSection from "./components/ContactSection";
import FAQSection from "./components/FAQSection";

export default function AdvertisePageClient() {
  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-white dark:bg-black">
      {/* Hero Section */}
      <HeroSection />

      {/* Benefits Section */}
      <BenefitsSection />

      {/* Contact Section */}
      <ContactSection />

      {/* FAQ Section */}
      <FAQSection />
    </div>
  );
}
