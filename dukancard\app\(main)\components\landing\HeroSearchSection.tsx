"use client";

import { motion, AnimatePresence } from "framer-motion";
import React, { useState, useEffect, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  pincodeSchema,
  citySchema,
  LocationSearchFormData,
  CitySearchFormData,
} from "@/lib/schemas/locationSchemas";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MapPin, Loader2, Search, Building2, ShoppingBag, Map, Building } from "lucide-react";
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList
} from "@/components/ui/command";
import { getCitySuggestionsClient } from "@/lib/client/locationUtils";

interface HeroSearchSectionProps {
  minimal?: boolean;
  hideTitle?: boolean;
}

export default function HeroSearchSection({ minimal = false, hideTitle = false }: HeroSearchSectionProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [metricIndex, setMetricIndex] = useState(0);
  const [searchType, setSearchType] = useState<"pincode" | "city">("pincode");
  const [citySuggestions, setCitySuggestions] = useState<string[]>([]);
  const [cityQuery, setCityQuery] = useState("");
  const [_selectedCity, setSelectedCity] = useState("");
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const cityInputRef = useRef<HTMLInputElement>(null);

  // Metrics data for the flipping animation
  const metrics = [
    { value: "2.8 Lakh+", label: "Businesses", icon: Building2 },
    { value: "3.5 Lakh+", label: "Products & Services", icon: ShoppingBag },
    { value: "450+", label: "Cities", icon: Map },
  ];

  // Effect to cycle through metrics every 3 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setMetricIndex((prevIndex) => (prevIndex + 1) % metrics.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [metrics.length]);

  // Pincode search form using the existing schema
  const pincodeForm = useForm<LocationSearchFormData>({
    resolver: zodResolver(pincodeSchema),
    defaultValues: {
      pincode: "",
      locality: null,
    },
    mode: "onChange",
  });

  // City search form using the city schema
  const cityForm = useForm<CitySearchFormData>({
    resolver: zodResolver(citySchema),
    defaultValues: {
      city: "",
      locality: null,
    },
    mode: "onChange",
  });

  // Fetch city suggestions when user types
  useEffect(() => {
    const fetchCitySuggestions = async () => {
      if (cityQuery.length < 2) {
        setCitySuggestions([]);
        return;
      }

      try {
        const result = await getCitySuggestionsClient(cityQuery);

        if (result.cities) {
          // Log the city suggestions for debugging
          console.log("HeroSearchSection - City suggestions:", result.cities);
          setCitySuggestions(result.cities);
        } else if (result.error) {
          console.error("Error fetching city suggestions:", result.error);
        }
      } catch (error) {
        console.error("Error fetching city suggestions:", error);
      }
    };

    fetchCitySuggestions();
  }, [cityQuery]);

  // Handle pincode form submission
  const onPincodeSubmit = (data: LocationSearchFormData) => {
    setIsLoading(true);

    // Use a slight delay to ensure the loading state is visible
    setTimeout(() => {
      try {
        // Create the URL with search parameters
        const params = new URLSearchParams();
        params.set("pincode", data.pincode);

        // Add locality if present
        if (data.locality) {
          params.set("locality", data.locality);
        }

        const url = `/discover?${params.toString()}`;

        // Use window.location for a full page navigation
        window.location.href = url;
      } catch (error) {
        console.error("Navigation failed:", error);
        setIsLoading(false);
      }
    }, 500); // Small delay for better UX
  };

  // Handle city form submission
  const onCitySubmit = (data: CitySearchFormData) => {
    setIsLoading(true);

    // Use a slight delay to ensure the loading state is visible
    setTimeout(() => {
      try {
        // Create the URL with search parameters
        const params = new URLSearchParams();
        params.set("city", data.city);

        // Add locality if present
        if (data.locality) {
          params.set("locality", data.locality);
        }

        const url = `/discover?${params.toString()}`;

        // Use window.location for a full page navigation
        window.location.href = url;
      } catch (error) {
        console.error("Navigation failed:", error);
        setIsLoading(false);
      }
    }, 500); // Small delay for better UX
  };

  // Handle city selection from dropdown
  const handleCitySelect = (city: string) => {
    setSelectedCity(city);
    cityForm.setValue("city", city, { shouldValidate: true });
    setCityQuery(city);
    setIsCityDropdownOpen(false);
  };

  return (
    <div className={minimal ? "" : "relative w-full pt-28 pb-16 md:pt-36 md:pb-24 overflow-hidden"}>
      {!minimal && (
        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Main centered glow */}
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[500px] h-[300px] bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-full blur-[100px] opacity-50"></div>

          {/* Subtle pattern overlay */}
          <div className="absolute inset-0 bg-[url('/decorative/subtle-pattern.svg')] bg-repeat opacity-5 dark:opacity-10"></div>
        </div>
      )}

      <div className={minimal ? "" : "container mx-auto px-4 max-w-6xl"}>
        {!minimal && (
          <div className="text-center mb-8">
            <motion.div
              className="h-[120px] md:h-[140px] flex flex-col items-center justify-center"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
            >
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight text-center">
                <span className="inline-block">Search across</span>{" "}
                <span className="text-[var(--brand-gold)] relative inline-block">
                  <AnimatePresence mode="wait">
                    <motion.span
                      key={metricIndex}
                      className="relative z-10 inline-block min-w-[120px] md:min-w-[150px] text-center"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.5 }}
                    >
                      {metrics[metricIndex].value}
                    </motion.span>
                  </AnimatePresence>
                  <motion.div
                    className="absolute -bottom-1 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-[var(--brand-gold)] to-transparent"
                    initial={{ width: "0%", left: "50%" }}
                    animate={{ width: "100%", left: "0%" }}
                    transition={{ duration: 1, delay: 0.5 }}
                  />
                </span>
              </h1>
              <div className="h-10 flex items-center justify-center">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={metricIndex}
                    className="flex items-center gap-2"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.5 }}
                  >
                    {React.createElement(metrics[metricIndex].icon, {
                      className: "w-5 h-5 text-[var(--brand-gold)]"
                    })}
                    <span className="text-xl font-semibold">
                      {metrics[metricIndex].label}
                    </span>
                  </motion.div>
                </AnimatePresence>
              </div>
            </motion.div>
          </div>
        )}

        {minimal && !hideTitle && (
          <div className="mb-6">
            <h2 className="text-2xl md:text-3xl font-bold mb-2">
              Find <span className="text-[var(--brand-gold)]">Businesses</span> Near You
            </h2>
            <p className="text-neutral-600 dark:text-neutral-400 mb-4">
              Search across 2.8 Lakh+ businesses and services
            </p>
          </div>
        )}

        <motion.div
          className={`${minimal ? "w-full" : "max-w-3xl mx-auto"}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: minimal ? 0.2 : 0.5 }}
        >
          <div className="bg-white dark:bg-neutral-900 backdrop-blur-md rounded-xl shadow-md border border-neutral-200/50 dark:border-neutral-800/50 p-5 md:p-6">
            <Tabs
              defaultValue="pincode"
              value={searchType}
              onValueChange={(value) => setSearchType(value as "pincode" | "city")}
              className="w-full"
            >
              <TabsList className="grid grid-cols-2 mb-5 bg-neutral-100 dark:bg-neutral-800 p-1 rounded-lg">
                <TabsTrigger
                  value="pincode"
                  className="text-sm md:text-base font-medium data-[state=active]:bg-white dark:data-[state=active]:bg-neutral-700 data-[state=active]:text-[var(--brand-gold)] transition-all duration-200"
                >
                  <MapPin className="h-4 w-4 mr-2" />
                  Search by Pincode
                </TabsTrigger>
                <TabsTrigger
                  value="city"
                  className="text-sm md:text-base font-medium data-[state=active]:bg-white dark:data-[state=active]:bg-neutral-700 data-[state=active]:text-[var(--brand-gold)] transition-all duration-200"
                >
                  <Building className="h-4 w-4 mr-2" />
                  Search by City
                </TabsTrigger>
              </TabsList>

              <TabsContent value="pincode" className="mt-0">
                <form onSubmit={pincodeForm.handleSubmit(onPincodeSubmit)}>
                  <div className="flex flex-col md:flex-row gap-3">
                    <div className="relative flex-1">
                      {/* Search icon */}
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                        <MapPin className="h-4 w-4 text-[var(--brand-gold)]" />
                      </div>

                      <Input
                        id="pincode"
                        type="tel"
                        inputMode="numeric"
                        maxLength={6}
                        placeholder="Enter pincode to find nearby businesses"
                        {...pincodeForm.register("pincode")}
                        onKeyDown={(e) => {
                          // Allow only numbers, backspace, delete, tab, arrow keys, and enter
                          if (
                            !/^\d$/.test(e.key) && // Allow digits
                            e.key !== 'Backspace' &&
                            e.key !== 'Delete' &&
                            e.key !== 'Tab' &&
                            e.key !== 'Enter' &&
                            !e.key.includes('Arrow')
                          ) {
                            e.preventDefault();
                          }
                        }}
                        className="pl-10 py-5 bg-white dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 focus-visible:ring-1 focus-visible:ring-[var(--brand-gold)] focus-visible:border-[var(--brand-gold)] text-base rounded-lg w-full"
                      />
                    </div>

                    <Button
                      type="submit"
                      disabled={isLoading || !pincodeForm.getValues("pincode")}
                      className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] font-medium py-5 px-6 rounded-lg shadow-sm hover:shadow transition-all duration-200 flex items-center justify-center text-base"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          <span>Searching...</span>
                        </>
                      ) : (
                        <>
                          <Search className="mr-2 h-4 w-4" />
                          <span>Search</span>
                        </>
                      )}
                    </Button>
                  </div>

                  {pincodeForm.formState.errors.pincode && (
                    <motion.p
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-red-500 dark:text-red-400 mt-2"
                    >
                      {pincodeForm.formState.errors.pincode.message}
                    </motion.p>
                  )}
                </form>
              </TabsContent>

              <TabsContent value="city" className="mt-0">
                <form onSubmit={cityForm.handleSubmit(onCitySubmit)}>
                  <div className="flex flex-col md:flex-row gap-3">
                    <div className="relative flex-1">
                      {/* Search icon */}
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
                        <Building className="h-4 w-4 text-[var(--brand-gold)]" />
                      </div>

                      <div className="relative">
                        <Input
                          id="city"
                          placeholder="Enter city name"
                          value={cityQuery}
                          onChange={(e) => {
                            setCityQuery(e.target.value);
                            cityForm.setValue("city", e.target.value, { shouldValidate: true });
                            if (e.target.value.length >= 2) {
                              setIsCityDropdownOpen(true);
                            } else {
                              setIsCityDropdownOpen(false);
                            }
                          }}
                          onFocus={() => {
                            if (cityQuery.length >= 2) {
                              setIsCityDropdownOpen(true);
                            }
                          }}
                          className="pl-10 py-5 bg-white dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 focus-visible:ring-1 focus-visible:ring-[var(--brand-gold)] focus-visible:border-[var(--brand-gold)] text-base rounded-lg w-full"
                          ref={cityInputRef}
                        />

                        {isCityDropdownOpen && citySuggestions.length > 0 && (
                          <div className="absolute z-50 w-full mt-1 bg-white dark:bg-neutral-800 rounded-lg shadow-md border border-neutral-200 dark:border-neutral-700 max-h-60 overflow-auto">
                            <Command>
                              <CommandList>
                                <CommandGroup>
                                  {citySuggestions.map((city) => (
                                    <CommandItem
                                      key={city}
                                      onSelect={() => handleCitySelect(city)}
                                      className="cursor-pointer p-2 hover:bg-neutral-100 dark:hover:bg-neutral-700"
                                    >
                                      <Building className="mr-2 h-4 w-4" />
                                      <span>{city}</span>
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </div>
                        )}
                      </div>
                    </div>

                    <Button
                      type="submit"
                      disabled={isLoading || !cityForm.getValues("city") || cityForm.formState.errors.city !== undefined}
                      className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] font-medium py-5 px-6 rounded-lg shadow-sm hover:shadow transition-all duration-200 flex items-center justify-center text-base"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          <span>Searching...</span>
                        </>
                      ) : (
                        <>
                          <Search className="mr-2 h-4 w-4" />
                          <span>Search</span>
                        </>
                      )}
                    </Button>
                  </div>

                  {cityForm.formState.errors.city && (
                    <motion.p
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-red-500 dark:text-red-400 mt-2"
                    >
                      {cityForm.formState.errors.city.message}
                    </motion.p>
                  )}
                </form>
              </TabsContent>
            </Tabs>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
