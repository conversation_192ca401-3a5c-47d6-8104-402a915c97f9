"use client";

import { useTheme } from "next-themes";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";

interface PolicyBackgroundProps {
  variant?: "default" | "gold" | "blue" | "purple";
  intensity?: "low" | "medium" | "high";
}

export default function PolicyBackground({
  variant = "default",
  intensity = "low",
}: PolicyBackgroundProps) {
  const { resolvedTheme } = useTheme();
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    setIsDark(resolvedTheme === "dark");
  }, [resolvedTheme]);

  // Define colors based on variant
  const getGradientColors = () => {
    switch (variant) {
      case "gold":
        return {
          primary: isDark
            ? "rgba(var(--brand-gold-rgb), 0.15)"
            : "rgba(var(--brand-gold-rgb), 0.08)",
          secondary: "transparent",
        };
      case "blue":
        return {
          primary: isDark ? "rgba(59, 130, 246, 0.15)" : "rgba(59, 130, 246, 0.08)",
          secondary: "transparent",
        };
      case "purple":
        return {
          primary: isDark
            ? "rgba(139, 92, 246, 0.15)"
            : "rgba(139, 92, 246, 0.08)",
          secondary: "transparent",
        };
      default:
        return {
          primary: isDark ? "rgba(63, 63, 70, 0.3)" : "rgba(244, 244, 245, 0.7)",
          secondary: "transparent",
        };
    }
  };

  // Adjust opacity based on intensity
  const getOpacity = () => {
    switch (intensity) {
      case "high":
        return isDark ? 1 : 0.7;
      case "medium":
        return isDark ? 0.7 : 0.5;
      case "low":
      default:
        return isDark ? 0.5 : 0.3;
    }
  };

  const { primary, secondary } = getGradientColors();
  const opacity = getOpacity();

  return (
    <div className="absolute inset-0 -z-10 overflow-hidden">
      {/* Main gradient */}
      <motion.div
        className="absolute inset-0"
        initial={{ opacity: 0 }}
        animate={{ opacity }}
        transition={{ duration: 1 }}
        style={{
          background: `radial-gradient(circle at 50% 30%, ${primary}, ${secondary})`,
        }}
      />

      {/* Subtle pattern overlay */}
      <div className="absolute inset-0 bg-[url('/decorative/subtle-pattern.svg')] bg-repeat opacity-5 dark:opacity-10" />

      {/* Floating elements for visual interest */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full"
        style={{
          background: variant === "gold" 
            ? "radial-gradient(circle, rgba(var(--brand-gold-rgb), 0.1), transparent 70%)" 
            : variant === "blue"
            ? "radial-gradient(circle, rgba(59, 130, 246, 0.1), transparent 70%)"
            : variant === "purple"
            ? "radial-gradient(circle, rgba(139, 92, 246, 0.1), transparent 70%)"
            : "radial-gradient(circle, rgba(63, 63, 70, 0.1), transparent 70%)",
        }}
        animate={{
          x: [0, 10, 0],
          y: [0, 15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
        }}
      />

      <motion.div
        className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full"
        style={{
          background: variant === "gold" 
            ? "radial-gradient(circle, rgba(var(--brand-gold-rgb), 0.05), transparent 70%)" 
            : variant === "blue"
            ? "radial-gradient(circle, rgba(59, 130, 246, 0.05), transparent 70%)"
            : variant === "purple"
            ? "radial-gradient(circle, rgba(139, 92, 246, 0.05), transparent 70%)"
            : "radial-gradient(circle, rgba(63, 63, 70, 0.05), transparent 70%)",
        }}
        animate={{
          x: [0, -15, 0],
          y: [0, 10, 0],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          repeatType: "reverse",
        }}
      />
    </div>
  );
}
