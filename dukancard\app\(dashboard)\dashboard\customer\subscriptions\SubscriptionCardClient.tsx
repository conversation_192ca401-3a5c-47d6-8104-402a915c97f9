'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { UserMinus, Loader2, ExternalLink } from 'lucide-react';
import { unsubscribeFromBusiness } from '@/lib/actions/interactions'; // Import server action
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface BusinessProfileData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface SubscriptionCardClientProps {
  subscriptionId: string;
  profile: BusinessProfileData;
  onUnsubscribeSuccess: (_subscriptionId: string) => void; // Callback to remove item from list
}

export default function SubscriptionCardClient({
  subscriptionId,
  profile,
  onUnsubscribeSuccess,
}: SubscriptionCardClientProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Initialize component state

  const handleUnsubscribe = async () => {
    setIsLoading(true);
    try {
      // Make sure we're using the correct business profile ID
      if (!profile.id) {
        toast.error("Cannot unsubscribe: Missing business ID");
        return;
      }

      const result = await unsubscribeFromBusiness(profile.id);

      if (result.success) {
        toast.success(`Unsubscribed from ${profile.business_name || 'business'}.`);
        onUnsubscribeSuccess(subscriptionId); // Notify parent to remove this card
      } else {
        toast.error(`Failed to unsubscribe: ${result.error || 'Unknown error'}`);
      }
    } catch (_error) {
      toast.error("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  // Format the full address with pincode and address line
  const addressParts = [
    profile.address_line,
    profile.city,
    profile.state,
    profile.pincode ? `PIN: ${profile.pincode}` : null
  ].filter(Boolean);

  const location = addressParts.join(', ');

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "rounded-lg border p-0 overflow-hidden transition-all duration-300",
        "bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",
        isHovered ? "shadow-md transform -translate-y-1" : "shadow-sm"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Card with header image background */}
      <div className="relative">
        {/* Decorative header */}
        <div
          className="h-20 w-full bg-gradient-to-r from-blue-500/20 to-[var(--brand-gold)]/20 dark:from-blue-900/30 dark:to-[var(--brand-gold)]/30"
        >
          {/* Decorative pattern overlay */}
          <div
            className="absolute inset-0 opacity-10 dark:opacity-20 bg-repeat"
            style={{
              backgroundImage: `url("/decorative/card-texture.svg")`,
              backgroundSize: "200px",
            }}
          />
        </div>

        {/* Avatar - positioned to overlap the header */}
        <div className="absolute -bottom-6 left-4">
          <div className="p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800">
            <Avatar className="h-16 w-16 border border-[var(--brand-gold)]/30 shadow-sm">
              {profile.logo_url ? (
                <AvatarImage src={profile.logo_url} alt={profile.business_name ?? 'Logo'} />
              ) : null}
              <AvatarFallback className="bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 text-blue-600 dark:text-blue-300 text-xl font-semibold border border-[var(--brand-gold)]/30">
                {profile.business_name?.charAt(0).toUpperCase() ?? 'B'}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>

      {/* Card content */}
      <div className="pt-8 px-4 pb-4">
        <div className="flex flex-col">
          <div className="mb-3">
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-200 group flex items-center gap-1">
              <Link
                href={`/${profile.business_slug}`}
                className="hover:text-[var(--brand-gold)] transition-colors inline-flex items-center gap-1"
                target="_blank"
              >
                {profile.business_name}
                <ExternalLink className="h-3.5 w-3.5 opacity-70" />
              </Link>
            </h3>
            {location && (
              <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-1 flex items-center">
                <span className="inline-block h-1 w-1 rounded-full bg-neutral-300 dark:bg-neutral-700 mr-2"></span>
                {location}
              </p>
            )}
          </div>

          {/* Action button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleUnsubscribe}
            disabled={isLoading}
            className={cn(
              "mt-2 w-full border-neutral-200 dark:border-neutral-700 transition-all duration-200",
              "hover:bg-red-50 hover:text-red-600 hover:border-red-200",
              "dark:hover:bg-red-950/30 dark:hover:text-red-400 dark:hover:border-red-900/50"
            )}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <UserMinus className="mr-2 h-4 w-4" />
            )}
            Unsubscribe
          </Button>
        </div>
      </div>
    </motion.div>
  );
}
