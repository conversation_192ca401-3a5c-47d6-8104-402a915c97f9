'use server';

import { createClient } from '@/utils/supabase/server';
import { createAdminClient } from '@/utils/supabase/admin';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { IndianMobileSchema } from '@/lib/schemas/authSchemas';

// Define the schema for profile updates
const ProfileSchema = z.object({
  name: z.string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),
  // Add other fields here if needed in the future
});

// Define the schema for phone updates
const PhoneSchema = z.object({
  phone: IndianMobileSchema,
});

// Define the schema for address updates with proper validation
const AddressSchema = z.object({
  address: z
    .string()
    .max(100, { message: "Address cannot exceed 100 characters." })
    .optional()
    .or(z.literal("")),
  pincode: z
    .string()
    .min(1, { message: "Pincode is required" })
    .regex(/^\d{6}$/, { message: "Must be a valid 6-digit pincode" }),
  city: z
    .string()
    .min(1, { message: "City is required" })
    .max(50, { message: "City cannot exceed 50 characters." })
    .refine((val) => val.trim().length > 0, { message: "City cannot be empty" }),
  state: z
    .string()
    .min(1, { message: "State is required" })
    .max(50, { message: "State cannot exceed 50 characters." })
    .refine((val) => val.trim().length > 0, { message: "State cannot be empty" }),
  locality: z
    .string()
    .min(1, { message: "Locality is required" })
    .refine((val) => val.trim().length > 0, { message: "Locality cannot be empty" }),
});

// Define the schema for email updates
const EmailSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
});

// Define the schema for mobile updates
const MobileSchema = z.object({
  mobile: IndianMobileSchema,
});

export type ProfileFormState = {
  message: string | null;
  errors?: {
    name?: string[];
    // Add other fields here
  };
  success: boolean;
};

export type PhoneFormState = {
  message: string | null;
  errors?: {
    phone?: string[];
  };
  success: boolean;
};

export type AddressFormState = {
  message: string | null;
  errors?: {
    address?: string[];
    pincode?: string[];
    city?: string[];
    state?: string[];
    locality?: string[];
  };
  success: boolean;
};

export type EmailFormState = {
  message: string | null;
  errors?: {
    email?: string[];
  };
  success: boolean;
};

export type MobileFormState = {
  message: string | null;
  errors?: {
    mobile?: string[];
  };
  success: boolean;
};

export async function updateCustomerProfile(
  prevState: ProfileFormState,
  formData: FormData
): Promise<ProfileFormState> {
  const supabase = await createClient(); // Added await

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const validatedFields = ProfileSchema.safeParse({
    name: formData.get('name'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid data provided.',
      errors: validatedFields.error.flatten().fieldErrors,
      success: false,
    };
  }

  const { name } = validatedFields.data;

  try {
    // Update name in auth.users table (full_name in user_metadata)
    // The database trigger will automatically sync this to customer_profiles table
    const { error: authUpdateError } = await supabase.auth.updateUser({
      data: { full_name: name }
    });

    if (authUpdateError) {
      console.error('Error updating auth user metadata:', authUpdateError);
      return { message: `Auth Error: ${authUpdateError.message}`, success: false };
    }

    // Revalidate the profile page and potentially the layout to reflect name change
    revalidatePath('/dashboard/customer/profile');
    revalidatePath('/dashboard/customer/layout'); // To update sidebar/header name

    return { message: 'Profile updated successfully!', success: true };
  } catch (error) {
    console.error('Unexpected error updating profile:', error);
    return { message: 'An unexpected error occurred.', success: false };
  }
}

export async function updateCustomerAddress(
  prevState: AddressFormState,
  formData: FormData
): Promise<AddressFormState> {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const validatedFields = AddressSchema.safeParse({
    address: formData.get('address'),
    pincode: formData.get('pincode'),
    city: formData.get('city'),
    state: formData.get('state'),
    locality: formData.get('locality'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid data provided.',
      errors: validatedFields.error.flatten().fieldErrors,
      success: false,
    };
  }

  const { address, pincode, city, state, locality } = validatedFields.data;

  try {
    const { error: updateError } = await supabase
      .from('customer_profiles')
      .update({
        address: address || null,
        pincode,
        city,
        state,
        locality
      })
      .eq('id', user.id);

    if (updateError) {
      console.error('Error updating customer address:', updateError);
      return { message: `Database Error: ${updateError.message}`, success: false };
    }

    // Revalidate relevant pages
    revalidatePath('/dashboard/customer/profile');
    revalidatePath('/dashboard/customer');

    return { message: 'Address updated successfully!', success: true };
  } catch (error) {
    console.error('Unexpected error updating address:', error);
    return { message: 'An unexpected error occurred.', success: false };
  }
}

export async function updateCustomerPhone(
  prevState: PhoneFormState,
  formData: FormData
): Promise<PhoneFormState> {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const validatedFields = PhoneSchema.safeParse({
    phone: formData.get('phone'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid data provided.',
      errors: validatedFields.error.flatten().fieldErrors,
      success: false,
    };
  }

  const { phone } = validatedFields.data;

  try {
    // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number

    // Update phone in customer_profiles table
    const { error: updateError } = await supabase
      .from('customer_profiles')
      .update({ phone: phone })
      .eq('id', user.id);

    if (updateError) {
      console.error('Error updating customer phone:', updateError);
      return { message: `Database Error: ${updateError.message}`, success: false };
    }

    // Update phone in Supabase auth.users table to maintain user ID consistency
    const { error: authUpdateError } = await supabase.auth.updateUser({
      phone: `+91${phone}`,
    });

    if (authUpdateError) {
      console.warn('Failed to update auth phone field:', authUpdateError.message);
      // Don't fail the operation for this, just log the warning
      // The customer_profiles table is updated successfully
    }

    // Revalidate relevant pages
    revalidatePath('/dashboard/customer/profile');
    revalidatePath('/dashboard/customer');

    return { message: 'Phone number updated successfully!', success: true };
  } catch (error) {
    console.error('Unexpected error updating phone:', error);
    return { message: 'An unexpected error occurred.', success: false };
  }
}

export async function updateCustomerEmail(
  prevState: EmailFormState,
  formData: FormData
): Promise<EmailFormState> {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const validatedFields = EmailSchema.safeParse({
    email: formData.get('email'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid data provided.',
      errors: validatedFields.error.flatten().fieldErrors,
      success: false,
    };
  }

  const { email } = validatedFields.data;

  try {
    // Check if user registered with Google OAuth (matching settings page logic)
    const isGoogleLogin = user.app_metadata?.provider === 'google';

    // Check if the user has email/password authentication
    let hasEmailAuth = false;

    if (isGoogleLogin && user.email) {
      try {
        // Use admin client to check user identities
        const adminClient = createAdminClient();
        const { data: authData } = await adminClient.auth.admin.getUserById(user.id);

        // Check if the user has email/password authentication
        if (authData?.user?.identities) {
          hasEmailAuth = authData.user.identities.some(
            (identity) => identity.provider === "email"
          );
        }
      } catch (error) {
        console.error("Error checking user auth methods:", error);
        hasEmailAuth = false;
      }
    }

    // Only disable email changes if they're using Google and don't have email auth
    const shouldDisableEmailChange = isGoogleLogin && !hasEmailAuth;

    if (shouldDisableEmailChange) {
      return {
        message: 'Email cannot be changed for Google accounts. Your email is linked to your Google account.',
        success: false,
      };
    }

    // Check if email is the same as current
    if (user.email === email) {
      return {
        message: 'Email address is the same as current.',
        success: false,
      };
    }

    // Update email in Supabase auth.users table
    // This is the primary source of truth for email
    const { error: authUpdateError } = await supabase.auth.updateUser({
      email: email,
    });

    if (authUpdateError) {
      console.error('Error updating auth email:', authUpdateError);

      // Provide user-friendly error messages
      let errorMessage = 'Failed to update email address.';
      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {
        errorMessage = 'This email address is already in use by another account.';
      } else if (authUpdateError.message.includes('check constraint')) {
        errorMessage = 'Invalid email format provided.';
      } else if (authUpdateError.message.includes('rate limit')) {
        errorMessage = 'Too many requests. Please try again later.';
      }

      return {
        message: errorMessage,
        success: false
      };
    }

    // Note: customer_profiles table will be automatically updated via database trigger

    // Revalidate relevant pages
    revalidatePath('/dashboard/customer/profile');
    revalidatePath('/dashboard/customer');

    return {
      message: 'Email address updated successfully! You may need to verify the new email address.',
      success: true
    };
  } catch (error) {
    console.error('Unexpected error updating email:', error);
    return { message: 'An unexpected error occurred.', success: false };
  }
}

export async function updateCustomerMobile(
  prevState: MobileFormState,
  formData: FormData
): Promise<MobileFormState> {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const validatedFields = MobileSchema.safeParse({
    mobile: formData.get('mobile'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid data provided.',
      errors: validatedFields.error.flatten().fieldErrors,
      success: false,
    };
  }

  const { mobile } = validatedFields.data;

  try {
    // Check if mobile is the same as current
    const currentMobile = user.phone ? user.phone.replace(/^\+91/, '') : '';
    if (mobile === currentMobile) {
      return {
        message: 'Mobile number is the same as current.',
        success: false,
      };
    }

    // Note: Mobile uniqueness check removed as multiple businesses/customers can share the same number

    // Update mobile in Supabase auth.users table
    // This is the primary source of truth for mobile
    const { error: authUpdateError } = await supabase.auth.updateUser({
      phone: `+91${mobile}`,
    });

    if (authUpdateError) {
      console.error('Error updating auth mobile:', authUpdateError);

      // Provide user-friendly error messages
      let errorMessage = 'Failed to update mobile number.';
      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {
        errorMessage = 'This mobile number is already in use by another account.';
      } else if (authUpdateError.message.includes('check constraint')) {
        errorMessage = 'Invalid mobile number format provided.';
      } else if (authUpdateError.message.includes('rate limit')) {
        errorMessage = 'Too many requests. Please try again later.';
      }

      return {
        message: errorMessage,
        success: false
      };
    }

    // Note: customer_profiles table will be automatically updated via database trigger

    // Revalidate relevant pages
    revalidatePath('/dashboard/customer/profile');
    revalidatePath('/dashboard/customer');

    return {
      message: 'Mobile number updated successfully!',
      success: true
    };
  } catch (error) {
    console.error('Unexpected error updating mobile:', error);
    return { message: 'An unexpected error occurred.', success: false };
  }
}
