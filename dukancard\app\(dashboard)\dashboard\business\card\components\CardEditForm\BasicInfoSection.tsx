"use client";

import { UseFormReturn } from "react-hook-form";
import { BusinessCardData } from "../../schema";
import { User, Building2, MessageSquare, Upload, Info, Briefcase, Mail, Tag, Loader2, Trash2, Calendar } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { CategoryCombobox } from "@/components/ui/category-combobox";
import Image from "next/image";

interface BasicInfoSectionProps {
  form: UseFormReturn<BusinessCardData>;
  onFileSelect: (_file: File | null) => void;
  isLogoUploading?: boolean;
  onLogoDelete?: () => void;
}

export default function BasicInfoSection({ form, onFileSelect, isLogoUploading = false, onLogoDelete }: BasicInfoSectionProps) {
  const bioLength = form.watch("about_bio")?.length || 0;

  return (
    <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
          <User className="w-4 sm:w-5 h-4 sm:h-5" />
        </div>
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
            Basic Information
          </h3>
          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
            Let&apos;s start with the essential details for your business card
          </p>
        </div>
      </div>

      <div className="flex flex-col gap-4 sm:gap-6">
        {/* Name and Title Fields - 2 columns on tablet and desktop */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          {/* Name Field */}
          <FormField
            control={form.control}
            name="member_name"
            render={({ field }) => (
              <FormItem className="space-y-1 sm:space-y-2">
                <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                  <User className="h-3.5 w-3.5 text-primary" />
                  Your Name
                  <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="e.g., Rajesh Mahapatra"
                      {...field}
                      className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                      maxLength={50}
                    />
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500">
                      {field.value?.length || 0}/50
                    </div>
                  </div>
                </FormControl>
                <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                  Your full name as it will appear on the card
                </FormDescription>
                <FormMessage className="text-xs text-red-500" />
              </FormItem>
            )}
          />

          {/* Title Field */}
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem className="space-y-1 sm:space-y-2">
                <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                  <Briefcase className="h-3.5 w-3.5 text-primary" />
                  Your Title/Designation
                  <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="e.g., Owner, Manager, Developer"
                      {...field}
                      className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                      maxLength={50}
                    />
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500">
                      {field.value?.length || 0}/50
                    </div>
                  </div>
                </FormControl>
                <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                  Your position or role within the business
                </FormDescription>
                <FormMessage className="text-xs text-red-500" />
              </FormItem>
            )}
          />
        </div>

        {/* Business Name Field - Full Width */}
        <FormField
          control={form.control}
          name="business_name"
          render={({ field }) => (
            <FormItem className="space-y-1 sm:space-y-2">
              <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <Building2 className="h-3.5 w-3.5 text-primary" />
                Business Name
                <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    placeholder="e.g., Mahapatra Kirana & General Store"
                    {...field}
                    className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                    maxLength={100}
                  />
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500">
                    {field.value?.length || 0}/100
                  </div>
                </div>
              </FormControl>
              <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                The name of your business or organization
              </FormDescription>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />

        {/* Business Category Field - Full Width */}
        <FormField
          control={form.control}
          name="business_category"
          render={({ field }) => (
            <FormItem className="space-y-1 sm:space-y-2">
              <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <Tag className="h-3.5 w-3.5 text-primary" />
                Business Category
                <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <CategoryCombobox
                  value={field.value || ""}
                  onChange={field.onChange}
                  placeholder="Select a business category"
                  className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                />
              </FormControl>
              <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                The category that best describes your business
              </FormDescription>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />

        {/* Established Year Field - Full Width */}
        <FormField
          control={form.control}
          name="established_year"
          render={({ field }) => (
            <FormItem className="space-y-1 sm:space-y-2">
              <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <Calendar className="h-3.5 w-3.5 text-primary" />
                Established Year
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    placeholder="e.g., 2015"
                    {...field}
                    type="number"
                    min="1800"
                    max={new Date().getFullYear()}
                    value={field.value || ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value === "" ? null : parseInt(value, 10));
                    }}
                    className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                  />
                </div>
              </FormControl>
              <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                The year your business was established (will be displayed on your card)
              </FormDescription>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />

        {/* Contact Email Field - Full Width */}
        <FormField
          control={form.control}
          name="contact_email"
          render={({ field }) => (
            <FormItem className="space-y-1 sm:space-y-2">
              <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <Mail className="h-3.5 w-3.5 text-primary" />
                Contact Email
                <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    placeholder="e.g., <EMAIL>"
                    {...field}
                    type="email"
                    className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
                  />
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500">
                    {field.value?.length || 0}/100
                  </div>
                </div>
              </FormControl>
              <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                Email address for customers to contact you (required)
              </FormDescription>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />

        {/* Logo Upload Field - Full Width */}
        <FormField
          control={form.control}
          name="logo_url"
          render={() => {
            // Get the current logo URL from the form state
            const currentLogoUrl = form.watch("logo_url");
            // Only consider non-empty strings as valid URLs
            const validLogoUrl = currentLogoUrl && currentLogoUrl.trim() !== "" ? currentLogoUrl : null;
            const currentFileName = validLogoUrl ? validLogoUrl.split("/").pop()?.split("?")[0] : null;

            return (
              <FormItem className="space-y-1 sm:space-y-2">
                <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                  <Upload className="h-3.5 w-3.5 text-primary" />
                  Logo / Profile Photo
                </FormLabel>

                {/* Display current logo preview if it exists */}
                {currentFileName && (
                  <div className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg bg-gradient-to-r from-neutral-50 to-neutral-100 dark:from-neutral-800/50 dark:to-neutral-800 border border-neutral-200 dark:border-neutral-700">
                    {validLogoUrl && (
                      <div className="h-10 w-10 sm:h-12 sm:w-12 rounded-md border border-neutral-200 dark:border-neutral-700 overflow-hidden bg-white dark:bg-black flex items-center justify-center shadow-sm">
                        <Image
                          src={validLogoUrl}
                          alt="Current logo"
                          className="h-full w-full object-contain"
                          width={48}
                          height={48}
                          priority
                        />
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium text-neutral-800 dark:text-neutral-200 truncate">
                        {currentFileName}
                      </p>
                      <p className="text-xs text-neutral-500 dark:text-neutral-400">
                        Current logo
                      </p>
                    </div>
                    {onLogoDelete && !isLogoUploading && (
                      <button
                        type="button"
                        onClick={onLogoDelete}
                        className="p-1.5 rounded-full bg-red-50 dark:bg-red-900/20 text-red-500 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors cursor-pointer"
                        title="Delete logo"
                        aria-label="Delete logo"
                      >
                        <Trash2 className="h-3.5 w-3.5" />
                      </button>
                    )}
                    {isLogoUploading && (
                      <div className="p-1.5 rounded-full bg-neutral-100 dark:bg-neutral-800">
                        <Loader2 className="h-3.5 w-3.5 animate-spin text-neutral-500 dark:text-neutral-400" />
                      </div>
                    )}
                  </div>
                )}

                <FormControl>
                  <div className="relative">
                    <label className={`flex flex-col items-center justify-center w-full h-20 sm:h-24 border-2 border-dashed ${isLogoUploading ? 'border-primary/30 bg-primary/5' : 'border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/50 hover:bg-neutral-100 dark:hover:bg-neutral-800'} rounded-lg ${isLogoUploading ? 'cursor-wait' : 'cursor-pointer'} transition-all duration-300`}>
                      <div className="flex flex-col items-center justify-center pt-3 pb-3 sm:pt-4 sm:pb-4">
                        {isLogoUploading ? (
                          <>
                            <div className="p-1.5 sm:p-2 rounded-full bg-primary/10 mb-1 sm:mb-2">
                              <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 text-primary animate-spin" />
                            </div>
                            <p className="text-xs text-primary font-medium">
                              Uploading logo...
                            </p>
                            <p className="text-xs text-neutral-500 dark:text-neutral-400 text-center mt-0.5 max-w-xs px-4">
                              Please wait while we process your image
                            </p>
                          </>
                        ) : (
                          <>
                            <div className="p-1.5 sm:p-2 rounded-full bg-primary/10 mb-1 sm:mb-2 hover:bg-primary/20 transition-colors">
                              <Upload className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                            </div>
                            <p className="text-xs text-neutral-700 dark:text-neutral-300 font-medium">
                              {validLogoUrl ? "Replace logo" : "Drop your logo here"}
                            </p>
                            <p className="text-xs text-neutral-500 dark:text-neutral-400 text-center mt-0.5 max-w-xs px-4">
                              PNG, JPG, GIF or WEBP (Max. 15MB)
                            </p>
                          </>
                        )}
                      </div>
                      <Input
                        type="file"
                        accept="image/png, image/jpeg, image/gif, image/webp"
                        className="hidden"
                        onChange={(e) => onFileSelect(e.target.files?.[0] || null)}
                        value={undefined}
                        disabled={isLogoUploading}
                      />
                    </label>
                  </div>
                </FormControl>

                <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1">
                  <Info className="w-3 h-3" />
                  Your logo will be displayed prominently on your business card
                </FormDescription>
                <FormMessage className="text-xs text-red-500" />
              </FormItem>
            );
          }}
        />

        {/* About/Bio Field */}
        <FormField
          control={form.control}
          name="about_bio"
          render={({ field }) => (
            <FormItem className="space-y-1 sm:space-y-2">
              <FormLabel className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <MessageSquare className="h-3.5 w-3.5 text-primary" />
                About / Bio
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Textarea
                    placeholder="Tell us about your business or yourself..."
                    {...field}
                    className="min-h-[80px] sm:min-h-[100px] rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 p-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary resize-none transition-all"
                    maxLength={100}
                  />
                  <div className="absolute right-2 bottom-2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500">
                    {bioLength}/100
                  </div>
                </div>
              </FormControl>
              <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                A short description that will be displayed on your card
              </FormDescription>
              <FormMessage className="text-xs text-red-500" />
            </FormItem>
          )}
        />
      </div>

      {/* Pro Tip section */}
      <div className="mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm">
        <div className="flex items-start gap-2 sm:gap-3">
        <div className="p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm">
            <Info className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          </div>
          <div>
          <p className="text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300">
            Pro Tip
            </p>
            <p className="text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed">
              Keep your business name and title concise for better readability on your digital business card.
              A clear, professional photo or logo helps with brand recognition and creates a memorable first impression.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}