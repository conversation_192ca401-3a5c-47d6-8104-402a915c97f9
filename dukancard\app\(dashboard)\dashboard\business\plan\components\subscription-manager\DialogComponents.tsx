"use client";

// SubscriptionUpdateOptionsDialog is no longer used - plan switching is handled directly
import TrialSubscriptionWarningDialog from "../TrialSubscriptionWarningDialog";
import { SubscriptionState, SubscriptionManagerProps } from "./types";
import { useSubscriptionActions } from "./SubscriptionActions";

interface DialogComponentsProps {
  subscriptionState: SubscriptionState;
  subscriptionProps: SubscriptionManagerProps;
}

export const DialogComponents = ({
  subscriptionState,
  subscriptionProps,
}: DialogComponentsProps) => {
  const {
    // Removed unused variables
    showTrialWarningDialog,
    setShowTrialWarningDialog,
    trialSubscriptionId,
    setCancelImmediately,
  } = subscriptionState;

  // Get subscription actions
  const actions = useSubscriptionActions({
    subscriptionState,
    subscriptionProps,
  });

  return (
    <>
      {/* Payment Method Limitations Dialog has been removed - plan switching is now handled directly */}

      {/* Trial Subscription Warning Dialog */}
      {showTrialWarningDialog !== undefined && setShowTrialWarningDialog && trialSubscriptionId && (
        <TrialSubscriptionWarningDialog
          open={showTrialWarningDialog === true}
          onOpenChange={(open) => {
            if (setShowTrialWarningDialog) {
              setShowTrialWarningDialog(open);
            }
          }}
          subscriptionId={trialSubscriptionId}
          onCancelSubscription={() => {
            if (setShowTrialWarningDialog) {
              setShowTrialWarningDialog(false);
            }
            setCancelImmediately(true);
            actions.handleCancelSubscription();
          }}
        />
      )}
    </>
  );
};
