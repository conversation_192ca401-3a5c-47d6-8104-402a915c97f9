"use server";

import { createClient } from "@/utils/supabase/server";

/**
 * Checks if a user's subscription allows them to go online
 * @param userId - The user ID to check
 * @returns Object indicating if user can go online and any error message
 */
export async function checkSubscriptionStatus(userId: string): Promise<{
  canGoOnline: boolean;
  error?: string;
}> {
  const supabase = await createClient();

  // Fetch subscription status from payment_subscriptions
  const { data: subscription, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("subscription_status")
    .eq("business_profile_id", userId)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
    // Continue with caution - allow going online if we can't check
    return { canGoOnline: true };
  }

  // If subscription is halted, prevent going online
  if (subscription?.subscription_status === "halted") {
    console.log(`User ${userId} attempted to set card online with halted subscription`);
    return {
      canGoOnline: false,
      error: "Cannot set card to online status while your subscription is paused. Please resume your subscription first."
    };
  }

  return { canGoOnline: true };
}

/**
 * Checks subscription status for forcing offline during data fetch
 * @param userId - The user ID to check
 * @param supabase - Supabase client instance
 * @returns Object indicating if card should be forced offline
 */
export async function checkForceOfflineStatus(userId: string, supabase: Awaited<ReturnType<typeof import("@/utils/supabase/server").createClient>>): Promise<{
  shouldForceOffline: boolean;
  reason?: string;
}> {
  // Fetch subscription status from payment_subscriptions
  const { data: subscription, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("subscription_status")
    .eq("business_profile_id", userId)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
    return { shouldForceOffline: false };
  }

  // Import centralized manager for consistent logic
  const { SUBSCRIPTION_STATUS } = await import('@/lib/razorpay/webhooks/handlers/utils');

  // Check if subscription is halted - force offline if it is
  if (subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED) {
    return {
      shouldForceOffline: true,
      reason: "subscription is paused"
    };
  }

  return { shouldForceOffline: false };
}
