import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';
import { Platform, Linking } from 'react-native';
import { supabase } from '@/lib/supabase';
import { supabaseAdmin } from '@/lib/utils/supabaseAdmin';
import NotificationFrequencyManager from './notificationFrequencyManager';

// Configure notification handler following latest Expo best practices
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

// Configure notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export interface PushNotificationData {
  type: 'business_activity' | 'post_interaction' | 'general';
  businessId?: string;
  postId?: string;
  url?: string;
  [key: string]: any;
}

export interface BusinessActivityData {
  type: 'like' | 'subscribe' | 'rating' | 'visit';
  businessId: string;
  userId: string;
  userName?: string;
  userType?: 'customer' | 'business';
  businessSlug?: string;
  ratingValue?: number;
  postId?: string;
}



export interface NotificationPayload {
  title: string;
  body: string;
  data?: PushNotificationData;
  sound?: 'default' | string;
  badge?: number;
}

class PushNotificationService {
  private static instance: PushNotificationService;
  private pushToken: string | null = null;
  private isInitialized = false;

  static getInstance(): PushNotificationService {
    if (!PushNotificationService.instance) {
      PushNotificationService.instance = new PushNotificationService();
    }
    return PushNotificationService.instance;
  }

  /**
   * Initialize push notifications
   * Should be called once when the app starts
   */
  async initialize(): Promise<string | null> {
    if (this.isInitialized) {
      return this.pushToken;
    }

    try {
      // Set up Android notification channel
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('dukancard-default', {
          name: 'DukanCard Notifications',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#D4AF37', // Gold color
          sound: 'default',
          enableVibrate: true,
          enableLights: true,
        });

        // Business activities channel
        await Notifications.setNotificationChannelAsync('dukancard-business', {
          name: 'Business Activities',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#D4AF37',
          sound: 'default',
        });

        // Post interactions channel
        await Notifications.setNotificationChannelAsync('dukancard-posts', {
          name: 'Post Interactions',
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 150, 150, 150],
          lightColor: '#D4AF37',
          sound: 'default',
        });
      }

      // Register for push notifications
      this.pushToken = await this.registerForPushNotifications();
      this.isInitialized = true;

      return this.pushToken;
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
      return null;
    }
  }

  /**
   * Register device for push notifications
   */
  private async registerForPushNotifications(): Promise<string | null> {
    // Check if running on physical device
    if (!Device.isDevice) {
      console.warn('Push notifications require a physical device');
      return null;
    }

    try {
      // Check existing permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      // Request permissions if not granted
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Push notification permissions not granted');
        return null;
      }

      // Get Expo push token (using Expo's push service)
      try {
        const pushTokenData = await Notifications.getExpoPushTokenAsync();
        const token = pushTokenData.data;
        console.log('Push token obtained:', token);
        return token;
      } catch (tokenError: any) {
        // Handle Firebase-related errors specifically
        if (tokenError.message && tokenError.message.includes('FirebaseApp is not initialized')) {
          // Firebase not initialized - this is expected when using Expo push service only
          // Push notifications will work in development mode with Expo Go
          // Return a development token or null
          return null;
        }
        throw tokenError; // Re-throw other errors
      }

    } catch (error) {
      console.error('Error registering for push notifications:', error);
      return null;
    }
  }

  /**
   * Store push token in database for the current user
   */
  async storePushToken(userId: string, userType: 'customer' | 'business'): Promise<boolean> {
    if (!this.pushToken) {
      console.warn('No push token available to store');
      return false;
    }

    try {
      const { error } = await supabase
        .from('push_tokens')
        .upsert({
          user_id: userId,
          token: this.pushToken,
          user_type: userType,
          platform: Platform.OS,
          device_info: {
            brand: Device.brand,
            modelName: Device.modelName,
            osName: Device.osName,
            osVersion: Device.osVersion,
          },
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'user_id,token'
        });

      if (error) {
        console.error('Error storing push token:', error);
        return false;
      }

      console.log('Push token stored successfully');
      return true;
    } catch (error) {
      console.error('Error storing push token:', error);
      return false;
    }
  }

  /**
   * Remove push token from database
   */
  async removePushToken(userId: string): Promise<boolean> {
    if (!this.pushToken) {
      return true; // Nothing to remove
    }

    try {
      const { error } = await supabase
        .from('push_tokens')
        .delete()
        .eq('user_id', userId)
        .eq('token', this.pushToken);

      if (error) {
        console.error('Error removing push token:', error);
        return false;
      }

      console.log('Push token removed successfully');
      return true;
    } catch (error) {
      console.error('Error removing push token:', error);
      return false;
    }
  }

  /**
   * Schedule a local notification
   */
  async scheduleLocalNotification(
    payload: NotificationPayload,
    trigger: Notifications.NotificationTriggerInput
  ): Promise<string | null> {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: payload.title,
          body: payload.body,
          data: payload.data || {},
          sound: payload.sound || 'default',
          badge: payload.badge,
        },
        trigger,
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling local notification:', error);
      return null;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  async cancelNotification(notificationId: string): Promise<boolean> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      return true;
    } catch (error) {
      console.error('Error canceling notification:', error);
      return false;
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<boolean> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      return true;
    } catch (error) {
      console.error('Error canceling all notifications:', error);
      return false;
    }
  }

  /**
   * Get current push token
   */
  getPushToken(): string | null {
    return this.pushToken;
  }

  /**
   * Check if notifications are enabled
   */
  async areNotificationsEnabled(): Promise<boolean> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      return false;
    }
  }

  /**
   * Send push notification to business user when they receive activity
   */
  async sendBusinessActivityNotification(data: BusinessActivityData): Promise<void> {
    try {
      // Use frequency manager for intelligent frequency control
      const frequencyManager = NotificationFrequencyManager;
      const priority = this.getNotificationPriority(data.type);

      const shouldSend = await frequencyManager.shouldSendNotification(
        data.businessId,
        data.type,
        priority
      );

      if (!shouldSend) {
        console.log('Notification skipped due to frequency control');
        return;
      }

      // Get business push tokens
      const { data: tokens, error } = await supabaseAdmin
        .from('push_tokens')
        .select('token, platform')
        .eq('user_id', data.businessId)
        .eq('user_type', 'business')
        .eq('is_active', true);

      if (error || !tokens || tokens.length === 0) {
        console.log('No active push tokens found for business user');
        return;
      }

      // Get user profile for personalization
      const userProfile = await this.getUserProfile(data.userId, data.userType);
      const userName = userProfile?.name || 'Someone';

      // Create notification content
      const notification = this.createBusinessNotificationContent(data, userName);

      // Send to all active tokens
      const messages = tokens.map(tokenData => ({
        to: tokenData.token,
        sound: 'default',
        title: notification.title,
        body: notification.body,
        data: {
          type: data.type,
          businessId: data.businessId,
          userId: data.userId,
          businessSlug: data.businessSlug,
          postId: data.postId,
        },
        badge: 1,
        channelId: 'dukancard-business',
      }));

      await this.sendPushMessages(messages);

      // Record notification for frequency tracking
      await frequencyManager.recordSentNotification(data.businessId, data.type, priority);

      console.log(`Push notification sent for ${data.type} activity`);
    } catch (error) {
      console.error('Error sending push notification:', error);
    }
  }

  /**
   * Get notification priority based on activity type
   */
  private getNotificationPriority(type: string): 'low' | 'medium' | 'high' | 'urgent' {
    switch (type) {
      case 'rating':
        return 'high'; // Ratings are important feedback
      case 'subscribe':
        return 'medium'; // Subscriptions are valuable
      case 'like':
        return 'low'; // Likes are nice but not critical
      case 'visit':
        return 'low'; // Visits are informational
      default:
        return 'medium';
    }
  }

  /**
   * Create notification content based on activity type
   */
  private createBusinessNotificationContent(data: BusinessActivityData, userName: string) {
    switch (data.type) {
      case 'like':
        return {
          title: '❤️ New Like!',
          body: `${userName} liked your business`,
        };
      case 'subscribe':
        return {
          title: '🔔 New Subscriber!',
          body: `${userName} subscribed to your business`,
        };
      case 'rating':
        return {
          title: '⭐ New Rating!',
          body: `${userName} rated your business ${data.ratingValue}/5`,
        };
      case 'visit':
        return {
          title: '👀 Business Card Visit',
          body: `${userName} visited your business card`,
        };
      default:
        return {
          title: '🔔 New Activity',
          body: `${userName} interacted with your business`,
        };
    }
  }

  /**
   * Get user profile for personalization
   */
  private async getUserProfile(userId: string, userType?: string): Promise<{ name: string } | null> {
    try {
      if (userType === 'business') {
        const { data } = await supabaseAdmin
          .from('business_profiles')
          .select('business_name')
          .eq('id', userId)
          .single();
        return data ? { name: data.business_name } : null;
      } else {
        const { data } = await supabaseAdmin
          .from('customer_profiles')
          .select('name')
          .eq('id', userId)
          .single();
        return data;
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  }



  /**
   * Send push messages using Expo's push service
   */
  private async sendPushMessages(messages: any[]): Promise<void> {
    try {
      const chunks = this.chunkArray(messages, 100); // Expo recommends max 100 per request

      for (const chunk of chunks) {
        const response = await fetch('https://exp.host/--/api/v2/push/send', {
          method: 'POST',
          headers: {
            Accept: 'application/json',
            'Accept-encoding': 'gzip, deflate',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(chunk),
        });

        if (!response.ok) {
          throw new Error(`Push notification failed: ${response.statusText}`);
        }
      }
    } catch (error) {
      console.error('Error sending push messages:', error);
      throw error;
    }
  }

  /**
   * Utility function to chunk array
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * Clean up inactive tokens
   */
  async cleanupInactiveTokens(): Promise<void> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      await supabaseAdmin
        .from('push_tokens')
        .update({ is_active: false })
        .lt('updated_at', thirtyDaysAgo.toISOString());

      console.log('Inactive tokens cleaned up');
    } catch (error) {
      console.error('Error cleaning up tokens:', error);
    }
  }

  /**
   * Send a test notification (for development and testing)
   */
  async sendTestNotification(): Promise<void> {
    try {
      await this.scheduleLocalNotification(
        {
          title: '🎉 DukanCard Test',
          body: 'Push notifications are working correctly!',
          data: { type: 'general' },
        },
        {
          type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,
          seconds: 1
        }
      );
      console.log('Test notification scheduled');
    } catch (error) {
      console.error('Error sending test notification:', error);
    }
  }

  /**
   * Open device notification settings
   */
  async openNotificationSettings(): Promise<void> {
    try {
      if (Platform.OS === 'ios') {
        await Linking.openURL('app-settings:');
      } else {
        await Linking.openURL('package:' + Constants.expoConfig?.android?.package);
      }
    } catch (error) {
      console.error('Error opening notification settings:', error);
    }
  }
}

export default PushNotificationService.getInstance();
