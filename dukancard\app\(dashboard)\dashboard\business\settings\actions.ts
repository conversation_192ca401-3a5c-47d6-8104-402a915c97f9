"use server";

import { createClient } from "@/utils/supabase/server";
import { z } from "zod";
import { revalidatePath } from "next/cache";
import { EmailSchema, PasswordSchema, LinkEmailSchema, VerifyEmailOTPSchema } from "./schema"; // Import schemas

// --- Update Email Action ---
export async function updateEmail(
  values: z.infer<typeof EmailSchema>
): Promise<{ success: boolean; message: string }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { success: false, message: "Authentication required." };
  }

  // Validate input server-side
  const validatedFields = EmailSchema.safeParse(values);
  if (!validatedFields.success) {
    return { success: false, message: "Invalid email format." };
  }

  const { email } = validatedFields.data;

  // Call Supabase to update email (triggers confirmation flow)
  const { error: updateError } = await supabase.auth.updateUser({ email });

  if (updateError) {
    console.error("Update Email Error:", updateError);
    return {
      success: false,
      message: updateError.message || "Failed to initiate email update.",
    };
  }

  return {
    success: true,
    message:
      "Confirmation email sent to the new address. Please check your inbox.",
  };
}

// --- Update Password Action ---
export async function updatePassword(
  values: z.infer<typeof PasswordSchema>
): Promise<{ success: boolean; message: string }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user || !user.email) {
    return { success: false, message: "Authentication required." };
  }

  // Validate input server-side
  const validatedFields = PasswordSchema.safeParse(values);
  if (!validatedFields.success) {
    // Consider returning specific field errors if needed
    return {
      success: false,
      message: "Invalid password format or passwords do not match.",
    };
  }

  const { currentPassword, newPassword } = validatedFields.data;

  // IMPORTANT: Verify the current password first before attempting update
  // by trying to sign in with the current password
  const { error: signInError } = await supabase.auth.signInWithPassword({
    email: user.email,
    password: currentPassword,
  });

  if (signInError) {
    console.error("Current Password Verification Error:", signInError);
    // Provide a user-friendly error message
    if (signInError.message.includes("Invalid login credentials")) {
      return { success: false, message: "Current password is incorrect." };
    }
    return {
      success: false,
      message: signInError.message || "Failed to verify current password.",
    };
  }

  // If current password verification succeeded, proceed with the update
  const { error: updateError } = await supabase.auth.updateUser({
    password: newPassword,
  });

  if (updateError) {
    console.error("Update Password Error:", updateError);
    return {
      success: false,
      message: updateError.message || "Failed to update password.",
    };
  }

  return { success: true, message: "Password updated successfully." };
}

// --- Enhanced Delete Account Security Actions ---

// Check user's email and phone availability for delete account verification
export async function checkDeleteAccountVerificationOptions(): Promise<{
  success: boolean;
  hasEmail: boolean;
  hasPhone: boolean;
  message?: string;
}> {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      hasEmail: false,
      hasPhone: false,
      message: "Authentication required."
    };
  }

  const hasEmail = !!(user.email && user.email.trim() !== '');
  const hasPhone = !!(user.phone && user.phone.trim() !== '');

  return {
    success: true,
    hasEmail,
    hasPhone,
  };
}

// Send OTP to email for delete account verification
export async function sendDeleteAccountOTP(): Promise<{
  success: boolean;
  message: string;
  email?: string;
  isConfigurationError?: boolean;
}> {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user || !user.email) {
    return {
      success: false,
      message: "Authentication required or no email found."
    };
  }

  try {
    const { error } = await supabase.auth.signInWithOtp({
      email: user.email,
      options: {
        shouldCreateUser: false, // Don't create new user
      },
    });

    if (error) {
      // Handle rate limit errors specifically
      if (error.message?.includes('email_send_rate_limit') || error.message?.includes('over_email_send_rate_limit')) {
        return {
          success: false,
          message: "Email rate limit exceeded. Please try again later.",
          isConfigurationError: true,
        };
      }

      return {
        success: false,
        message: error.message || "Failed to send verification code.",
      };
    }

    return {
      success: true,
      message: "Verification code sent to your email address.",
      email: user.email,
    };
  } catch (_error) {
    return {
      success: false,
      message: "An unexpected error occurred while sending verification code.",
    };
  }
}

// Verify OTP for delete account
export async function verifyDeleteAccountOTP(email: string, otp: string): Promise<{
  success: boolean;
  message: string;
}> {
  const supabase = await createClient();

  try {
    const { error } = await supabase.auth.verifyOtp({
      email: email,
      token: otp,
      type: 'email',
    });

    if (error) {
      let errorMessage = 'Failed to verify code.';

      switch (error.code) {
        case 'invalid_otp':
        case 'expired_otp':
          errorMessage = 'Invalid or expired verification code. Please try again.';
          break;
        case 'too_many_requests':
          errorMessage = 'Too many verification attempts. Please wait before trying again.';
          break;
        default:
          errorMessage = 'Unable to verify code. Please try again.';
      }

      return { success: false, message: errorMessage };
    }

    return { success: true, message: "Verification successful." };
  } catch (_error) {
    return {
      success: false,
      message: "An unexpected error occurred during verification.",
    };
  }
}

// Verify password for delete account (for phone users)
export async function verifyDeleteAccountPassword(password: string): Promise<{
  success: boolean;
  message: string;
}> {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user || !user.phone) {
    return {
      success: false,
      message: "Authentication required or no phone found."
    };
  }

  try {
    // Verify current password by attempting to sign in
    const { error } = await supabase.auth.signInWithPassword({
      phone: user.phone,
      password: password,
    });

    if (error) {
      return {
        success: false,
        message: "Invalid password. Please try again.",
      };
    }

    return { success: true, message: "Password verified successfully." };
  } catch (_error) {
    return {
      success: false,
      message: "An unexpected error occurred during password verification.",
    };
  }
}

// --- Delete Account Action ---
export async function deleteAccount(): Promise<{
  success: boolean;
  message: string;
}> {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { success: false, message: "Authentication required." };
  }

  try {
    // 1. Fetch subscription information from payment_subscriptions table
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("razorpay_subscription_id, subscription_status")
      .eq("business_profile_id", user.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle();

    if (subscriptionError) {
      console.error("Delete Account - Error fetching subscription:", subscriptionError);
      // Log the error but continue with deletion process
      // We don't want to block account deletion if subscription fetch fails
    }

    // Handle subscription cancellation if it exists
    if (subscription && subscription.razorpay_subscription_id) {
      // If the subscription is in authenticated state, we need to pause it to cancel it
      if (subscription.subscription_status === "authenticated") {
        console.log("[DELETE_ACCOUNT] Handling authenticated subscription cancellation");

        try {
          // Import the pauseSubscription function
          const { pauseSubscription } = await import("@/lib/razorpay/services/subscription");

          // For authenticated subscriptions, we'll use the pause endpoint which will cancel it
          // According to Razorpay docs: "If you pause a Subscription in the authenticated state, it goes to the cancelled state"
          const pauseResult = await pauseSubscription(subscription.razorpay_subscription_id, "now", true);

          if (!pauseResult.success) {
            console.error("[DELETE_ACCOUNT] Error pausing authenticated subscription:", pauseResult.error);
            // Continue anyway - we'll still delete the account
          } else {
            console.log("[DELETE_ACCOUNT] Successfully paused/cancelled authenticated subscription");
          }

          // Note: No need to update subscription status in database
          // CASCADE deletion will handle all database cleanup automatically
        } catch (subscriptionError) {
          console.error("[DELETE_ACCOUNT] Error handling subscription cancellation:", subscriptionError);
          // Continue with account deletion even if subscription cancellation fails
        }
      } else if (subscription.subscription_status === "active") {
        // For active subscriptions, cancel them in Razorpay before account deletion
        console.log("[DELETE_ACCOUNT] Cancelling active subscription in Razorpay");

        try {
          // Import the cancelSubscription function
          const { cancelSubscription } = await import("@/lib/razorpay/services/subscription");

          // Cancel the active subscription immediately
          const cancelResult = await cancelSubscription(subscription.razorpay_subscription_id, false);

          if (!cancelResult.success) {
            console.error("[DELETE_ACCOUNT] Error cancelling active subscription:", cancelResult.error);
            // Continue anyway - we'll still delete the account
          } else {
            console.log("[DELETE_ACCOUNT] Successfully cancelled active subscription");
          }

          // Note: No need to update subscription status in database
          // CASCADE deletion will handle all database cleanup automatically
        } catch (subscriptionError) {
          console.error("[DELETE_ACCOUNT] Error handling active subscription cancellation:", subscriptionError);
          // Continue with account deletion even if subscription cancellation fails
        }
      } else if (subscription.subscription_status === "halted" ||
                 subscription.subscription_status === "pending" ||
                 subscription.subscription_status === "cancellation_scheduled") {
        // For halted, pending, or cancellation_scheduled subscriptions, also cancel them in Razorpay
        console.log(`[DELETE_ACCOUNT] Cancelling ${subscription.subscription_status} subscription in Razorpay`);

        try {
          // Import the cancelSubscription function
          const { cancelSubscription } = await import("@/lib/razorpay/services/subscription");

          // Cancel the subscription immediately
          const cancelResult = await cancelSubscription(subscription.razorpay_subscription_id, false);

          if (!cancelResult.success) {
            console.error(`[DELETE_ACCOUNT] Error cancelling ${subscription.subscription_status} subscription:`, cancelResult.error);
            // Continue anyway - we'll still delete the account
          } else {
            console.log(`[DELETE_ACCOUNT] Successfully cancelled ${subscription.subscription_status} subscription`);
          }

          // Note: No need to update subscription status in database
          // CASCADE deletion will handle all database cleanup automatically
        } catch (subscriptionError) {
          console.error(`[DELETE_ACCOUNT] Error handling ${subscription.subscription_status} subscription cancellation:`, subscriptionError);
          // Continue with account deletion even if subscription cancellation fails
        }
      } else {
        // For other statuses (trial, cancelled, expired, completed, payment_failed), no Razorpay action needed
        console.log(`[DELETE_ACCOUNT] Subscription status is ${subscription.subscription_status} - no Razorpay cancellation needed`);
      }
    }

    // 2. Clean up storage data
    try {
      // Import the admin client for storage operations
      const { createAdminClient } = await import('@/utils/supabase/admin');
      const { getScalableUserPath } = await import('@/lib/utils/storage-paths');
      const supabaseAdmin = createAdminClient();

      const bucketName = "business";
      const userStoragePath = getScalableUserPath(user.id);

      // Recursive function to delete all files and folders in a path
      const deleteRecursively = async (path: string): Promise<void> => {
        const { data: items, error: listError } = await supabaseAdmin.storage
          .from(bucketName)
          .list(path);

        if (listError) {
          console.error(`Error listing files in ${path}:`, listError);
          return;
        }

        if (!items || items.length === 0) {
          return;
        }

        // Separate files and folders
        const files: string[] = [];
        const folders: string[] = [];

        for (const item of items) {
          const fullPath = path ? `${path}/${item.name}` : item.name;

          if (item.metadata === null) {
            // This is a folder
            folders.push(fullPath);
          } else {
            // This is a file
            files.push(fullPath);
          }
        }

        // Delete all files in the current directory
        if (files.length > 0) {
          const { error: deleteError } = await supabaseAdmin.storage
            .from(bucketName)
            .remove(files);

          if (deleteError && deleteError.message !== "The resource was not found") {
            console.error(`Error deleting files in ${path}:`, deleteError);
          } else {
            console.log(`Successfully deleted ${files.length} files in ${path}`);
          }
        }

        // Recursively delete folders
        for (const folder of folders) {
          await deleteRecursively(folder);
        }
      };

      // Start the recursive deletion from the user's root folder
      await deleteRecursively(userStoragePath);

      console.log('Successfully cleaned up business storage data');
    } catch (storageError) {
      // Log but continue with deletion
      console.error("Error cleaning up storage data:", storageError);
    }

    // 3. Use the admin client to delete the user and profile
    // Import the admin client
    const { createAdminClient } = await import('@/utils/supabase/admin');
    const supabaseAdmin = createAdminClient();

    // Delete from business_profiles table (CASCADE will handle related data)
    console.log('Deleting business profile...');
    const { error: deleteProfileError } = await supabaseAdmin
      .from('business_profiles')
      .delete()
      .eq('id', user.id);

    if (deleteProfileError) {
      console.error('Error deleting business profile:', deleteProfileError);
      return {
        success: false,
        message: `Failed to delete business profile: ${deleteProfileError.message}`
      };
    }

    console.log('Business profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed.');

    // 4. Sign out the user locally first (while the session is still valid)
    await supabase.auth.signOut();

    // Then delete the user using the admin client
    // Using hard delete (shouldSoftDelete=false) to completely remove the user
    const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(user.id, false);

    if (deleteUserError) {
      console.error('Error deleting user account:', deleteUserError);
      return {
        success: false,
        message: `Failed to delete account: ${deleteUserError.message}`
      };
    }

    // 5. Revalidate paths if needed
    revalidatePath("/", "layout"); // Revalidate root layout

    return { success: true, message: "Account deleted successfully." };
  } catch (error) {
    console.error("Delete Account - Unexpected error:", error);
    return {
      success: false,
      message: "An unexpected error occurred during account deletion.",
    };
  }
}

// --- Business Email Linking Actions ---

export type LinkBusinessEmailFormState = {
  message: string | null;
  errors?: {
    email?: string[];
  };
  success: boolean;
};

export async function linkBusinessEmail(
  _prevState: LinkBusinessEmailFormState,
  formData: FormData
): Promise<LinkBusinessEmailFormState> {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const validatedFields = LinkEmailSchema.safeParse({
    email: formData.get('email'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid data provided.',
      errors: validatedFields.error.flatten().fieldErrors,
      success: false,
    };
  }

  const { email } = validatedFields.data;

  try {
    // Check if user already has an email (email update) or not (email linking)
    const isEmailUpdate = !!user.email;

    if (isEmailUpdate) {
      // User already has email - use email change flow
      const { error: authUpdateError } = await supabase.auth.updateUser({
        email: email,
      });

      if (authUpdateError) {
        // Handle specific Supabase auth error codes
        let errorMessage = 'Failed to update email address.';

        switch (authUpdateError.code) {
          case 'email_exists':
            errorMessage = 'This email address is already registered with another account.';
            break;
          case 'invalid_email':
            errorMessage = 'Please enter a valid email address.';
            break;
          case 'email_change_confirm_limit':
            errorMessage = 'Too many email change requests. Please wait before trying again.';
            break;
          case 'over_email_send_rate_limit':
            errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';
            break;
          case 'email_not_confirmed':
            errorMessage = 'Please confirm your current email address before changing it.';
            break;
          case 'same_email':
            errorMessage = 'The new email address is the same as your current email.';
            break;
          default:
            errorMessage = 'Unable to update email address. Please try again later.';
        }

        return { message: errorMessage, success: false };
      }

      // Note: business_profiles table will be automatically updated via database trigger

      return {
        message: 'Verification email sent to both old and new addresses. Please check your inbox to complete the change.',
        success: true,
      };
    } else {
      // User doesn't have email - directly link the email without OTP verification
      // Supabase will automatically handle duplicate validation
      const { error: updateError } = await supabase.auth.updateUser({
        email: email,
      });

      if (updateError) {
        // Handle specific Supabase auth error codes
        let errorMessage = 'Failed to link email address.';

        switch (updateError.code) {
          case 'email_exists':
            errorMessage = 'This email address is already registered with another account.';
            break;
          case 'invalid_email':
            errorMessage = 'Please enter a valid email address.';
            break;
          case 'email_change_confirm_limit':
            errorMessage = 'Too many email requests. Please wait before trying again.';
            break;
          case 'over_email_send_rate_limit':
            errorMessage = 'Email rate limit exceeded. Please wait before trying again.';
            break;
          default:
            errorMessage = 'Unable to link email address. Please try again later.';
        }

        return { message: errorMessage, success: false };
      }

      // Note: business_profiles table will be automatically updated via database trigger

      return {
        message: 'Email address linked successfully!',
        success: true,
      };
    }
  } catch (_error) {
    return { message: 'An unexpected error occurred while linking email.', success: false };
  }
}

// --- Verify Business Email OTP ---

export type VerifyBusinessEmailOTPFormState = {
  message: string | null;
  errors?: {
    email?: string[];
    otp?: string[];
  };
  success: boolean;
};

export async function verifyBusinessEmailOTP(
  _prevState: VerifyBusinessEmailOTPFormState,
  formData: FormData
): Promise<VerifyBusinessEmailOTPFormState> {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const validatedFields = VerifyEmailOTPSchema.safeParse({
    email: formData.get('email'),
    otp: formData.get('otp'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid data provided.',
      errors: validatedFields.error.flatten().fieldErrors,
      success: false,
    };
  }

  const { email, otp } = validatedFields.data;

  try {
    // Verify the OTP
    const { error: verifyError } = await supabase.auth.verifyOtp({
      email: email,
      token: otp,
      type: 'email',
    });

    if (verifyError) {
      let errorMessage = 'Failed to verify code.';

      switch (verifyError.code) {
        case 'invalid_otp':
        case 'expired_otp':
          errorMessage = 'Invalid or expired verification code. Please try again.';
          break;
        case 'too_many_requests':
          errorMessage = 'Too many verification attempts. Please wait before trying again.';
          break;
        default:
          errorMessage = 'Unable to verify code. Please try again.';
      }

      return { message: errorMessage, success: false };
    }

    // If verification successful, update the user's email
    const { error: updateError } = await supabase.auth.updateUser({
      email: email,
    });

    if (updateError) {
      return { message: 'Verification successful but failed to link email. Please contact support.', success: false };
    }

    // Note: business_profiles table will be automatically updated via database trigger

    return {
      message: 'Email address linked successfully!',
      success: true,
    };
  } catch (_error) {
    return { message: 'An unexpected error occurred while verifying code.', success: false };
  }
}
