export interface BusinessActivity {
  id: string;
  business_profile_id: string;
  user_id: string;
  activity_type: 'like' | 'subscribe' | 'rating' | 'visit';
  rating_value?: number | null;
  created_at: string;
  is_read: boolean;
  customer_profiles?: {
    name?: string | null;
    avatar_url?: string | null;
    email?: string | null;
  };
}

export interface BusinessActivitiesResponse {
  success: boolean;
  data?: BusinessActivity[];
  count?: number;
  error?: string;
}

export type ActivityFilterType = 'all' | 'like' | 'subscribe' | 'rating' | 'unread';
export type ActivitySortType = 'newest' | 'oldest' | 'unread_first';

export interface GetBusinessActivitiesParams {
  businessProfileId: string;
  page?: number;
  limit?: number;
  filter?: ActivityFilterType;
  sort?: ActivitySortType;
  autoMarkAsRead?: boolean;
}

export interface MarkActivitiesAsReadParams {
  businessProfileId: string;
  activityIds?: string[] | 'all';
}

export interface ActivityServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
