"use client";

import { Card } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  index: number;
}

export default function FeatureCard({
  icon: Icon,
  title,
  description,
  index: _index, // Renamed to _index to indicate it's intentionally unused
}: FeatureCardProps) {
  return (
    <div className="h-full">
      <Card className="bg-white/90 dark:bg-black/40 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 p-6 h-full hover:border-[var(--brand-gold)]/70 transition-all duration-300 hover:shadow-lg dark:hover:shadow-[var(--brand-gold)]/10 rounded-xl group relative overflow-hidden">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/0 to-[var(--brand-gold)]/5 dark:from-transparent dark:to-[var(--brand-gold)]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        {/* Icon container */}
        <div className="mb-4 relative">
          <div className="w-14 h-14 rounded-full bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 flex items-center justify-center relative overflow-hidden group-hover:bg-[var(--brand-gold)]/20 dark:group-hover:bg-[var(--brand-gold)]/30 transition-colors duration-300">
            <Icon className="w-7 h-7 text-[var(--brand-gold)]" />
          </div>
        </div>

        {/* Title */}
        <div className="relative inline-block mb-2">
          <h3 className="text-xl font-semibold text-foreground">{title}</h3>
        </div>

        <p className="text-neutral-600 dark:text-neutral-400 relative z-10">
          {description}
        </p>
      </Card>
    </div>
  );
}
