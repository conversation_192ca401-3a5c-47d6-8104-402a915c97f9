"use client";

import React, { useState, useCallback, useEffect } from "react";
import <PERSON><PERSON><PERSON>, { Point, Area } from "react-easy-crop";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";
import { Slider } from "@/components/ui/slider";

// Helper function to create an image element
const createImage = (url: string): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener("load", () => resolve(image));
    image.addEventListener("error", (error) => reject(error));
    image.setAttribute("crossOrigin", "anonymous"); // needed to avoid cross-origin issues
    image.src = url;
  });

// Helper function to get the cropped image blob
async function getCroppedImgBlob(
  imageSrc: string,
  pixelCrop: Area
): Promise<Blob | null> {
  const image = await createImage(imageSrc);
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  if (!ctx) {
    return null;
  }

  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;
  const pixelRatio = window.devicePixelRatio || 1;

  canvas.width = pixelCrop.width * pixelRatio * scaleX;
  canvas.height = pixelCrop.height * pixelRatio * scaleY;

  ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);
  ctx.imageSmoothingQuality = "high";

  ctx.drawImage(
    image,
    pixelCrop.x * scaleX,
    pixelCrop.y * scaleY,
    pixelCrop.width * scaleX,
    pixelCrop.height * scaleY,
    0,
    0,
    pixelCrop.width * scaleX,
    pixelCrop.height * scaleY
  );

  return new Promise((resolve) => {
    canvas.toBlob(
      resolve, // Pass resolve directly as the callback
      "image/png" // Output as PNG from canvas
      // Quality parameter is not applicable for PNG
    );
  });
}

interface ProductImageCropDialogProps {
  imgSrc: string | null;
  onCropComplete: (_blob: Blob | null) => void;
  onClose: () => void;
  isOpen: boolean;
}

export default function ProductImageCropDialog({
  imgSrc,
  onCropComplete,
  onClose,
  isOpen,
}: ProductImageCropDialogProps) {
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [isCropping, setIsCropping] = useState(false);

  const onCropCompleteCallback = useCallback((_croppedArea: Area, croppedAreaPixels: Area) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const handleCrop = async () => {
    if (!imgSrc || !croppedAreaPixels) {
      console.warn("Image source or crop area not available.");
      onCropComplete(null);
      return;
    }
    setIsCropping(true);
    try {
      const croppedBlob = await getCroppedImgBlob(imgSrc, croppedAreaPixels);
      onCropComplete(croppedBlob);
    } catch (e) {
      console.error("Error cropping image:", e);
      onCropComplete(null); // Indicate error
    } finally {
      setIsCropping(false);
    }
  };

  // Reset zoom when dialog opens
  useEffect(() => {
    if (isOpen) {
      setZoom(1);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Crop Your Product Image</DialogTitle>
        </DialogHeader>
        <div className="relative h-[40vh] md:h-[50vh] w-full my-4 bg-neutral-200 dark:bg-neutral-800">
          {imgSrc ? (
            <Cropper
              image={imgSrc}
              crop={crop}
              zoom={zoom}
              aspect={1} // 1:1 (square) aspect ratio for product images
              cropShape="rect" // Rectangular crop area
              showGrid={true}
              onCropChange={setCrop}
              onZoomChange={setZoom}
              onCropComplete={onCropCompleteCallback}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <p>Loading image...</p>
            </div>
          )}
        </div>
        {/* Zoom Slider */}
        <div className="px-4 pb-4">
          <Slider
            min={1}
            max={3}
            step={0.1}
            value={[zoom]}
            onValueChange={(value: number[]) => setZoom(value[0])}
            className="w-full"
            aria-label="Zoom slider"
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isCropping}>
            Cancel
          </Button>
          <Button
            onClick={handleCrop}
            disabled={isCropping}
            className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]"
          >
            {isCropping ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Crop Image
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
