import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Package, ImageIcon, MessageSquare } from 'lucide-react-native';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

export type TabType = 'about' | 'products' | 'gallery' | 'reviews';

interface TabNavigationProps {
  selectedTab: TabType;
  onTabChange: (tab: TabType) => void;
  isDark: boolean;
}

export default function TabNavigation({ selectedTab, onTabChange, isDark }: TabNavigationProps) {
  const styles = createPublicCardViewStyles(isDark);

  return (
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[styles.tab, selectedTab === 'products' && styles.activeTab]}
        onPress={() => onTabChange('products')}
      >
        <Package color={selectedTab === 'products' ? '#D4AF37' : (isDark ? '#A0A0A0' : '#6B7280')} size={16} />
        <Text style={[styles.tabText, selectedTab === 'products' && styles.activeTabText]}>Products</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, selectedTab === 'about' && styles.activeTab]}
        onPress={() => onTabChange('about')}
      >
        <Text style={[styles.tabText, selectedTab === 'about' && styles.activeTabText]}>About</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, selectedTab === 'gallery' && styles.activeTab]}
        onPress={() => onTabChange('gallery')}
      >
        <ImageIcon color={selectedTab === 'gallery' ? '#D4AF37' : (isDark ? '#A0A0A0' : '#6B7280')} size={16} />
        <Text style={[styles.tabText, selectedTab === 'gallery' && styles.activeTabText]}>Gallery</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, selectedTab === 'reviews' && styles.activeTab]}
        onPress={() => onTabChange('reviews')}
      >
        <MessageSquare color={selectedTab === 'reviews' ? '#D4AF37' : (isDark ? '#A0A0A0' : '#6B7280')} size={16} />
        <Text style={[styles.tabText, selectedTab === 'reviews' && styles.activeTabText]}>Reviews</Text>
      </TouchableOpacity>
    </View>
  );
}
