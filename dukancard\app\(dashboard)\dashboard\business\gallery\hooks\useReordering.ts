import { useState, useEffect } from "react";
import { arrayMove } from '@dnd-kit/sortable';
import { DragEndEvent, DragStartEvent } from '@dnd-kit/core';
import { toast } from "sonner";
import { GalleryImage } from "../types";
import { ReorderState } from "../types/galleryTypes";
import { updateGalleryOrder } from "../actions";

interface UseReorderingProps {
  images: GalleryImage[];
  updateImages: (_images: GalleryImage[]) => void;
}

export const useReordering = ({ images, updateImages }: UseReorderingProps) => {
  const [reorderState, setReorderState] = useState<ReorderState>({
    orderedImages: images,
    hasUnsavedChanges: false,
    isSavingOrder: false,
    isReordering: false,
  });
  
  const [activeId, setActiveId] = useState<string | null>(null);

  // Update ordered images when images change
  useEffect(() => {
    setReorderState(prev => ({
      ...prev,
      orderedImages: images,
      hasUnsavedChanges: false,
    }));
  }, [images]);

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
    setReorderState(prev => ({ ...prev, isReordering: true }));
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);
    setReorderState(prev => ({ ...prev, isReordering: false }));

    if (active.id !== over?.id && over) {
      const oldIndex = reorderState.orderedImages.findIndex((image) => image.id === active.id);
      const newIndex = reorderState.orderedImages.findIndex((image) => image.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newOrderedImages = arrayMove(reorderState.orderedImages, oldIndex, newIndex);
        setReorderState(prev => ({
          ...prev,
          orderedImages: newOrderedImages,
          hasUnsavedChanges: true,
        }));
      }
    }
  };

  const handleSaveOrder = async () => {
    setReorderState(prev => ({ ...prev, isSavingOrder: true }));
    try {
      const { success, error } = await updateGalleryOrder(reorderState.orderedImages);

      if (success) {
        updateImages(reorderState.orderedImages);
        setReorderState(prev => ({ ...prev, hasUnsavedChanges: false }));
        toast.success("Gallery order updated", {
          description: "Your gallery images have been reordered successfully",
        });
      } else {
        toast.error("Failed to save order", {
          description: error || "Failed to update gallery order",
        });
      }
    } catch (error) {
      console.error("Error saving gallery order:", error);
      toast.error("Failed to save order", {
        description: "An unexpected error occurred",
      });
    } finally {
      setReorderState(prev => ({ ...prev, isSavingOrder: false }));
    }
  };

  const handleResetOrder = () => {
    setReorderState(prev => ({
      ...prev,
      orderedImages: images,
      hasUnsavedChanges: false,
    }));
  };

  return {
    reorderState,
    activeId,
    handleDragStart,
    handleDragEnd,
    handleSaveOrder,
    handleResetOrder,
  };
};
