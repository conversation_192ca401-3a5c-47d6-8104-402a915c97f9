import React from "react";
import { Metadata } from "next";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle } from "lucide-react";
import { pricingPlans, PricingPlan } from "@/lib/PricingPlans";
import PlanPageWrapper from "./PlanPageWrapper";
// Removed direct Razorpay API import to avoid rate limits

// Helper function to find plan details by ID
// Note: This might be less useful now as we pass full plan objects, but keep for potential use
const getPlanDetails = (
  planId: string | null,
  cycle: "monthly" | "yearly" = "monthly"
): PricingPlan | undefined => {
  if (!planId) return undefined;
  const allPlans = pricingPlans(cycle);
  return allPlans.find((plan) => plan.id === planId);
};

// Helper function to check subscription status
type BusinessProfileInfo = {
  trial_end_date: string | null;
};

type PaymentSubscriptionInfo = {
  razorpay_subscription_id: string | null;
  subscription_status: string | null;
  subscription_start_date: string | null;
  subscription_expiry_time: string | null;
  last_payment_date: string | null;
  subscription_paused_at: string | null;
  plan_id: string | null;
  plan_cycle: string | null;
};

// Define the full set of possible subscription statuses
export type SubscriptionStatus =
  | "active"
  | "authenticated"
  | "created"
  | "pending"
  | "halted"
  | "cancelled"
  | "expired"
  | "completed"
  | "trial"
  | "inactive"
  | "paused";

async function checkSubscriptionStatus(
  profile: BusinessProfileInfo | null,
  subscription: PaymentSubscriptionInfo | null,
  _razorpayStatus: string | null
): Promise<SubscriptionStatus> {
  if (!profile) return "inactive";

  // Import centralized subscription logic - SINGLE SOURCE OF TRUTH
  const { SubscriptionStateManager, SUBSCRIPTION_STATUS } = await import('@/lib/razorpay/webhooks/handlers/utils');

  const now = new Date();
  const trialEndDate = profile.trial_end_date ? new Date(profile.trial_end_date) : null;

  // CRITICAL FIX: Use centralized logic for all subscription status determination
  // Priority order: Database status > Trial period check > Default to inactive

  // 1. Check database subscription status first (most reliable)
  if (subscription?.subscription_status) {
    const status = subscription.subscription_status;
    const planId = subscription.plan_id || 'free';

    console.log(`[PLAN_PAGE] Using database subscription status: ${status}, plan: ${planId}`);

    // Handle paused subscriptions
    if (subscription.subscription_paused_at) {
      return "paused";
    }

    // CRITICAL: Handle trial status properly
    if (status === SUBSCRIPTION_STATUS.TRIAL) {
      // Verify trial hasn't expired
      if (trialEndDate && trialEndDate > now) {
        return "trial";
      } else {
        // Trial has expired, check if they have active subscription
        const hasActiveSubscription = SubscriptionStateManager.shouldHaveActiveSubscription(status, planId);
        return hasActiveSubscription ? "active" : "inactive";
      }
    }

    // Handle free plan users
    if (planId === 'free' && status === SUBSCRIPTION_STATUS.ACTIVE) {
      return "inactive"; // Free plan users show as inactive to encourage upgrades
    }

    // Map other statuses using centralized constants
    switch (status) {
      case SUBSCRIPTION_STATUS.ACTIVE:
        return "active";
      case SUBSCRIPTION_STATUS.AUTHENTICATED:
        return "authenticated";
      case SUBSCRIPTION_STATUS.PENDING:
        return "pending";
      case SUBSCRIPTION_STATUS.HALTED:
        return "halted";
      case SUBSCRIPTION_STATUS.CANCELLED:
        return "cancelled";
      case SUBSCRIPTION_STATUS.EXPIRED:
        return "expired";
      case SUBSCRIPTION_STATUS.COMPLETED:
        return "completed";
      default:
        console.warn(`[PLAN_PAGE] Unknown subscription status: ${status}`);
        return "inactive";
    }
  }

  // 2. Check if user is in trial period (fallback for users without subscription records)
  if (trialEndDate && trialEndDate > now) {
    console.log(`[PLAN_PAGE] User is in trial period until: ${trialEndDate}`);
    return "trial";
  }

  // 3. Default to inactive (free plan or no subscription)
  console.log(`[PLAN_PAGE] No active subscription found, defaulting to inactive`);
  return "inactive";
}

// Add metadata
export const metadata: Metadata = {
  title: "Manage Plan",
  robots: "noindex, nofollow",
};

export default async function BusinessPlanPage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/login?message=Authentication required");
  }

  // Fetch profile data from business_profiles (only trial_end_date is needed)
  const { data: profile, error: profileError } = await supabase
    .from("business_profiles")
    .select("trial_end_date")
    .eq("id", user.id)
    .single();

  if (profileError || !profile) {
    // Enhanced error handling with more specific error messages
    console.error("Error fetching business profile:", profileError?.message);

    let errorTitle = "Error Fetching Plan";
    let errorMessage = "Could not load your current plan details. Please try again later or contact support.";

    // Provide more specific error messages based on the error code
    if (profileError) {
      if (profileError.code === "PGRST116") {
        errorTitle = "Business Profile Not Found";
        errorMessage = "Your business profile could not be found. Please complete the onboarding process first.";
      } else if (profileError.code === "42P01") {
        errorTitle = "Database Configuration Error";
        errorMessage = "There was an issue with the database configuration. Please contact support.";
      } else if (profileError.code === "42703") {
        errorTitle = "Database Schema Error";
        errorMessage = "There was an issue with the database schema. Please contact support.";
      }

      // Log detailed error information for debugging
      console.error("Profile error details:", {
        code: profileError.code,
        message: profileError.message,
        details: profileError.details,
        hint: profileError.hint
      });
    }

    return (
      <div className="space-y-6">
        <Card className="bg-card border border-destructive dark:border-red-700/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="w-5 h-5" /> {errorTitle}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p>{errorMessage}</p>
            {profileError && (
              <p className="mt-2 text-sm text-muted-foreground">
                Error code: {profileError.code || "Unknown"}
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Fetch subscription data from payment_subscriptions
  const { data: subscription, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select(
      "razorpay_subscription_id, subscription_status, subscription_start_date, subscription_expiry_time, subscription_charge_time, last_payment_date, last_payment_method, subscription_paused_at, plan_id, plan_cycle, cancellation_requested_at, cancelled_at"
    )
    .eq("business_profile_id", user.id)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  // Refunds are only available for payment issues or subscription problems
  // By default, users are not eligible for refunds
  const isEligibleForRefund = false;

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
  }

  // Debug: Log the subscription data to see what we're getting from the database
  console.log("[PLAN_PAGE_DEBUG] Subscription data from database:", {
    subscription_id: subscription?.razorpay_subscription_id,
    last_payment_method: subscription?.last_payment_method,
    subscription_status: subscription?.subscription_status,
    plan_id: subscription?.plan_id,
    plan_cycle: subscription?.plan_cycle
  });

  // Use only the data from Supabase to avoid Razorpay API rate limits
  // We'll use the subscription status and dates from the payment_subscriptions table

  // CRITICAL FIX: Determine subscription status using centralized logic
  const subscriptionStatus = await checkSubscriptionStatus(profile, subscription, subscription?.subscription_status || null);

  // Get plan cycle from subscription if available, default to "monthly"
  const planCycle: "monthly" | "yearly" = subscription?.plan_cycle === "yearly" ? "yearly" : "monthly";

  // Get current plan details from subscription
  const currentPlanId = subscription?.plan_id;
  const currentPlanDetails = getPlanDetails(currentPlanId, planCycle);

  // Get all plans for display
  const monthlyPlans = pricingPlans("monthly");
  const yearlyPlans = pricingPlans("yearly");

  // Calculate next billing date
  let nextBillingDate: string | null = null;

  try {
    if (subscriptionStatus === "active" && subscription?.subscription_charge_time) {
      // For active subscriptions, use subscription_charge_time (next billing date)
      nextBillingDate = new Date(subscription.subscription_charge_time).toLocaleString(
        "en-IN",
        {
          year: "numeric",
          month: "long",
          day: "numeric",
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        }
      );
    } else if (
      subscriptionStatus === "authenticated" &&
      subscription?.subscription_start_date
    ) {
      // For authenticated subscriptions, use subscription_start_date (when subscription will start)
      nextBillingDate = new Date(subscription.subscription_start_date).toLocaleString(
        "en-IN",
        {
          year: "numeric",
          month: "long",
          day: "numeric",
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        }
      );
    } else if (subscription?.subscription_expiry_time) {
      // Fall back to database expiry time
      nextBillingDate = new Date(subscription.subscription_expiry_time).toLocaleString(
        "en-IN",
        {
          year: "numeric",
          month: "long",
          day: "numeric",
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        }
      );
    }
  } catch (error) {
    console.error("Error formatting next billing date:", error);
    nextBillingDate = null;
  }

  // Get current subscription ID
  const currentSubscriptionId = subscription?.razorpay_subscription_id || null;

  // For authenticated subscriptions, use subscription_start_date
  const authenticatedSubscriptionStartDate =
    subscriptionStatus === "authenticated"
      ? subscription?.subscription_start_date
      : null;

  // Get subscription end date
  const subscriptionEndDate = subscription?.subscription_expiry_time || null;

  // Get cancellation dates from subscription
  const cancellationRequestedAt = subscription?.cancellation_requested_at || null;
  const cancelledAt = subscription?.cancelled_at || null;

  return (
    <PlanPageWrapper
      userId={user.id}
      currentPlanDetails={currentPlanDetails}
      subscriptionStatus={subscriptionStatus}
      trialEndDate={profile.trial_end_date}
      subscriptionEndDate={subscriptionEndDate}
      monthlyPlans={monthlyPlans}
      yearlyPlans={yearlyPlans}
      currentSubscriptionId={currentSubscriptionId}
      nextBillingDate={nextBillingDate}
      cancellationRequestedAt={cancellationRequestedAt}
      cancelledAt={cancelledAt}
      planCycle={planCycle}
      authenticatedSubscriptionStartDate={authenticatedSubscriptionStartDate}
      // Pass subscription data from Supabase instead of Razorpay
      subscriptionStartDate={subscription?.subscription_start_date || null}
      subscriptionExpiryTime={subscription?.subscription_expiry_time || null}
      subscriptionChargeTime={subscription?.subscription_charge_time || null}
      isEligibleForRefund={isEligibleForRefund}
      // Pass payment method from database
      lastPaymentMethod={subscription?.last_payment_method || null}
      // Pass razorpay subscription ID to determine switch vs fresh subscription
      razorpaySubscriptionId={subscription?.razorpay_subscription_id || null}
    />
  );
}
