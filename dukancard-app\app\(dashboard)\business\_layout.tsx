import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useNetworkStatus } from '@/lib/utils/networkStatus';
import { OfflineBanner } from '@/components/ui/OfflineComponents';
import UnifiedBottomNavigation from '@/components/shared/navigation/UnifiedBottomNavigation';
import { useAuth } from '@/contexts/AuthContext';
import { useBusinessProfile } from '@/contexts/UserDataContext';

// Import screen components
import BusinessFeedScreen from './index';
import DiscoverScreen from '@/components/shared/screens/DiscoverScreen';
import BusinessProfileScreen from './profile'

export default function BusinessTabLayout() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const networkStatus = useNetworkStatus();
  const { user } = useAuth();
  const { businessProfile } = useBusinessProfile();
  const [activeTab, setActiveTab] = useState('home');

  // Use display name from profile context
  const businessName = businessProfile?.business_name || 'Business User';

  const renderActiveScreen = () => {
    switch (activeTab) {
      case 'home':
        return <BusinessFeedScreen />;
      case 'discover':
        return <DiscoverScreen userName={businessName} showNotifications={true} />;
      case 'profile':
        return <BusinessProfileScreen />;
      default:
        return <BusinessFeedScreen />;
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <View style={styles.container}>
      {/* Offline Banner */}
      <OfflineBanner visible={!networkStatus.isConnected} />

      <View style={styles.screenContainer}>
        {renderActiveScreen()}
      </View>

      <UnifiedBottomNavigation
        activeTab={activeTab}
        onTabChange={handleTabChange}
        showQRScanner={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  screenContainer: {
    flex: 1,
  },
});