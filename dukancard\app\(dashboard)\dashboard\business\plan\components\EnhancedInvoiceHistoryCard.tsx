"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, Card<PERSON>eader, CardT<PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Receipt, Download, FileText, Calendar, RefreshCw } from "lucide-react";
import { createClient } from "@/utils/supabase/client";
import { formatDate, cn } from "@/lib/utils";
import { toast } from "sonner";
import { SubscriptionStateManager } from "@/lib/razorpay/webhooks/handlers/utils";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

// Define types for invoice data from Razorpay
interface InvoiceItem {
  id: string;
  entity: string;
  receipt: string | null;
  invoice_number: string | null;
  customer_id: string;
  customer_details?: {
    id: string;
    name?: string | null;
    email: string;
    contact: string;
    gstin?: string | null;
    billing_address?: Record<string, unknown> | null;
    shipping_address?: Record<string, unknown> | null;
    customer_name?: string | null;
    customer_email: string;
    customer_contact: string;
  };
  order_id?: string;
  subscription_id?: string;
  status: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  description: string | null;
  short_url: string;
  date: number;
  paid_at: number | null;
  created_at: number;
  line_items?: Array<{
    id: string;
    name: string;
    amount: number;
    currency: string;
    quantity: number;
    type: string;
  }>;
}

interface EnhancedInvoiceHistoryCardProps {
  subscriptionId?: string | null;
  planId?: string; // Added planId to check if user is on free plan
}

// Define pagination metadata interface
interface PaginationMetadata {
  page: number;
  count: number;
  totalCount: number;
  totalPages: number;
}

export default function EnhancedInvoiceHistoryCard({
  subscriptionId,
  planId = ""
}: EnhancedInvoiceHistoryCardProps = {}) {
  const [invoices, setInvoices] = useState<InvoiceItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const [paginationMetadata, setPaginationMetadata] = useState<PaginationMetadata>({
    page: 1,
    count: itemsPerPage,
    totalCount: 0,
    totalPages: 0
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
  };

  // Fetch invoices using subscription ID with pagination
  const fetchInvoices = useCallback(async (subId: string, page: number = 1) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/subscriptions/${subId}/invoices?page=${page}&count=${itemsPerPage}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch invoices");
      }

      if (data.success && data.invoices && data.invoices.items) {
        setInvoices(data.invoices.items);

        // Update pagination metadata
        if (data.pagination) {
          setPaginationMetadata(data.pagination);
        } else {
          // Fallback if pagination metadata is not provided
          setPaginationMetadata({
            page,
            count: itemsPerPage,
            totalCount: data.invoices.count || 0,
            totalPages: Math.ceil((data.invoices.count || 0) / itemsPerPage)
          });
        }
      } else if (data.success && data.invoices && Array.isArray(data.invoices)) {
        // Handle case where invoices might be returned directly as an array
        setInvoices(data.invoices);

        // Set basic pagination metadata
        setPaginationMetadata({
          page,
          count: itemsPerPage,
          totalCount: data.invoices.length,
          totalPages: Math.ceil(data.invoices.length / itemsPerPage)
        });
      } else {
        setInvoices([]);
        console.log("[RAZORPAY_DEBUG] No invoices found or unexpected response format:", data);

        // Reset pagination metadata
        setPaginationMetadata({
          page: 1,
          count: itemsPerPage,
          totalCount: 0,
          totalPages: 0
        });
      }
    } catch (err) {
      console.error("[RAZORPAY_ERROR] Error fetching invoices:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch invoices");
    } finally {
      setIsLoading(false);
    }
  }, [itemsPerPage]);

  // Fetch subscription ID if not provided
  useEffect(() => {
    const initializeInvoices = async () => {
      // Import centralized manager for consistent logic
      const { SubscriptionStateManager } = await import('@/lib/razorpay/webhooks/handlers/utils');

      // Skip fetching invoices for free plan users using centralized logic
      if (SubscriptionStateManager.isFreeStatus('', planId)) {
        setIsLoading(false);
        setInvoices([]);
        return;
      }

      const fetchSubscriptionId = async () => {
        if (subscriptionId) return;

        try {
          const supabase = createClient();
          const { data: subscriptions, error: subscriptionError } = await supabase
            .from("payment_subscriptions")
            .select("razorpay_subscription_id, plan_id")
            .eq("subscription_status", "active")
            .limit(1);

          if (subscriptionError) throw subscriptionError;

          // Skip fetching invoices if the user is on a free plan using centralized logic
          if (subscriptions && subscriptions.length > 0 && SubscriptionStateManager.isFreeStatus('', subscriptions[0].plan_id)) {
            setIsLoading(false);
            return;
          }

          if (subscriptions && subscriptions.length > 0 && subscriptions[0].razorpay_subscription_id) {
            fetchInvoices(subscriptions[0].razorpay_subscription_id, currentPage);
          } else {
            setIsLoading(false);
          }
        } catch (err) {
          console.error("[RAZORPAY_ERROR] Error fetching subscription ID:", err);
          setError(err instanceof Error ? err.message : "Failed to fetch subscription details");
          setIsLoading(false);
        }
      };

      if (!subscriptionId) {
        fetchSubscriptionId();
      } else {
        fetchInvoices(subscriptionId, currentPage);
      }
    };

    initializeInvoices();
  }, [subscriptionId, fetchInvoices, currentPage, planId]);

  // Format currency
  const formatCurrency = (amount: number, currency: string) => {
    const formatter = new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: currency || "INR",
      minimumFractionDigits: 2,
    });
    return formatter.format(amount / 100); // Razorpay amounts are in paise/cents
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    if (page < 1 || page > paginationMetadata.totalPages || page === currentPage) return;
    setCurrentPage(page);

    if (subscriptionId) {
      fetchInvoices(subscriptionId, page);
    }
  };

  return (
    <Card className="border shadow-sm transition-all duration-300 hover:shadow-md h-full bg-white dark:bg-black">
      <CardHeader className="bg-gradient-to-br from-transparent to-blue-50/50 dark:to-blue-900/10">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <motion.div
              whileHover={{
                rotate: [0, -10, 10, -5, 0],
                transition: { duration: 0.5 },
              }}
              className="p-2 rounded-full bg-blue-100/50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400"
            >
              <Receipt className="w-5 h-5" />
            </motion.div>
            <div>
              <CardTitle>Invoice History</CardTitle>
              <CardDescription>
                View and download your invoice history
              </CardDescription>
            </div>
          </div>
          {subscriptionId && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                fetchInvoices(subscriptionId, currentPage);
                toast.info("Refreshing invoice history...");
              }}
              disabled={isLoading}
              className="bg-white dark:bg-black/20 border-blue-200 dark:border-blue-800/50 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200"
            >
              <RefreshCw
                className={cn(
                  "w-4 h-4 mr-2 text-blue-600 dark:text-blue-400",
                  isLoading && "animate-spin"
                )}
              />
              Refresh
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <motion.div variants={itemVariants} className="p-4 sm:p-6">
          {isLoading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-4 py-4"
            >
              <motion.div
                className="flex items-center justify-center mb-6"
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <RefreshCw className="w-6 h-6 text-blue-500" />
              </motion.div>
              <div className="h-10 w-full bg-blue-100/50 dark:bg-blue-900/20 rounded-md animate-pulse" />
              <div className="h-16 w-full bg-blue-100/30 dark:bg-blue-900/10 rounded-md animate-pulse" />
              <div className="h-16 w-full bg-blue-100/20 dark:bg-blue-900/5 rounded-md animate-pulse" />
            </motion.div>
          ) : error ? (
            <motion.div
              key="error"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex flex-col items-center justify-center py-8 text-center"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                className="bg-red-50 dark:bg-red-900/20 p-4 rounded-full mb-4"
              >
                <FileText className="w-8 h-8 text-red-500 dark:text-red-400" />
              </motion.div>
              <h3 className="text-lg font-medium mb-2 text-red-600 dark:text-red-400">Error Loading Invoices</h3>
              <p className="text-muted-foreground max-w-sm mb-4">{error}</p>
              {subscriptionId && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    fetchInvoices(subscriptionId, currentPage);
                    toast.info("Retrying invoice fetch...");
                  }}
                  className="bg-white dark:bg-black/20 border-red-200 dark:border-red-800/50 hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
              )}
            </motion.div>
          ) : invoices.length === 0 ? (
            <motion.div
              key="empty"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex flex-col items-center justify-center py-10 text-center"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-full mb-4"
              >
                <Receipt className="w-10 h-10 text-blue-500 dark:text-blue-400" />
              </motion.div>
              <h3 className="text-lg font-medium mb-2">No Invoices Yet</h3>
              <p className="text-muted-foreground max-w-sm">
                {SubscriptionStateManager.isFreeStatus('', planId)
                  ? "Free plan users don't have invoices. Upgrade to a paid plan to see invoices here."
                  : "Invoices will appear here after your first payment"}
              </p>
            </motion.div>
          ) : (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-800"
            >
              <Table>
                <TableHeader className="bg-gray-50 dark:bg-black/90">
                  <TableRow>
                    <TableHead className="font-medium">Invoice #</TableHead>
                    <TableHead className="font-medium">Date</TableHead>
                    <TableHead className="font-medium">Amount</TableHead>
                    <TableHead className="font-medium">Status</TableHead>
                    <TableHead className="font-medium text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoices.map((invoice, index) => (
                    <motion.tr
                      key={invoice.id}
                      variants={itemVariants}
                      className={cn(
                        "transition-colors hover:bg-blue-50/50 dark:hover:bg-blue-900/10",
                        index % 2 === 0
                          ? "bg-white dark:bg-black"
                          : "bg-blue-50/20 dark:bg-black/80"
                      )}
                    >
                      <TableCell className="font-medium text-blue-700 dark:text-blue-300">
                        {invoice.id.replace("inv_", "#")}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-blue-500 dark:text-blue-400" />
                          <span>
                            {formatDate(new Date(invoice.date * 1000))}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="font-semibold">
                        {formatCurrency(invoice.amount, invoice.currency)}
                      </TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            invoice.status === "paid"
                              ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                              : invoice.status === "pending" || invoice.status === "issued"
                              ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                              : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                          }`}
                        >
                          {invoice.status.charAt(0).toUpperCase() +
                            invoice.status.slice(1)}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        {invoice.short_url ? (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 px-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                            onClick={() =>
                              window.open(invoice.short_url, "_blank")
                            }
                          >
                            <Download className="h-4 w-4 mr-1" />
                            <span className="sr-only sm:not-sr-only sm:inline-block">
                              View
                            </span>
                          </Button>
                        ) : (
                          <span className="text-xs text-muted-foreground">
                            Not available
                          </span>
                        )}
                      </TableCell>
                    </motion.tr>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {paginationMetadata.totalPages > 1 && (
                <div className="flex justify-center mt-4 pb-2">
                  <Pagination>
                    <PaginationContent>
                      {currentPage > 1 && (
                        <PaginationItem>
                          <PaginationPrevious
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              handlePageChange(currentPage - 1);
                            }}
                            className="border-gray-200 dark:border-gray-800 hover:border-gray-400 dark:hover:border-gray-600 transition-colors"
                          />
                        </PaginationItem>
                      )}

                      {/* First page */}
                      {currentPage > 2 && (
                        <PaginationItem>
                          <PaginationLink
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              handlePageChange(1);
                            }}
                            className={currentPage === 1 ? "bg-gray-100 dark:bg-gray-900" : ""}
                          >
                            1
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      {/* Ellipsis */}
                      {currentPage > 3 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      {/* Previous page */}
                      {currentPage > 1 && (
                        <PaginationItem>
                          <PaginationLink
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              handlePageChange(currentPage - 1);
                            }}
                          >
                            {currentPage - 1}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      {/* Current page */}
                      <PaginationItem>
                        <PaginationLink
                          href="#"
                          isActive
                          onClick={(e) => e.preventDefault()}
                        >
                          {currentPage}
                        </PaginationLink>
                      </PaginationItem>

                      {/* Next page */}
                      {currentPage < paginationMetadata.totalPages && (
                        <PaginationItem>
                          <PaginationLink
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              handlePageChange(currentPage + 1);
                            }}
                          >
                            {currentPage + 1}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      {/* Ellipsis */}
                      {currentPage < paginationMetadata.totalPages - 2 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      {/* Last page */}
                      {currentPage < paginationMetadata.totalPages - 1 && (
                        <PaginationItem>
                          <PaginationLink
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              handlePageChange(paginationMetadata.totalPages);
                            }}
                          >
                            {paginationMetadata.totalPages}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      {currentPage < paginationMetadata.totalPages && (
                        <PaginationItem>
                          <PaginationNext
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              handlePageChange(currentPage + 1);
                            }}
                            className="border-gray-200 dark:border-gray-800 hover:border-gray-400 dark:hover:border-gray-600 transition-colors"
                          />
                        </PaginationItem>
                      )}
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </motion.div>
          )}
        </motion.div>
      </CardContent>

      {/* Premium footer with helpful information */}
      {invoices.length > 0 && (
        <CardFooter className="bg-white dark:bg-black border-t border-gray-200 dark:border-gray-800 py-3 px-6 text-xs text-muted-foreground">
          <div className="flex items-center gap-2">
            <motion.div
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
              className="text-blue-500 dark:text-blue-400"
            >
              <Receipt className="w-4 h-4" />
            </motion.div>
            <span>
              Invoices are generated automatically by Razorpay when payments are processed.
            </span>
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
