import { cn } from "@/lib/utils";

// Common form styles to be reused across form components
export const formStyles = {
  inputClasses: cn(
    "bg-white dark:bg-neutral-800 border-neutral-300 dark:border-neutral-600",
    "text-neutral-900 dark:text-white placeholder:text-neutral-400 dark:placeholder:text-neutral-500",
    "focus-visible:ring-offset-0 focus-visible:ring-1 focus-visible:ring-primary/50 dark:focus-visible:ring-primary/50",
    "transition-all duration-200 ease-in-out"
  ),

  labelClasses: "text-neutral-700 dark:text-neutral-300 font-medium",

  descriptionClasses: "text-xs text-neutral-500 dark:text-neutral-500",

  sectionClasses: cn(
    "p-6 bg-white dark:bg-black border border-neutral-200/80 dark:border-neutral-800/80 rounded-xl",
    "shadow-sm hover:shadow-md transition-all duration-300 ease-in-out"
  ),

  headingClasses: cn(
    "text-lg font-semibold mb-6 text-neutral-900 dark:text-white flex items-center gap-2",
    "after:content-[''] after:h-[1px] after:flex-grow after:bg-neutral-200 dark:after:bg-neutral-800"
  )
};
