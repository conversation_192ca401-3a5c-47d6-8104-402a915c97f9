"use client";

import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import PolicyBackground from "./PolicyBackground";

interface PolicyHeroSectionProps {
  title: string;
  lastUpdated: string;
  variant?: "default" | "gold" | "blue" | "purple";
}

export default function PolicyHeroSection({
  title,
  lastUpdated,
  variant = "gold",
}: PolicyHeroSectionProps) {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  const titleGlowVariants = {
    hidden: { textShadow: "0px 0px 0px rgba(0, 0, 0, 0)" },
    visible: {
      textShadow: [
        "0px 0px 0px rgba(var(--brand-gold-rgb), 0)",
        "0px 0px 10px rgba(var(--brand-gold-rgb), 0.3)",
        "0px 0px 0px rgba(var(--brand-gold-rgb), 0)",
      ],
      transition: {
        duration: 3,
        repeat: Infinity,
        repeatType: "reverse" as const,
      },
    },
  };

  return (
    <section className="relative w-full pt-24 pb-8 md:pt-32 md:pb-12 overflow-hidden">
      {/* Background */}
      <PolicyBackground variant={variant} intensity="low" />

      <div className="container mx-auto px-4 max-w-4xl">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="relative z-10"
        >
          {/* Back button */}
          <motion.div variants={itemVariants}>
            <Button
              variant="ghost"
              asChild
              className="mb-6 hover:bg-transparent hover:text-[var(--brand-gold)] cursor-pointer"
            >
              <Link href="/">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Link>
            </Button>
          </motion.div>

          {/* Title with glow effect */}
          <motion.h1
            variants={itemVariants}
            className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-center"
          >
            <motion.span
              variants={titleGlowVariants}
              className="inline-block"
            >
              {title}
            </motion.span>
          </motion.h1>

          {/* Last updated date */}
          <motion.p
            variants={itemVariants}
            className="text-muted-foreground text-center mb-8"
          >
            Last Updated: {lastUpdated}
          </motion.p>

          {/* Separator */}
          <motion.div variants={itemVariants}>
            <Separator className="mb-8" />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
