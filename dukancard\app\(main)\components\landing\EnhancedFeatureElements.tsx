"use client";

import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import {
  Share2,
  QrCode,
  MapPin,
  ShoppingBag,
  MessageCircle,
  Phone,
  Star,
  Instagram,
  Facebook,
  Globe,
  Heart,
  UserPlus,
  Mail,
  CreditCard,
  Sparkles,
  Clock,
  Palette,
  Image,
  Scan,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface EnhancedFeatureElementsProps {
  minimal?: boolean;
}

export default function EnhancedFeatureElements({ minimal = false }: EnhancedFeatureElementsProps) {
  // Client-side only rendering
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Define features with responsive positioning - firefly style
  const features = [
    // Original features with updated positions
    {
      id: 1,
      icon: Share2,
      label: "Easily Shareable",
      description: "Share your digital card via link, QR code, or social media",
      top: minimal ? "5%" : "10%",
      left: minimal ? "-3%" : "-5%",
      delay: 0.1,
      size: "normal",
    },
    {
      id: 2,
      icon: QrCode,
      label: "QR Code",
      description: "Scan to instantly view the business card on any device",
      top: minimal ? "15%" : "25%",
      right: minimal ? "-3%" : "-5%",
      delay: 0.2,
      size: "normal",
    },
    {
      id: 3,
      icon: MapPin,
      label: "Location",
      description: "Find the business location with integrated maps",
      bottom: minimal ? "20%" : "30%",
      left: minimal ? "-3%" : "-5%",
      delay: 0.3,
      size: "normal",
    },
    {
      id: 4,
      icon: ShoppingBag,
      label: "Products & Services",
      description: "Browse available products and services with pricing",
      bottom: minimal ? "10%" : "15%",
      right: minimal ? "-3%" : "-5%",
      delay: 0.4,
      size: "normal",
    },
    {
      id: 5,
      icon: MessageCircle,
      label: "Direct Contact",
      description: "Message the business directly through WhatsApp",
      bottom: minimal ? "-3%" : "-5%",
      left: minimal ? "20%" : "30%",
      delay: 0.5,
      size: "normal",
    },
    {
      id: 6,
      icon: Phone,
      label: "One-Click Call",
      description: "Call the business with a single tap",
      top: minimal ? "-3%" : "-5%",
      right: minimal ? "20%" : "30%",
      delay: 0.6,
      size: "normal",
    },
    {
      id: 7,
      icon: Star,
      label: "Reviews",
      description: "See ratings and reviews from other customers",
      top: minimal ? "40%" : "50%",
      right: minimal ? "-3%" : "-5%",
      delay: 0.7,
      size: "normal",
    },

    // Additional features - firefly style
    {
      id: 8,
      icon: Instagram,
      label: "Social Media",
      description: "Connect with the business on Instagram",
      top: minimal ? "25%" : "35%",
      left: minimal ? "5%" : "10%",
      delay: 0.8,
      size: "small",
    },
    {
      id: 9,
      icon: Facebook,
      label: "Facebook Page",
      description: "Visit the business Facebook page",
      top: minimal ? "60%" : "70%",
      right: minimal ? "10%" : "15%",
      delay: 0.9,
      size: "small",
    },
    {
      id: 10,
      icon: Globe,
      label: "Website",
      description: "Visit the business website for more information",
      bottom: minimal ? "40%" : "50%",
      right: minimal ? "15%" : "20%",
      delay: 1.0,
      size: "small",
    },
    {
      id: 11,
      icon: Heart,
      label: "Like",
      description: "Like the business card to show your appreciation",
      top: minimal ? "30%" : "40%",
      left: minimal ? "15%" : "20%",
      delay: 1.1,
      size: "tiny",
    },
    {
      id: 12,
      icon: UserPlus,
      label: "Subscribe",
      description: "Subscribe to receive updates from this business",
      bottom: minimal ? "30%" : "40%",
      left: minimal ? "10%" : "15%",
      delay: 1.2,
      size: "small",
    },
    {
      id: 13,
      icon: Mail,
      label: "Email",
      description: "Contact the business via email",
      top: minimal ? "70%" : "80%",
      left: minimal ? "5%" : "10%",
      delay: 1.3,
      size: "tiny",
    },
    {
      id: 14,
      icon: CreditCard,
      label: "Digital Card",
      description: "Professional digital business card",
      top: minimal ? "50%" : "60%",
      left: minimal ? "-5%" : "-10%",
      delay: 1.4,
      size: "small",
    },
    {
      id: 15,
      icon: Sparkles,
      label: "Premium Features",
      description: "Access premium features with subscription plans",
      bottom: minimal ? "50%" : "60%",
      right: minimal ? "5%" : "10%",
      delay: 1.5,
      size: "tiny",
    },
    {
      id: 16,
      icon: Clock,
      label: "Business Hours",
      description: "View the business operating hours",
      top: minimal ? "10%" : "15%",
      right: minimal ? "15%" : "20%",
      delay: 1.6,
      size: "tiny",
    },

    {
      id: 17,
      icon: Palette,
      label: "Customization",
      description: "Customize your card with themes and colors",
      top: minimal ? "20%" : "30%",
      right: minimal ? "25%" : "30%",
      delay: 1.7,
      size: "tiny",
    },
    {
      id: 18,
      icon: Image,
      label: "Gallery",
      description: "View business photos and product images",
      bottom: minimal ? "25%" : "35%",
      left: minimal ? "25%" : "30%",
      delay: 1.8,
      size: "tiny",
    },
    {
      id: 19,
      icon: Scan,
      label: "Scan",
      description: "Scan QR code to view business details",
      top: minimal ? "80%" : "85%",
      right: minimal ? "10%" : "15%",
      delay: 1.9,
      size: "tiny",
    },
  ];

  // For minimal mode, show a subset of features but still include firefly effect
  const displayFeatures = minimal
    ? features.filter(f => [1, 2, 4, 6, 8, 9, 11, 13, 15, 17, 19].includes(f.id))
    : features;

  return (
    <div className="relative w-full h-full">
      <TooltipProvider>
        {isClient
          ? displayFeatures.map((feature) => {
              // Calculate unique animation offsets based on feature ID
              const animationOffset = (feature.id * 0.5) % 2;
              const positionStyle: React.CSSProperties = {
                ...(feature.top !== undefined ? { top: feature.top } : {}),
                ...(feature.right !== undefined ? { right: feature.right } : {}),
                ...(feature.bottom !== undefined
                  ? { bottom: feature.bottom }
                  : {}),
                ...(feature.left !== undefined ? { left: feature.left } : {}),
              };

              return (
                <Tooltip key={feature.id}>
                  <TooltipTrigger asChild>
                    <motion.div
                      className="absolute z-10"
                      style={positionStyle}
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{
                        type: "spring",
                        stiffness: 260,
                        damping: 20,
                        delay: feature.delay,
                      }}
                      whileHover={{ scale: 1.1 }}
                    >
                      <motion.div
                        className={`flex items-center justify-center rounded-full shadow-md ${
                          feature.size === "tiny"
                            ? "w-6 h-6 md:w-7 md:h-7"
                            : feature.size === "small"
                              ? "w-7 h-7 md:w-8 md:h-8"
                              : "w-8 h-8 md:w-10 md:h-10"
                        } ${feature.size === "tiny" ? "bg-white/80 dark:bg-neutral-800/80" : "bg-white dark:bg-neutral-800"}`}
                        style={{
                          boxShadow: "0 0 10px rgba(var(--brand-gold-rgb), 0.3), 0 0 20px rgba(var(--brand-gold-rgb), 0.1)"
                        }}
                        whileHover={{
                          boxShadow: "0 0 15px var(--brand-gold), 0 0 30px rgba(var(--brand-gold-rgb), 0.3)",
                          scale: 1.1,
                        }}
                        animate={{
                          // Firefly-like random movement
                          x: [
                            0,
                            feature.id % 2 === 0 ? feature.id % 5 : -feature.id % 5,
                            feature.id % 3 === 0 ? -feature.id % 4 : feature.id % 4,
                            0
                          ],
                          y: [
                            0,
                            feature.id % 3 === 0 ? feature.id % 5 : -feature.id % 5,
                            feature.id % 2 === 0 ? -feature.id % 4 : feature.id % 4,
                            0
                          ],
                          scale: [1, 1.05, 0.98, 1],
                          opacity: [0.9, 1, 0.95, 0.9],
                          boxShadow: [
                            "0 0 10px rgba(var(--brand-gold-rgb), 0.2), 0 0 20px rgba(var(--brand-gold-rgb), 0.1)",
                            "0 0 15px rgba(var(--brand-gold-rgb), 0.4), 0 0 25px rgba(var(--brand-gold-rgb), 0.2)",
                            "0 0 12px rgba(var(--brand-gold-rgb), 0.3), 0 0 22px rgba(var(--brand-gold-rgb), 0.15)",
                            "0 0 10px rgba(var(--brand-gold-rgb), 0.2), 0 0 20px rgba(var(--brand-gold-rgb), 0.1)"
                          ]
                        }}
                        transition={{
                          // Varied durations for more natural movement
                          duration: 4 + (feature.id % 3),
                          delay: animationOffset,
                          repeat: Infinity,
                          repeatType: "loop",
                          ease: "easeInOut",
                          times: [0, 0.3, 0.7, 1]
                        }}
                      >
                        {React.createElement(feature.icon, {
                          className: `${
                            feature.size === "tiny"
                              ? "w-3 h-3 md:w-4 md:h-4"
                              : feature.size === "small"
                                ? "w-3.5 h-3.5 md:w-4.5 md:h-4.5"
                                : "w-4 h-4 md:w-5 md:h-5"
                          } text-[var(--brand-gold)]`,
                          strokeWidth: feature.size === "tiny" ? 2 : 1.5
                        })}
                      </motion.div>

                      {/* Firefly glow effect */}
                      <div className="absolute inset-0 overflow-visible rounded-full">
                        {/* Firefly glow trails - different for each size */}
                        {feature.size === "tiny" ? (
                          // Tiny icons have a simple glow
                          <motion.div
                            className="absolute inset-0 rounded-full bg-[var(--brand-gold)]/20 dark:bg-[var(--brand-gold)]/30 blur-[2px]"
                            animate={{
                              scale: [1, 1.5, 1],
                              opacity: [0.5, 0.8, 0.5]
                            }}
                            transition={{
                              duration: 2 + (feature.id % 2),
                              repeat: Infinity,
                              repeatType: "loop",
                              ease: "easeInOut",
                              delay: feature.id * 0.1
                            }}
                          />
                        ) : feature.size === "small" ? (
                          // Small icons have corner glows
                          <>
                            {/* Top-left and bottom-right corners for small icons */}
                            <motion.div
                              className="absolute -top-1 -left-1 w-2/3 h-2/3 rounded-br-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]"
                              animate={{
                                scale: [0, 1.3],
                                opacity: [0.7, 0]
                              }}
                              transition={{
                                duration: 2.5,
                                repeat: Infinity,
                                repeatType: "loop",
                                ease: "easeOut",
                                delay: feature.id * 0.1,
                                repeatDelay: 0.2
                              }}
                            />
                            <motion.div
                              className="absolute -bottom-1 -right-1 w-2/3 h-2/3 rounded-tl-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]"
                              animate={{
                                scale: [0, 1.3],
                                opacity: [0.7, 0]
                              }}
                              transition={{
                                duration: 2.5,
                                repeat: Infinity,
                                repeatType: "loop",
                                ease: "easeOut",
                                delay: feature.id * 0.1 + 1.25,
                                repeatDelay: 0.2
                              }}
                            />
                          </>
                        ) : (
                          // Normal icons have all four corner glows
                          <>
                            {/* Top-left corner pulse */}
                            <motion.div
                              className="absolute -top-1 -left-1 w-1/2 h-1/2 rounded-br-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]"
                              animate={{
                                scale: [0, 1.5],
                                opacity: [0.8, 0]
                              }}
                              transition={{
                                duration: 3,
                                repeat: Infinity,
                                repeatType: "loop",
                                ease: "easeOut",
                                delay: feature.id * 0.1,
                                repeatDelay: 0.3
                              }}
                            />

                            {/* Top-right corner pulse */}
                            <motion.div
                              className="absolute -top-1 -right-1 w-1/2 h-1/2 rounded-bl-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]"
                              animate={{
                                scale: [0, 1.5],
                                opacity: [0.8, 0]
                              }}
                              transition={{
                                duration: 3,
                                repeat: Infinity,
                                repeatType: "loop",
                                ease: "easeOut",
                                delay: feature.id * 0.1 + 0.75,
                                repeatDelay: 0.3
                              }}
                            />

                            {/* Bottom-left corner pulse */}
                            <motion.div
                              className="absolute -bottom-1 -left-1 w-1/2 h-1/2 rounded-tr-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]"
                              animate={{
                                scale: [0, 1.5],
                                opacity: [0.8, 0]
                              }}
                              transition={{
                                duration: 3,
                                repeat: Infinity,
                                repeatType: "loop",
                                ease: "easeOut",
                                delay: feature.id * 0.1 + 1.5,
                                repeatDelay: 0.3
                              }}
                            />

                            {/* Bottom-right corner pulse */}
                            <motion.div
                              className="absolute -bottom-1 -right-1 w-1/2 h-1/2 rounded-tl-full bg-[var(--brand-gold)]/15 dark:bg-[var(--brand-gold)]/25 blur-[1px]"
                              animate={{
                                scale: [0, 1.5],
                                opacity: [0.8, 0]
                              }}
                              transition={{
                                duration: 3,
                                repeat: Infinity,
                                repeatType: "loop",
                                ease: "easeOut",
                                delay: feature.id * 0.1 + 2.25,
                                repeatDelay: 0.3
                              }}
                            />
                          </>
                        )}
                      </div>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent
                    side="top"
                    className="max-w-[200px] z-50 bg-white/80 dark:bg-neutral-800/90 backdrop-blur-sm border border-[var(--brand-gold)]/30"
                  >
                    <div>
                      <p className="font-semibold text-[var(--brand-gold)]">
                        {feature.label}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {feature.description}
                      </p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              );
            })
          : null}
      </TooltipProvider>
    </div>
  );
}
