import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import SettingsPageClient from './components/SettingsPageClient';

import { cleanPhoneFromAuth } from '@/lib/utils';

export const metadata: Metadata = {
  title: 'Account Settings - Dukancard',
  description: 'Manage your Dukancard customer account settings.',
  robots: 'noindex, nofollow',
};

export default async function CustomerSettingsPage() {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    redirect('/login?message=Authentication required');
  }

  // Settings page is exempt from address validation
  // Users can access settings even without complete address

  // Clean phone number from auth format
  const cleanedPhone = cleanPhoneFromAuth(user?.phone);

  // Determine user registration type
  const isGoogleUser = user.app_metadata?.provider === "google";
  const hasEmail = !!user.email;
  const hasPhone = !!cleanedPhone;

  // Determine registration method
  let registrationType: 'google' | 'email' | 'phone' = 'email'; // default

  if (isGoogleUser) {
    registrationType = 'google';
  } else if (hasPhone && !hasEmail) {
    registrationType = 'phone';
  } else if (hasEmail) {
    registrationType = 'email';
  }

  return (
    <SettingsPageClient
      currentEmail={user?.email}
      currentPhone={cleanedPhone}
      registrationType={registrationType}
    />
  );
}
