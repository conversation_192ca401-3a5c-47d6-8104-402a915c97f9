import React, { useState, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, Modal } from 'react-native';
import { Search, X, SortAsc } from 'lucide-react-native';
import { BusinessProduct, ProductSortOption } from '../../lib/services/businessCardDataService';
import { ProductCard } from '../shared/ui';
import { LoadingSpinner } from '../shared/ui/LoadingSpinner';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface ProductsTabProps {
  products: BusinessProduct[];
  businessId: string;
  isDark: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  searchQuery: string;
  sortBy: ProductSortOption;
  onLoadMore: () => void;
  onSearch: (query: string) => void;
  onSort: (sortBy: ProductSortOption) => void;
}

export default function ProductsTab({
  products,
  businessId,
  isDark,
  loadingMore,
  hasMore,
  searchQuery,
  sortBy,
  onLoadMore,
  onSearch,
  onSort,
}: ProductsTabProps) {
  const styles = createPublicCardViewStyles(isDark);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [showSortModal, setShowSortModal] = useState(false);
  const searchTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Handle search input change with debouncing
  const handleSearchChange = (text: string) => {
    setLocalSearchQuery(text);

    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      onSearch(text);
    }, 500); // 500ms debounce delay
  };

  // Handle clear search
  const handleClearSearch = () => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    setLocalSearchQuery('');
    onSearch('');
  };

  // Handle sort selection
  const handleSortSelect = (newSortBy: ProductSortOption) => {
    onSort(newSortBy);
    setShowSortModal(false);
  };

  // Get sort label
  const getSortLabel = (sort: ProductSortOption) => {
    switch (sort) {
      case 'newest': return 'Newest First';
      case 'price_low': return 'Price: Low to High';
      case 'price_high': return 'Price: High to Low';
      case 'name_asc': return 'Name: A to Z';
      case 'name_desc': return 'Name: Z to A';
      default: return 'Newest First';
    }
  };

  // Convert BusinessProduct to ProductData for compatibility
  const convertBusinessProductToProductData = (product: BusinessProduct) => {
    return {
      id: product.id,
      name: product.name,
      description: product.description || null,
      base_price: product.base_price || null,
      discounted_price: product.discounted_price || null,
      image_url: product.image_url || null,
      images: product.images || null,
      featured_image_index: product.featured_image_index || null,
      business_id: businessId,
      is_available: true,
      slug: product.slug || `product-${product.id}`,
    };
  };

  return (
    <View style={styles.section}>
      {/* Search and Sort Controls */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search color={isDark ? '#9CA3AF' : '#6B7280'} size={20} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: isDark ? '#FFFFFF' : '#000000' }]}
            placeholder="Search products..."
            placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
            value={localSearchQuery}
            onChangeText={handleSearchChange}
          />
          {localSearchQuery.length > 0 && (
            <TouchableOpacity onPress={handleClearSearch} style={styles.clearButton}>
              <X color={isDark ? '#9CA3AF' : '#6B7280'} size={20} />
            </TouchableOpacity>
          )}
        </View>

        {/* Sort Button */}
        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => setShowSortModal(true)}
        >
          <SortAsc color={isDark ? '#D4AF37' : '#D4AF37'} size={20} />
          <Text style={[styles.sortButtonText, { color: isDark ? '#FFFFFF' : '#000000' }]}>
            Sort
          </Text>
        </TouchableOpacity>
      </View>

      {products && products.length > 0 ? (
        <View>
          {/* Products Grid */}
          <View style={styles.productsGrid}>
            {products.map((item) => (
              <View key={item.id} style={styles.productGridItem}>
                <ProductCard
                  product={convertBusinessProductToProductData(item)}
                  isClickable={true}
                  variant="default"
                />
              </View>
            ))}
          </View>

          {/* Loading indicator for infinite scroll */}
          {loadingMore && hasMore && (
            <View style={styles.loadMoreContainer}>
              <LoadingSpinner size="small" />
            </View>
          )}

          {/* Invisible trigger for infinite scroll */}
          {hasMore && !loadingMore && (
            <View
              style={styles.infiniteScrollTrigger}
              onLayout={() => {
                // Trigger load more when this view becomes visible
                setTimeout(() => {
                  onLoadMore();
                }, 100);
              }}
            />
          )}
        </View>
      ) : (
        <Text style={styles.emptyText}>No products available</Text>
      )}

      {/* Sort Modal */}
      <Modal
        visible={showSortModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowSortModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowSortModal(false)}
        >
          <View style={styles.sortModal}>
            <Text style={styles.sortModalTitle}>Sort Products</Text>

            {/* Date Section */}
            <View style={styles.sortSection}>
              <Text style={styles.sortSectionTitle}>Date</Text>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'newest' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('newest')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'newest' && styles.sortOptionTextSelected]}>
                  Newest First
                </Text>
              </TouchableOpacity>
            </View>

            {/* Price Section */}
            <View style={styles.sortSection}>
              <Text style={styles.sortSectionTitle}>Price</Text>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'price_low' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('price_low')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'price_low' && styles.sortOptionTextSelected]}>
                  Price: Low to High
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'price_high' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('price_high')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'price_high' && styles.sortOptionTextSelected]}>
                  Price: High to Low
                </Text>
              </TouchableOpacity>
            </View>

            {/* Name Section */}
            <View style={styles.sortSection}>
              <Text style={styles.sortSectionTitle}>Name</Text>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'name_asc' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('name_asc')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'name_asc' && styles.sortOptionTextSelected]}>
                  Name: A to Z
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'name_desc' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('name_desc')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'name_desc' && styles.sortOptionTextSelected]}>
                  Name: Z to A
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}
