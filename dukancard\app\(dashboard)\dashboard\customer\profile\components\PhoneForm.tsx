"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Phone, Loader2, Save } from "lucide-react";
import { toast } from "sonner";
import { updateCustomerPhone } from "../actions";
import { IndianMobileSchema } from "@/lib/schemas/authSchemas";

// Phone form schema
const PhoneFormSchema = z.object({
  phone: IndianMobileSchema,
});

type PhoneFormData = z.infer<typeof PhoneFormSchema>;

interface PhoneFormState {
  message: string | null;
  errors?: { [key: string]: string[] | undefined };
  success: boolean;
}

interface PhoneFormProps {
  initialPhone: string | null;
}

export default function PhoneForm({ initialPhone }: PhoneFormProps) {
  const [isPending, startTransition] = useTransition();
  const [formState, setFormState] = useState<PhoneFormState>({
    message: null,
    errors: {},
    success: false
  });

  const form = useForm<PhoneFormData>({
    resolver: zodResolver(PhoneFormSchema),
    defaultValues: {
      phone: initialPhone || '',
    },
    mode: 'onChange',
  });

  const onSubmit = (data: PhoneFormData) => {
    const formData = new FormData();
    formData.append('phone', data.phone);

    startTransition(async () => {
      try {
        const initialState: PhoneFormState = {
          message: null,
          errors: {},
          success: false
        };

        const result = await updateCustomerPhone(initialState, formData);
        setFormState(result);

        if (result.success) {
          toast.success("Phone number updated successfully!");
          // Optionally refresh the page to show updated phone
          window.location.reload();
        } else {
          toast.error(result.message || "Failed to update phone number");
        }
      } catch (error) {
        console.error('Error submitting phone form:', error);
        setFormState({
          message: 'An unexpected error occurred. Please try again.',
          success: false,
          errors: {}
        });
        toast.error("An unexpected error occurred");
      }
    });
  };

  return (
    <Card className="bg-background border-border">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
          <Phone className="w-5 h-5 text-primary" />
          Update Phone Number
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">
                    Mobile Number
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        placeholder="9876543210"
                        type="tel"
                        pattern="[0-9]*"
                        inputMode="numeric"
                        {...field}
                        onChange={(e) => {
                          // Remove any +91 prefix if user enters it
                          let value = e.target.value.replace(/^\+91/, '');
                          // Only allow numeric input
                          value = value.replace(/\D/g, '');
                          // Limit to 10 digits
                          if (value.length > 10) {
                            value = value.slice(0, 10);
                          }
                          field.onChange(value);
                        }}
                        onKeyDown={(e) => {
                          // Prevent non-numeric input
                          const isNumeric = /^[0-9]$/.test(e.key);
                          const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key);
                          if (!isNumeric && !isControl) {
                            e.preventDefault();
                          }
                        }}
                        className="bg-background border-border focus-visible:ring-primary/20 pl-10"
                        disabled={isPending}
                      />
                      <Phone className="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Display form state messages */}
            {formState.message && (
              <div className={`text-sm ${formState.success ? 'text-green-600' : 'text-red-600'}`}>
                {formState.message}
              </div>
            )}

            <Button
              type="submit"
              disabled={isPending || !form.formState.isValid}
              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Update Phone Number
                </>
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
