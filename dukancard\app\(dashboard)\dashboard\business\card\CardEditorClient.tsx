"use client";

import { useState, useEffect, useTransition, useRef, useMemo, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { Link as LinkIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { scrollToFirstError } from "./utils/scrollToError";

import {
  BusinessCardData,
  businessCardSchema,
  defaultBusinessCardData,
  requiredFieldsForOnline,
  requiredFieldsForSaving,
} from "./schema";
import { updateBusinessCard } from "./actions";

// Import custom hooks
import { useLogoUpload } from "./components/hooks/useLogoUpload";
import { usePincodeDetails } from "./components/hooks/usePincodeDetails";

// Import components
import CardEditFormContent from "./components/CardEditForm/CardEditFormContent";
import CardPreviewSection from "./components/CardPreviewSection";
import ImageCropDialog from "./components/ImageCropDialog";
import LogoDeleteDialog from "./components/LogoDeleteDialog";
import FormSubmitButton from "./components/CardEditForm/FormSubmitButton";
import UnsavedChangesReminder from "./components/UnsavedChangesReminder";
import { Form } from "@/components/ui/form";

export type UserPlanStatus =
  | "basic"
  | "growth"
  | "pro"
  | "enterprise"
  | "trial"
  | null;

interface CardEditorClientProps {
  initialData: Partial<BusinessCardData>;
  currentUserPlan: UserPlanStatus;
  subscriptionStatus: string | null;
}

export default function CardEditorClient({
  initialData,
  currentUserPlan,
  subscriptionStatus,
}: CardEditorClientProps) {
  // Client-side check to prevent SSR issues
  const [isClient, setIsClient] = useState(false);

  // Main state for the current card data (for preview)
  const [currentCardData, setCurrentCardData] = useState<BusinessCardData>(
    () => {
      const mergedData = {
        ...defaultBusinessCardData,
        ...initialData,
      };
      return {
        ...mergedData,
        member_name: mergedData.member_name || "",
        title: mergedData.title || "",
        business_name: mergedData.business_name || "",
        status: mergedData.status || "offline",
      } as BusinessCardData;
    }
  );

  // Store the original saved data for discard functionality
  const [savedCardData, setSavedCardData] = useState<BusinessCardData>(
    () => {
      const mergedData = {
        ...defaultBusinessCardData,
        ...initialData,
      };
      return {
        ...mergedData,
        member_name: mergedData.member_name || "",
        title: mergedData.title || "",
        business_name: mergedData.business_name || "",
        status: mergedData.status || "offline",
      } as BusinessCardData;
    }
  );

  // Form submission state
  const [isPending, startTransition] = useTransition();
  const [isResetting, setIsResetting] = useState(false);
  const [isCheckingSlug, setIsCheckingSlug] = useState(false);

  // Reference to the card preview element for QR code download
  const cardPreviewRef = useRef<HTMLDivElement | null>(null);

  // Properly merge initial data with defaults to prevent dirty state on load
  const formDefaultValues = useMemo(() => ({
    ...defaultBusinessCardData,
    ...initialData,
    locality: initialData?.locality ?? "",
    // Ensure custom_branding and custom_ads have proper structure
    custom_branding: {
      ...defaultBusinessCardData.custom_branding,
      ...(initialData?.custom_branding || {}),
    },
    custom_ads: {
      ...defaultBusinessCardData.custom_ads,
      ...(initialData?.custom_ads || {}),
    },
  }), [initialData]);

  // Initialize the form with zod resolver
  const form = useForm<BusinessCardData>({
    resolver: zodResolver(businessCardSchema),
    defaultValues: formDefaultValues,
    mode: "onChange",
    // This ensures the form properly tracks changes
    resetOptions: {
      keepDirtyValues: false, // When form is reset, all fields are marked as pristine
      keepErrors: false,      // Clear all errors when form is reset
    }
  });

  // Get watched form values
  const watchedFields = form.watch();

  // Check if subscription is halted
  const isSubscriptionHalted = subscriptionStatus === "halted";

  // Determine if user can go online based on required fields and subscription status
  // If subscription is halted, user cannot go online regardless of required fields
  const canGoOnline = !isSubscriptionHalted && requiredFieldsForOnline.every(
    (field) => watchedFields[field] && String(watchedFields[field]).trim() !== ""
  );

  // Use custom hooks
  const { isPincodeLoading, availableLocalities, handlePincodeChange } =
    usePincodeDetails({
      form,
      initialPincode: initialData?.pincode,
      initialLocality: initialData?.locality,
    });

  const {
    logoUploadStatus,
    localPreviewUrl,
    isLogoUploading,
    imageToCrop,
    onFileSelect,
    handleCropComplete,
    handleCropDialogClose,
    handleLogoDelete,
    logoErrorDisplay,
    isDeleteDialogOpen,
    isDeleting,
    // openDeleteDialog is not used
    closeDeleteDialog,
    confirmLogoDelete,
  } = useLogoUpload({
    form,
    initialLogoUrl: initialData?.logo_url || "",
    onUpdateCardData: (data) =>
      setCurrentCardData((prev) => ({ ...prev, ...data })),
  });

  // Effect to set client-side flag
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Effect to properly reset form after initial data is loaded to prevent dirty state
  useEffect(() => {
    if (!isClient) return;

    // Reset the form with the properly merged data to ensure it's not marked as dirty
    // Only run this once after component mounts
    const timer = setTimeout(() => {
      setIsResetting(true);
      form.reset(formDefaultValues, {
        keepDirtyValues: false,
        keepErrors: false,
        keepDirty: false,
        keepIsSubmitted: false,
      });
      // Clear resetting flag after a short delay
      setTimeout(() => setIsResetting(false), 100);
    }, 100); // Small delay to ensure form is fully initialized

    return () => clearTimeout(timer);
  }, [isClient, form, formDefaultValues]); // Include form and formDefaultValues dependencies

  // Effect to watch form changes and update preview state
  useEffect(() => {
    const subscription = form.watch((value) => {
      // Don't update preview during form resets to prevent loops
      if (!isResetting) {
        setCurrentCardData((prev) => ({
          ...prev,
          ...value,
        }));
      }
    });

    return () => {
      subscription.unsubscribe();
      if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);
    };
  }, [form, localPreviewUrl, isResetting]);

  // Helper function to get missing required fields
  const getMissingFields = (forOnlineOnly: boolean = true): Array<keyof BusinessCardData> => {
    const formValues = form.getValues();
    const fieldsToCheck = forOnlineOnly ? requiredFieldsForOnline : requiredFieldsForSaving;
    return fieldsToCheck.filter(
      (field) => !formValues[field] || String(formValues[field]).trim() === ""
    );
  };

  // Helper function to get human-readable field names
  const getFieldLabel = (field: keyof BusinessCardData): string => {
    const fieldLabels: Record<string, string> = {
      member_name: "Your Name",
      title: "Your Title",
      business_name: "Business Name",
      phone: "Primary Phone",
      address_line: "Address Line",
      pincode: "Pincode",
      city: "City",
      state: "State",
      locality: "Locality",
      contact_email: "Contact Email",
      business_category: "Business Category",
    };
    return fieldLabels[field] || field;
  };

  // Handler to discard unsaved changes
  const handleDiscardChanges = () => {
    // Set resetting flag to prevent watch subscription from triggering
    setIsResetting(true);

    // Reset to the last saved state (savedCardData)
    form.reset(savedCardData, {
      keepDirtyValues: false,
      keepErrors: false,
      keepDirty: false,
      keepIsSubmitted: false,
    });

    // Also update the current card data to match the saved state
    setCurrentCardData(savedCardData);

    // Clear resetting flag after a short delay
    setTimeout(() => setIsResetting(false), 100);

    toast.info("Changes discarded");
  };

  // Single form submission handler (used by both floating save and form save button)
  const onSubmit = (data: BusinessCardData) => {
    // Check if there are any validation errors from zod
    if (Object.keys(form.formState.errors).length > 0) {
      // Scroll to the first error
      scrollToFirstError('business-card-form');

      toast.error(
        <div>
          <p className="font-medium mb-1">Cannot save business card</p>
          <p className="text-sm mb-1">Please fix the validation errors</p>
        </div>
      );
      return;
    }

    // First check if required fields for saving are missing
    const missingSavingFields = getMissingFields(false);
    if (missingSavingFields.length > 0) {
      const missingFieldLabels = missingSavingFields.map(getFieldLabel);

      toast.error(
        <div>
          <p className="font-medium mb-1">Cannot save business card</p>
          <p className="text-sm mb-1">Please fill in the following required fields:</p>
          <ul className="text-sm list-disc pl-4">
            {missingFieldLabels.map((field, index) => (
              <li key={index}>{field}</li>
            ))}
          </ul>
        </div>
      );

      // Focus on the first missing field
      const firstMissingField = missingSavingFields[0];
      form.setFocus(firstMissingField);
      return;
    }

    // Check if subscription is halted and trying to go online
    if (data.status === "online" && isSubscriptionHalted) {
      toast.error(
        <div>
          <p className="font-medium mb-1">Cannot set card to online status</p>
          <p className="text-sm mb-1">Your subscription is currently paused. Please resume your subscription to set your card online.</p>
        </div>
      );
      return;
    }

    // Then check if trying to go online but missing required fields
    if (data.status === "online" && !canGoOnline && !isSubscriptionHalted) {
      const missingOnlineFields = getMissingFields(true);
      const missingFieldLabels = missingOnlineFields.map(getFieldLabel);

      toast.error(
        <div>
          <p className="font-medium mb-1">Cannot set card to online status</p>
          <p className="text-sm mb-1">Please fill in the following required fields:</p>
          <ul className="text-sm list-disc pl-4">
            {missingFieldLabels.map((field, index) => (
              <li key={index}>{field}</li>
            ))}
          </ul>
        </div>
      );

      // Focus on the first missing field
      const firstMissingField = missingOnlineFields[0];
      form.setFocus(firstMissingField);
      return;
    }

    startTransition(async () => {
      const result = await updateBusinessCard(data);

      if (result.success && result.data) {
        toast.success("Business card updated successfully!");

        // Update both current and saved card data states
        setCurrentCardData(result.data);
        setSavedCardData(result.data);

        // Set resetting flag to prevent watch subscription from triggering
        setIsResetting(true);

        // Reset the form with the updated data immediately
        // Use the proper reset options to ensure form state is properly updated
        form.reset(result.data, {
          keepDirtyValues: false, // Mark all fields as pristine
          keepErrors: false,      // Clear all errors
          keepDirty: false,       // Reset dirty state
          keepIsSubmitted: false, // Reset submitted state
        });

        // Clear resetting flag after a short delay
        setTimeout(() => setIsResetting(false), 100);
      } else {
        toast.error(result.error || "Failed to update business card.");
      }
    });
  };

  // Callback for slug checking state changes
  const handleSlugCheckingChange = useCallback((checking: boolean) => {
    setIsCheckingSlug(checking);
  }, []);

  // Single save handler for both floating save and form save button
  const handleSave = async () => {
    // Prevent multiple simultaneous submissions or if async operations are in progress
    if (isPending || isCheckingSlug || isPincodeLoading) {
      return;
    }

    // Check if there are any form validation errors (including slug availability)
    if (Object.keys(form.formState.errors).length > 0) {
      toast.error("Please fix the form errors before saving");
      return;
    }

    // Get current form values
    const formValues = form.getValues();

    // Manually validate the form
    const validation = businessCardSchema.safeParse(formValues);

    if (!validation.success) {
      console.error("Validation failed:", validation.error);
      toast.error("Please fix the form errors before saving");
      return;
    }

    // If validation passes, call onSubmit directly
    onSubmit(validation.data);
  };

  // Show loading state until client is ready
  if (!isClient) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--brand-gold)] mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading editor...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Floating Unsaved Changes Reminder */}
      <UnsavedChangesReminder
        form={form}
        isPending={isPending}
        isLogoUploading={isLogoUploading}
        isCheckingSlug={isCheckingSlug}
        isPincodeLoading={isPincodeLoading}
        onSave={handleSave}
        onDiscard={handleDiscardChanges}
      />

      {/* Single Form for both Mobile and Desktop */}
      <Form {...form}>
        <form onSubmit={(e) => e.preventDefault()} className="space-y-8">
          {/* Mobile/Tablet Layout - Card on top, form below (visible on screens below lg breakpoint) */}
          <div className="flex flex-col gap-6 lg:hidden">
            {/* Preview Section */}
            <CardPreviewSection
              cardData={currentCardData}
              logoUploadStatus={logoUploadStatus}
              localPreviewUrl={localPreviewUrl}
              userPlan={
                currentUserPlan === "trial" ? "basic" : currentUserPlan ?? undefined
              }
              cardPreviewRef={cardPreviewRef}
            />

            {/* Edit Form Section */}
            <motion.div initial="hidden" animate="visible" style={{width: '100%'}}>
              <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-4 sm:p-5 md:p-6 mb-4 transition-all duration-300 hover:shadow-lg">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
                  <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
                    <LinkIcon className="w-4 sm:w-5 h-4 sm:h-5" />
                  </div>
                  <div className="flex-1">
                    <h2 className="text-lg sm:text-xl font-semibold bg-gradient-to-r from-[var(--brand-gold)] to-amber-500 bg-clip-text text-transparent">
                      Edit Details
                    </h2>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                      Customize your digital business card below. Changes reflect in
                      real-time.
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const slug = form.getValues("business_slug");
                      if (slug) window.open(`/${slug}`, "_blank");
                      else toast.error("Please set a business slug first.");
                    }}
                    disabled={!form.getValues("business_slug")}
                    className="text-xs px-2 py-0.5 sm:px-2.5 sm:py-1 border-[var(--brand-gold)]/50 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 rounded-full transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    <LinkIcon className="mr-1 h-3 w-3 sm:h-4 sm:w-4" /> View Public
                    Card
                  </Button>
                </div>

                {/* Form Content */}
                <CardEditFormContent
                  form={form}
                  canGoOnline={canGoOnline}
                  currentUserPlan={currentUserPlan}
                  onFileSelect={onFileSelect}
                  isPincodeLoading={isPincodeLoading}
                  availableLocalities={availableLocalities}
                  onPincodeChange={handlePincodeChange}
                  isLogoUploading={isLogoUploading}
                  onLogoDelete={handleLogoDelete}
                  isSubscriptionHalted={isSubscriptionHalted}
                  onSlugCheckingChange={handleSlugCheckingChange}
                />

                <div className="flex flex-col gap-2 sm:gap-3 mt-4 sm:mt-6">
                  {logoErrorDisplay && (
                    <p className="text-xs text-red-500 dark:text-red-400 text-right">
                      {logoErrorDisplay}
                    </p>
                  )}
                  <FormSubmitButton
                    form={form}
                    isPending={isPending}
                    isLogoUploading={isLogoUploading}
                    isCheckingSlug={isCheckingSlug}
                    isPincodeLoading={isPincodeLoading}
                    onSave={handleSave}
                  />
                </div>
              </div>
            </motion.div>
          </div>

          {/* Desktop Layout - Only visible at lg breakpoint and above */}
          <div className="hidden lg:flex flex-col lg:flex-row gap-8 sm:gap-12 pb-12 relative">
            {/* Preview Column */}
            <div className="flex-[1] w-full lg:w-1/2 lg:sticky lg:top-24 self-start">
              <CardPreviewSection
                cardData={currentCardData}
                logoUploadStatus={logoUploadStatus}
                localPreviewUrl={localPreviewUrl}
                userPlan={
                  currentUserPlan === "trial"
                    ? "basic"
                    : currentUserPlan ?? undefined
                }
                cardPreviewRef={cardPreviewRef}
              />
            </div>

            {/* Edit Form Column */}
            <motion.div
              initial="hidden"
              animate="visible"
              style={{flex: 2, width: '100%', position: 'sticky', top: '6rem', alignSelf: 'flex-start'}}
            >
              <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-4 sm:p-5 md:p-6 mb-4 transition-all duration-300 hover:shadow-lg">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
                  <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
                    <LinkIcon className="w-4 sm:w-5 h-4 sm:h-5" />
                  </div>
                  <div className="flex-1">
                    <h2 className="text-lg sm:text-xl font-semibold bg-gradient-to-r from-[var(--brand-gold)] to-amber-500 bg-clip-text text-transparent">
                      Edit Details
                    </h2>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                      Customize your digital business card below. Changes reflect in
                      real-time.
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const slug = form.getValues("business_slug");
                      if (slug) window.open(`/${slug}`, "_blank");
                      else toast.error("Please set a business slug first.");
                    }}
                    disabled={
                      !form.getValues("business_slug") ||
                      !!form.formState.errors.business_slug
                    }
                    className="text-xs px-2 py-0.5 sm:px-2.5 sm:py-1 border-[var(--brand-gold)]/50 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 rounded-full transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    <LinkIcon className="mr-1 h-3 w-3 sm:h-4 sm:w-4" /> View Public
                    Card
                  </Button>
                </div>

                {/* Form Content */}
                <CardEditFormContent
                  form={form}
                  canGoOnline={canGoOnline}
                  currentUserPlan={currentUserPlan}
                  onFileSelect={onFileSelect}
                  isPincodeLoading={isPincodeLoading}
                  availableLocalities={availableLocalities}
                  onPincodeChange={handlePincodeChange}
                  isLogoUploading={isLogoUploading}
                  onLogoDelete={handleLogoDelete}
                  isSubscriptionHalted={isSubscriptionHalted}
                />

                <div className="flex flex-col gap-2 sm:gap-3 mt-4 sm:mt-6">
                  {logoErrorDisplay && (
                    <p className="text-xs text-red-500 dark:text-red-400 text-right">
                      {logoErrorDisplay}
                    </p>
                  )}
                  <FormSubmitButton
                    form={form}
                    isPending={isPending}
                    isLogoUploading={isLogoUploading}
                    isCheckingSlug={isCheckingSlug}
                    isPincodeLoading={isPincodeLoading}
                    onSave={handleSave}
                  />
                </div>
              </div>
            </motion.div>
          </div>
        </form>
      </Form>

      {/* Image Crop Dialog */}
      <ImageCropDialog
        isOpen={!!imageToCrop}
        imgSrc={imageToCrop}
        onClose={handleCropDialogClose}
        onCropComplete={handleCropComplete}
      />

      {/* Logo Delete Confirmation Dialog */}
      <LogoDeleteDialog
        isOpen={isDeleteDialogOpen}
        isDeleting={isDeleting}
        onClose={closeDeleteDialog}
        onConfirm={confirmLogoDelete}
      />
    </>
  );
}
