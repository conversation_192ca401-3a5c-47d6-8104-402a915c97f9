import * as z from "zod";
import { IndianMobileSchema } from "@/lib/schemas/authSchemas";

// Regular expression for validating hex color codes (e.g., #RRGGBB, #RGB)
// const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/; // Removed as theme_color is removed

// Zod schema for business card data validation (Phase 1)
export const businessCardSchema = z.object({
  // Optional fields first
  logo_url: z
    .string()
    .url({ message: "Invalid URL format for logo/profile photo." })
    .optional()
    .or(z.literal(""))
    .nullable(), // Allow empty string, null, or valid URL
  established_year: z
    .number()
    .int({ message: "Established year must be a whole number." })
    .min(1800, { message: "Established year must be after 1800." })
    .max(new Date().getFullYear(), { message: "Established year cannot be in the future." })
    .optional()
    .nullable(),
  // Address broken down - NOW REQUIRED (from onboarding)
  address_line: z
    .string()
    .min(1, { message: "Address line is required." })
    .max(100, { message: "Address line cannot exceed 100 characters." }),
  locality: z
    .string()
    .min(1, { message: "Locality/area is required." }),
  city: z
    .string()
    .min(1, { message: "City is required." })
    .max(50, { message: "City cannot exceed 50 characters." }),
  state: z
    .string()
    .min(1, { message: "State is required." })
    .max(50, { message: "State cannot exceed 50 characters." }),
  pincode: z
    .string()
    .min(6, { message: "Pincode must be 6 digits." })
    .max(6, { message: "Pincode must be 6 digits." })
    .regex(/^\d+$/, { message: "Pincode must contain only digits." }),
  phone: IndianMobileSchema, // Primary display phone - NOW REQUIRED
  // timing_info removed
  // delivery_info removed
  // website_url removed
  instagram_url: z
    .string()
    .url({ message: "Invalid URL format for Instagram." })
    .optional()
    .or(z.literal("")),
  facebook_url: z
    .string()
    .url({ message: "Invalid URL format for Facebook." })
    .optional()
    .or(z.literal("")),
  // linkedin_url removed
  // twitter_url removed
  // youtube_url removed
  whatsapp_number: IndianMobileSchema // For wa.me link
    .optional()
    .or(z.literal("")),
  // call_number removed
  about_bio: z
    .string()
    .max(100, { message: "Bio cannot exceed 100 characters." })
    .optional()
    .or(z.literal("")),
  theme_color: z // Added for Growth plan
    .string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {
      message: "Invalid hex color format (e.g., #RRGGBB or #RGB).",
    })
    .optional()
    .or(z.literal("")),
  // card_texture field removed as it doesn't exist in the database
  business_hours: z.any().optional().nullable(), // Added for Growth plan - Using z.any() for now, refine if specific structure needed
  delivery_info: z // Added for Growth plan
    .string()
    .max(100, { message: "Delivery info cannot exceed 100 characters." })
    .optional()
    .or(z.literal("")),
  business_category: z
    .string()
    .min(1, { message: "Business category is required." }),
  google_maps_url: z
    .string()
    .url({ message: "Please enter a valid Google Maps URL." })
    .optional()
    .or(z.literal(""))
    .refine((url) => {
      if (!url || url === "") return true; // Allow empty
      // Validate Google Maps URL patterns
      const googleMapsPatterns = [
        /^https:\/\/maps\.app\.goo\.gl\/[a-zA-Z0-9]+$/,
        /^https:\/\/www\.google\.com\/maps\//,
        /^https:\/\/goo\.gl\/maps\//,
        /^https:\/\/maps\.google\.com\//
      ];
      return googleMapsPatterns.some(pattern => pattern.test(url));
    }, {
      message: "Please enter a valid Google Maps URL (e.g., https://maps.app.goo.gl/... or https://www.google.com/maps/...)"
    }),
  status: z.enum(["online", "offline"]).default("offline"),
  // Custom branding fields for Pro/Enterprise users
  custom_branding: z.object({
    custom_header_text: z.string().max(50).optional().or(z.literal("")),
    custom_header_image_url: z.string().url().optional().or(z.literal("")), // Legacy field
    custom_header_image_light_url: z.string().url().optional().or(z.literal("")), // Light theme
    custom_header_image_dark_url: z.string().url().optional().or(z.literal("")), // Dark theme
    hide_dukancard_branding: z.boolean().optional(),
    // File objects for pending uploads (not saved to database)
    pending_light_header_file: z.any().optional(), // File object for light theme
    pending_dark_header_file: z.any().optional(), // File object for dark theme
  }).optional()
  .refine((data) => {
    // Only require custom_header_text OR any header image if hide_dukancard_branding is explicitly true
    if (data?.hide_dukancard_branding === true) {
      const hasText = data?.custom_header_text && data.custom_header_text.trim() !== "";
      const hasLegacyImage = data?.custom_header_image_url && data.custom_header_image_url.trim() !== "";
      const hasLightImage = data?.custom_header_image_light_url && data.custom_header_image_light_url.trim() !== "";
      const hasDarkImage = data?.custom_header_image_dark_url && data.custom_header_image_dark_url.trim() !== "";

      if (!hasText && !hasLegacyImage && !hasLightImage && !hasDarkImage) {
        return false;
      }
    }
    return true;
  }, {
    message: "Custom header text or image is required when hiding Dukancard branding",
    path: ["custom_header_text"]
  }),
  // Custom ads for Pro/Enterprise users
  custom_ads: z.object({
    enabled: z.boolean().optional(),
    image_url: z.string().url().optional().or(z.literal("")),
    link_url: z.string().url().optional().or(z.literal("")),
    uploaded_at: z.string().optional().or(z.literal("")).nullable(),
  }).optional(),
  business_slug: z
    .string()
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, {
      message:
        "Slug must be lowercase letters, numbers, or hyphens, and cannot start/end with a hyphen.",
    })
    .min(3, { message: "Slug must be at least 3 characters long." })
    .optional()
    .or(z.literal("")),

  // Required fields
  member_name: z
    .string()
    .min(1, { message: "Member name is required." })
    .max(50, { message: "Name cannot exceed 50 characters." }),
  title: z
    .string()
    .min(1, { message: "Title/Designation is required." })
    .max(50, { message: "Title cannot exceed 50 characters." }),
  business_name: z
    .string()
    .min(1, { message: "Business name is required." })
    .max(100, { message: "Business name cannot exceed 100 characters." }),

  // Read-only/managed fields (keep for type safety if needed)
  id: z.string().uuid().optional(),
  contact_email: z.string().email({ message: "Please enter a valid email address" }).min(1, { message: "Contact email is required" }),
  has_active_subscription: z.boolean().optional(),
  trial_end_date: z.string().optional().nullable(), // Database returns string, not Date
  created_at: z.union([z.string(), z.date()]).optional().transform((val) => {
    if (val instanceof Date) return val.toISOString();
    return val;
  }), // Handle both Date objects and strings
  updated_at: z.union([z.string(), z.date()]).optional().transform((val) => {
    if (val instanceof Date) return val.toISOString();
    return val;
  }), // Handle both Date objects and strings

  // Interaction fields (added in Phase 2) - make optional as they might not always be fetched
  total_likes: z.number().int().nonnegative().optional(),
  total_subscriptions: z.number().int().nonnegative().optional(),
  average_rating: z.number().nonnegative().optional(),
  total_visits: z.number().int().nonnegative().optional(),
});

// TypeScript type inferred from the Zod schema
export type BusinessCardData = z.infer<typeof businessCardSchema>;

// Default values for initializing the form or preview (Phase 1)
export const defaultBusinessCardData: Partial<BusinessCardData> = {
  member_name: "",
  title: "",
  business_name: "",
  logo_url: null,
  established_year: null,
  address_line: "",
  locality: "",
  city: "",
  state: "",
  pincode: "",
  phone: "",
  instagram_url: "",
  facebook_url: "",
  whatsapp_number: "",
  about_bio: "",
  theme_color: "",
  business_hours: null,
  delivery_info: "",
  business_category: "",
  google_maps_url: "",
  status: "offline",
  business_slug: "",
  contact_email: "", // Added contact_email field
  custom_branding: {
    custom_header_text: "",
    custom_header_image_url: "", // Legacy field
    custom_header_image_light_url: "", // Light theme
    custom_header_image_dark_url: "", // Dark theme
    hide_dukancard_branding: false,
    pending_light_header_file: null, // File object for light theme
    pending_dark_header_file: null, // File object for dark theme
  },
  custom_ads: {
    enabled: false,
    image_url: "",
    link_url: "",
    uploaded_at: null,
  },
};

// Define which fields are strictly required to go online
export const requiredFieldsForOnline: (keyof BusinessCardData)[] = [
  "member_name",
  "title",
  "business_name",
  "phone",
  "address_line",
  "pincode",
  "city",
  "state",
  "locality",
  "contact_email", // Added contact_email as required for online status
  "business_category" // Added business_category as required for online status
];

// Define which fields are required for saving regardless of status (all onboarding fields except plan)
export const requiredFieldsForSaving: (keyof BusinessCardData)[] = [
  "member_name",
  "title",
  "business_name",
  "phone",
  "contact_email",
  "business_category",
  "address_line",
  "pincode",
  "city",
  "state",
  "locality"
];
