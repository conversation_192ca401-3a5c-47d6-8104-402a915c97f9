"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
// Unused import removed: import { Button } from "@/components/ui/button";
import {
  Loader2,
  XCircle,
  AlertCircle,
  Clock,
  Calendar,
  CheckCircle,
  Zap,
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import DialogBackground from "@/app/(dashboard)/dashboard/business/plan/components/DialogBackground";
import EnhancedGlowButton from "../EnhancedGlowButton";
import { cn } from "@/lib/utils";

interface ModernCancellationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCancelSubscription: (_cancelImmediately: boolean) => Promise<void>;
  isLoading: boolean;
  isWithinRefundWindow: boolean;
  subscriptionStatus: string;
  effectiveCancellationDate: string | null;
  authenticatedSubscription?: { id: string } | null;
}

export default function ModernCancellationDialog({
  isOpen,
  onClose,
  onCancelSubscription,
  isLoading,
  isWithinRefundWindow,
  subscriptionStatus,
  effectiveCancellationDate,
  authenticatedSubscription,
}: ModernCancellationDialogProps) {
  // State for client-side rendering check
  const [isClient, setIsClient] = useState(false);

  // Animation variants
  const dialogVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
  };

  // Unused variable removed
  const _buttonVariants = {
    hover: { scale: 1.03 },
    tap: { scale: 0.98 },
  };

  // Set isClient to true on mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Handle cancel subscription
  const handleCancelSubscription = async (cancelImmediately: boolean) => {
    await onCancelSubscription(cancelImmediately);
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          onClose();
        }
      }}
    >
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={dialogVariants}
          className="relative overflow-hidden"
        >
          {/* Background gradient effect */}
          {isClient && <DialogBackground variant="gold" intensity="medium" />}

          {/* Header with icon */}
          <DialogHeader className="p-6 pb-2 z-10 relative">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-full bg-red-500/15 text-red-500">
                <XCircle className="w-5 h-5" />
              </div>
              <Badge
                variant="outline"
                className="border-red-500/50 text-red-500 px-2 py-0.5"
              >
                Subscription Management
              </Badge>
            </div>
            <DialogTitle className="text-2xl font-bold">
              Manage Subscription
            </DialogTitle>
          </DialogHeader>

          {/* Content section */}
          <div className="p-6 pt-2 space-y-4 z-10 relative">
            {/* Description with animation */}
            <motion.p
              variants={itemVariants}
              className="text-muted-foreground"
            >
              Select an action for your subscription
            </motion.p>

            {/* Cancellation options */}
            <motion.div variants={itemVariants} className="space-y-3">
              {/* Cancel at end of billing cycle - Only for active subscriptions */}
              {subscriptionStatus === "active" && (
                <motion.div
                  whileHover={{ y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  className={cn(
                    "p-4 rounded-lg border border-neutral-200 dark:border-neutral-800",
                    "bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80",
                    "transition-all duration-200 cursor-pointer",
                    (isLoading || !!effectiveCancellationDate) && "opacity-50 cursor-not-allowed"
                  )}
                  onClick={() => !isLoading && !effectiveCancellationDate && handleCancelSubscription(false)}
                >
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-full bg-amber-50 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400 mt-0.5">
                      <Clock className="w-4 h-4" />
                    </div>
                    <div>
                      <h3 className="font-medium">Cancel at End of Billing Cycle</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        Continue using until your current period ends
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Cancel immediately (no refund) - Only for active subscriptions */}
              {subscriptionStatus === "active" && (
                <motion.div
                  whileHover={{ y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  className={cn(
                    "p-4 rounded-lg border border-neutral-200 dark:border-neutral-800",
                    "bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80",
                    "transition-all duration-200 cursor-pointer",
                    isLoading && "opacity-50 cursor-not-allowed"
                  )}
                  onClick={() => !isLoading && handleCancelSubscription(true)}
                >
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-full bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 mt-0.5">
                      <Zap className="w-4 h-4" />
                    </div>
                    <div>
                      <h3 className="font-medium">Cancel Immediately</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        Cancel now with no refund for current period
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Cancel immediately with refund (if within window) - Only for active subscriptions */}
              {subscriptionStatus === "active" && isWithinRefundWindow && (
                <motion.div
                  whileHover={{ y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  className={cn(
                    "p-4 rounded-lg border border-neutral-200 dark:border-neutral-800",
                    "bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80",
                    "transition-all duration-200 cursor-pointer",
                    isLoading && "opacity-50 cursor-not-allowed"
                  )}
                  onClick={() => !isLoading && handleCancelSubscription(true)}
                >
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-full bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 mt-0.5">
                      <AlertCircle className="w-4 h-4" />
                    </div>
                    <div>
                      <h3 className="font-medium">Cancel with Refund</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        Cancel immediately and request a refund
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Cancel authenticated subscription - Only for authenticated subscriptions */}
              {subscriptionStatus === "authenticated" && authenticatedSubscription?.id && (
                <motion.div
                  whileHover={{ y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  className={cn(
                    "p-4 rounded-lg border border-neutral-200 dark:border-neutral-800",
                    "bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80",
                    "transition-all duration-200 cursor-pointer",
                    isLoading && "opacity-50 cursor-not-allowed"
                  )}
                  onClick={() => !isLoading && handleCancelSubscription(true)}
                >
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-full bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 mt-0.5">
                      <Calendar className="w-4 h-4" />
                    </div>
                    <div>
                      <h3 className="font-medium">Cancel Future Subscription</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        Cancel your upcoming subscription
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}
            </motion.div>

            {/* Warning alert */}
            {(subscriptionStatus === "active" || subscriptionStatus === "authenticated") && (
              <motion.div variants={itemVariants}>
                <Alert className="bg-white/50 dark:bg-black/50 border border-red-200 dark:border-red-800/50">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                  <AlertTitle className="font-medium">Important</AlertTitle>
                  <AlertDescription className="text-sm">
                    <ul className="list-disc pl-4 space-y-1">
                      <li>
                        <strong>Cancel at End of Billing Cycle:</strong> You&apos;ll keep access to premium features until your current billing period ends.
                      </li>
                      <li>
                        <strong>Cancel Immediately:</strong> Your access to premium features will end now with no refund for the current period.
                      </li>
                      {isWithinRefundWindow && (
                        <li>
                          <strong>Cancel with Refund:</strong> Your access will end now and you&apos;ll receive a refund for the current period.
                        </li>
                      )}
                    </ul>
                  </AlertDescription>
                </Alert>
              </motion.div>
            )}
          </div>

          {/* Footer with buttons */}
          <DialogFooter className="p-6 pt-2 flex justify-center">
            <motion.div
              variants={itemVariants}
              className="w-full sm:w-auto"
            >
              <EnhancedGlowButton
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="w-full sm:w-auto"
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Close
                  </span>
                )}
              </EnhancedGlowButton>
            </motion.div>
          </DialogFooter>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
