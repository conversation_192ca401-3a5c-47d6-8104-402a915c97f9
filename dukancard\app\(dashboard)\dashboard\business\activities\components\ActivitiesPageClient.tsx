"use client";

/**
 * Realtime Subscription Notes
 *
 * This component uses Supabase Realtime to listen for new activities.
 *
 * Important: You need to enable realtime for the business_activities table in Supabase:
 * 1. Go to Supabase Dashboard > Database > Replication
 * 2. Find the "business_activities" table in the list
 * 3. Enable realtime by toggling it on
 *
 * The component subscribes to INSERT events on the business_activities table
 * with a filter for the specific business_profile_id:
 *
 * ```javascript
 * supabase
 *   .channel('business-activities-changes')
 *   .on(
 *     'postgres_changes',
 *     {
 *       event: 'INSERT',
 *       schema: 'public',
 *       table: 'business_activities',
 *       filter: `business_profile_id=eq.${businessProfileId}`,
 *     },
 *     async (payload) => {
 *       // Handle new activity
 *     }
 *   )
 *   .subscribe();
 * ```
 */

import { useState, useEffect, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Bell, Heart, Star, Users, RefreshCw, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { BusinessActivity, getBusinessActivities, getUnreadActivitiesCount } from "@/lib/actions/activities";
import ActivityItem from "./ActivityItem";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { realtimeService } from "@/lib/services/realtimeService";

interface ActivitiesPageClientProps {
  initialActivities: BusinessActivity[];
  totalCount: number;
  unreadCount: number;
  businessProfileId: string;
}

export default function ActivitiesPageClient({
  initialActivities,
  totalCount,
  unreadCount: initialUnreadCount,
  businessProfileId,
}: ActivitiesPageClientProps) {
  const [activities, setActivities] = useState<BusinessActivity[]>(initialActivities);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(totalCount > initialActivities.length);
  const [sortBy, setSortBy] = useState<"newest" | "oldest" | "unread_first">("newest");
  const [filterBy, setFilterBy] = useState<"all" | "like" | "subscribe" | "rating" | "unread">("all");
  const [activeTab, setActiveTab] = useState<"all" | "likes" | "subscriptions" | "ratings">("all");
  const [unreadCount, setUnreadCount] = useState(initialUnreadCount);

  // Track which unread notifications have been viewed in this session
  const [viewedUnreadIds, setViewedUnreadIds] = useState<Set<string>>(new Set());
  // Snapshot of activities when page first loads (for section determination)
  const [activitiesSnapshot, _setActivitiesSnapshot] = useState<BusinessActivity[]>(initialActivities);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Function to mark a notification as viewed (when it comes into view)
  const markAsViewed = useCallback((activityId: string) => {
    // Only track activities that were initially unread when page loaded
    const wasInitiallyUnread = activitiesSnapshot.find(a => a.id === activityId)?.is_read === false;
    if (wasInitiallyUnread) {
      setViewedUnreadIds(prev => new Set([...prev, activityId]));
    }
  }, [activitiesSnapshot]);

  // Check if all initially unread activities have been viewed and mark them as read
  useEffect(() => {
    // Get activities that were unread when page loaded (from snapshot)
    const initiallyUnreadActivities = activitiesSnapshot.filter(a => !a.is_read);
    const initiallyUnreadIds = initiallyUnreadActivities.map(a => a.id);

    // If we have initially unread activities and all of them have been viewed
    if (initiallyUnreadIds.length > 0 && initiallyUnreadIds.every(id => viewedUnreadIds.has(id))) {
      // Mark all as read after a short delay to ensure user has actually seen them
      const timer = setTimeout(async () => {
        try {
          const { markActivitiesAsRead } = await import("@/lib/actions/activities");
          await markActivitiesAsRead({
            businessProfileId,
            activityIds: "all"
          });

          // Update local state to reflect they're now read
          setActivities(prev => prev.map(activity => ({ ...activity, is_read: true })));
          setUnreadCount(0);
        } catch (error) {
          console.error("Error marking activities as read:", error);
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [viewedUnreadIds, activitiesSnapshot, businessProfileId]);

  // Refresh activities
  const refreshActivities = useCallback(async (
    newPage: number,
    newFilter: "all" | "like" | "subscribe" | "rating" | "unread",
    newSort: "newest" | "oldest" | "unread_first"
  ) => {
    // Only show loading state if it's a user-initiated refresh or first page load
    // For real-time updates, we don't want to show loading state
    const isUserInitiated = newPage === 1 && activities.length > 0;

    if (isUserInitiated) {
      // Clear activities to show loading state for user-initiated refreshes
      setActivities([]);
    }

    setLoading(true);

    try {
      const pageSize = 15; // Increased page size for better user experience

      const { activities: newActivities, count } = await getBusinessActivities({
        businessProfileId,
        page: newPage,
        pageSize: pageSize,
        sortBy: newSort,
        filterBy: newFilter,
        autoMarkAsRead: false, // Don't auto-mark as read - let user view them first
      });

      // If it's the first page, replace all activities
      // Otherwise, append the new activities to the existing list
      if (newPage === 1) {
        // Ensure we have valid activities array
        const validActivities = newActivities || [];
        console.log("Setting activities:", validActivities.length, "items");
        setActivities(validActivities);
      } else {
        setActivities(prev => [...prev, ...(newActivities || [])]);
      }

      // Update hasMore flag based on the total count
      setHasMore(count > (newPage * pageSize));

      // Update unread count if we've marked activities as read
      // We'll fetch the latest unread count
      const { count: newUnreadCount } = await getUnreadActivitiesCount(businessProfileId);
      setUnreadCount(newUnreadCount);
    } catch (error) {
      console.error("Error refreshing activities:", error);
      toast.error("Failed to load activities");
    } finally {
      setLoading(false);
    }
  }, [businessProfileId, activities.length]);

  // Set up initial loading effect
  useEffect(() => {
    // If we have initial activities, no need to show loading state
    if (initialActivities.length > 0) return;

    // Otherwise, refresh activities to show loading state
    refreshActivities(1, filterBy, sortBy);
  }, [filterBy, initialActivities.length, sortBy, refreshActivities]);

  // Set up real-time subscription for new activities
  useEffect(() => {
    if (!businessProfileId) return;

    const insertSubscription = realtimeService.subscribeToBusinessActivities(
      businessProfileId,
      async (payload) => {
        console.log("Received realtime activity insert:", payload);

        try {
          // Fetch the new activity with user profile
          const { activities: newActivities } = await getBusinessActivities({
            businessProfileId,
            page: 1,
            pageSize: 1,
            sortBy: "newest",
            filterBy: "all",
            autoMarkAsRead: false, // Don't auto-mark as read for realtime notifications
          });

          if (newActivities && newActivities.length > 0) {
            console.log("New activity fetched:", newActivities[0]);

            // Make sure the activity has all required fields for rendering
            const newActivity = {
              ...newActivities[0],
              // Ensure user_profile exists
              user_profile: newActivities[0].user_profile || {
                name: "Anonymous User",
                is_business: false
              }
            };

            // Increment unread count
            setUnreadCount((prev) => prev + 1);

            // Show a toast notification
            const activity = newActivity;
            const userName = activity.user_profile?.is_business
              ? activity.user_profile?.business_name
              : activity.user_profile?.name;

            let message = "";
            switch (activity.activity_type) {
              case "like":
                message = `${userName || "Someone"} liked your business`;
                break;
              case "subscribe":
                message = `${userName || "Someone"} subscribed to your business`;
                break;
              case "rating":
                message = `${userName || "Someone"} rated your business ${activity.rating_value}/5`;
                break;
            }

            toast.info("New Activity", {
              description: message,
              duration: 5000,
            });

            // Instead of just adding to state, do a full refresh immediately
            // This ensures the activity is properly rendered with all data
            refreshActivities(1, filterBy, sortBy);
          }
        } catch (error) {
          console.error("Error handling new activity:", error);
        }
      },
      'activities-page'
    );
    const deleteSubscription = realtimeService.subscribeToTable(
      'business_activities',
      (payload) => {
        console.log("Received realtime activity delete:", payload);

        // Remove the deleted activity from the list
        if (payload.old && 'id' in payload.old && payload.old.id) {
          const deletedActivity = payload.old as BusinessActivity;
          // Use a timeout to ensure the animation completes before removing the item
          setTimeout(() => {
            setActivities((prev) => prev.filter(a => a.id !== deletedActivity.id));

            // Update unread count if the deleted activity was unread
            if (deletedActivity.is_read === false) {
              setUnreadCount((prev) => Math.max(0, prev - 1));
            }
          }, 300); // Match the animation duration in the motion.div
        }
      },
      {
        event: 'DELETE',
        filter: `business_profile_id=eq.${businessProfileId}`
      },
      `activities-page-delete-${businessProfileId}`
    );

    return () => {
      insertSubscription.unsubscribe();
      deleteSubscription.unsubscribe();
    };
  }, [businessProfileId, filterBy, refreshActivities, sortBy]);

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value as "all" | "likes" | "subscriptions" | "ratings");

    // Update filter based on tab
    let newFilter: "all" | "like" | "subscribe" | "rating" | "unread" = "all";
    switch (value) {
      case "likes":
        newFilter = "like";
        break;
      case "subscriptions":
        newFilter = "subscribe";
        break;
      case "ratings":
        newFilter = "rating";
        break;
      default:
        newFilter = "all";
    }

    // Set the new filter and page
    setFilterBy(newFilter);
    setPage(1);
    // refreshActivities will handle clearing activities and loading state
    refreshActivities(1, newFilter, sortBy);
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    const newSortBy = value as "newest" | "oldest" | "unread_first";

    // Set the new sort and page
    setSortBy(newSortBy);
    setPage(1);
    // refreshActivities will handle clearing activities and loading state
    refreshActivities(1, filterBy, newSortBy);
  };



  // Load more activities
  const loadMore = useCallback(() => {
    const nextPage = page + 1;
    setPage(nextPage);
    refreshActivities(nextPage, filterBy, sortBy);
  }, [page, filterBy, sortBy, refreshActivities]);

  // We've removed the mark all as read function
  // as activities are now auto-marked as read when the page is viewed

  // Container animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  // Set up intersection observer for infinite scrolling
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [hasMore, loading, loadMore]);

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Main Card Container */}
      <Card className="border border-neutral-200 dark:border-neutral-800 shadow-md">
        <CardHeader className="pb-0">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900">
                <Bell className="w-4 h-4 text-blue-600 dark:text-blue-300" />
              </div>
              <CardTitle className="text-xl font-semibold">Business Activities</CardTitle>
              {unreadCount > 0 && (
                <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {unreadCount} unread
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-6">
          {/* Tabs and Filters */}
          <Tabs defaultValue="all" value={activeTab} onValueChange={handleTabChange} className="w-full">
            {/* Tabs and Filters - Redesigned Layout */}
            <div className="flex flex-col gap-6 mb-6">
              {/* Beautiful Tabs */}
              <div className="w-full flex justify-center">
                <TabsList className="grid grid-cols-4 w-full max-w-xl p-1 rounded-xl bg-transparent">
                  <TabsTrigger
                    value="all"
                    className="rounded-lg py-2 px-3 m-1 font-medium flex items-center justify-center data-[state=active]:border-[#F5B014] data-[state=active]:border data-[state=active]:bg-transparent data-[state=active]:shadow-none cursor-pointer bg-transparent hover:bg-transparent"
                  >
                    All
                  </TabsTrigger>
                  <TabsTrigger
                    value="likes"
                    className="flex items-center justify-center gap-1.5 rounded-lg py-2 px-3 m-1 font-medium data-[state=active]:border-[#F5B014] data-[state=active]:border data-[state=active]:bg-transparent data-[state=active]:shadow-none cursor-pointer bg-transparent hover:bg-transparent"
                  >
                    <Heart className="w-4 h-4 text-rose-500" />
                    <span>Likes</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="subscriptions"
                    className="flex items-center justify-center gap-1.5 rounded-lg py-2 px-3 m-1 font-medium data-[state=active]:border-[#F5B014] data-[state=active]:border data-[state=active]:bg-transparent data-[state=active]:shadow-none cursor-pointer bg-transparent hover:bg-transparent"
                  >
                    <Users className="w-4 h-4 text-blue-500" />
                    <span>Subs</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="ratings"
                    className="flex items-center justify-center gap-1.5 rounded-lg py-2 px-3 m-1 font-medium data-[state=active]:border-[#F5B014] data-[state=active]:border data-[state=active]:bg-transparent data-[state=active]:shadow-none cursor-pointer bg-transparent hover:bg-transparent"
                  >
                    <Star className="w-4 h-4 text-amber-500" />
                    <span>Ratings</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Sort and Action Buttons - Reorganized Layout */}
              <div className="flex flex-col gap-4">
                {/* Sort Dropdown - Right Aligned in its own row */}
                <div className="flex justify-end">
                  <div className="w-full sm:w-[180px]">
                    <Select value={sortBy} onValueChange={handleSortChange}>
                      <SelectTrigger className="w-full bg-white dark:bg-black border-neutral-200 dark:border-neutral-800">
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="newest">Newest First</SelectItem>
                        <SelectItem value="oldest">Oldest First</SelectItem>
                        <SelectItem value="unread_first">Unread First</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Action Buttons - In a separate row */}
                <div className="flex justify-between w-full">

                  {/* Refresh - Right Side */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // The refreshActivities function now handles clearing activities
                      setPage(1);
                      refreshActivities(1, filterBy, sortBy);
                    }}
                    disabled={loading}
                    className="border-neutral-200 dark:border-neutral-800 hover:bg-neutral-100 dark:hover:bg-neutral-900 rounded-full shadow-sm hover:shadow transition-all duration-300"
                  >
                    <RefreshCw className={cn("w-4 h-4 mr-2", loading && "animate-spin")} />
                    <span>Refresh</span>
                  </Button>
                </div>
              </div>
            </div>

            {/* Activities List */}
            <TabsContent value="all" className="mt-0">
              <ActivityList
                activities={activities}
                loading={loading}
                hasMore={hasMore}
                loadMoreRef={loadMoreRef}
                onActivityViewed={markAsViewed}
                activitiesSnapshot={activitiesSnapshot}
              />
            </TabsContent>

            <TabsContent value="likes" className="mt-0">
              <ActivityList
                activities={activities}
                loading={loading}
                hasMore={hasMore}
                loadMoreRef={loadMoreRef}
                onActivityViewed={markAsViewed}
                activitiesSnapshot={activitiesSnapshot}
              />
            </TabsContent>

            <TabsContent value="subscriptions" className="mt-0">
              <ActivityList
                activities={activities}
                loading={loading}
                hasMore={hasMore}
                loadMoreRef={loadMoreRef}
                onActivityViewed={markAsViewed}
                activitiesSnapshot={activitiesSnapshot}
              />
            </TabsContent>

            <TabsContent value="ratings" className="mt-0">
              <ActivityList
                activities={activities}
                loading={loading}
                hasMore={hasMore}
                loadMoreRef={loadMoreRef}
                onActivityViewed={markAsViewed}
                activitiesSnapshot={activitiesSnapshot}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// Activity Skeleton Component
function ActivitySkeleton() {
  return (
    <div className="border border-neutral-200 dark:border-neutral-800 rounded-lg p-4 bg-white dark:bg-black transition-all duration-300 overflow-hidden">
      <div className="flex gap-4 items-start">
        {/* Avatar Skeleton with Pulse Effect */}
        <div className="shrink-0">
          <Skeleton className="h-10 w-10 rounded-lg flex-shrink-0" />
        </div>

        <div className="flex-1 min-w-0">
          {/* User Info */}
          <div className="flex flex-col space-y-1 mb-1">
            <div className="flex items-center gap-1 flex-wrap">
              <Skeleton className="h-5 w-32" />
            </div>

            <Skeleton className="h-4 w-3/4" />
          </div>

          {/* Footer with Badge and Time */}
          <div className="flex flex-wrap items-center justify-between mt-2 gap-2">
            <div className="flex items-center flex-wrap gap-2">
              <Skeleton className="h-6 w-20 rounded-full" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Activity List Component
function ActivityList({
  activities,
  loading,
  hasMore,
  loadMoreRef,
  onActivityViewed,
  activitiesSnapshot
}: {
  activities: BusinessActivity[];
  loading: boolean;
  hasMore: boolean;
  loadMoreRef: React.RefObject<HTMLDivElement | null>;
  onActivityViewed?: (_activityId: string) => void;
  activitiesSnapshot: BusinessActivity[];
}) {
  // Separate activities into new and old based on INITIAL read status when page loaded
  const newActivities = activities.filter(activity => {
    const snapshotActivity = activitiesSnapshot.find(a => a.id === activity.id);
    return snapshotActivity?.is_read === false;
  });
  const oldActivities = activities.filter(activity => {
    const snapshotActivity = activitiesSnapshot.find(a => a.id === activity.id);
    return snapshotActivity?.is_read === true;
  });

  // Render section header
  const renderSectionHeader = (title: string) => (
    <div className="px-4 py-3 bg-neutral-50 dark:bg-neutral-900/50 border-b border-neutral-200 dark:border-neutral-800">
      <h3 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300">
        {title}
      </h3>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Loading Skeletons - Show when no activities and loading */}
      {activities.length === 0 && loading ? (
        <div className="space-y-4">
          <ActivitySkeleton />
          <ActivitySkeleton />
          <ActivitySkeleton />
          <ActivitySkeleton />
        </div>
      ) : activities.length === 0 && !loading ? (
        // Empty state - No activities and not loading
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <div className="relative mb-6">
            <div className="absolute -inset-6 rounded-full bg-blue-500/10 animate-pulse blur-lg"></div>
            <Bell className="w-16 h-16 text-blue-500/70 dark:text-blue-400/70 relative z-10" />
          </div>
          <h3 className="text-xl font-semibold text-neutral-700 dark:text-neutral-300 mb-2">No activities yet</h3>
          <p className="text-sm text-neutral-500 dark:text-neutral-400 max-w-md">
            When someone likes, subscribes, or rates your business, their activity will appear here.
          </p>
          <p className="text-xs text-neutral-400 dark:text-neutral-500 mt-4 max-w-sm">
            Share your business card with more people to increase engagement and visibility.
          </p>
        </div>
      ) : (
        // Activity Items with Sections - Show when we have activities
        <>
          <AnimatePresence initial={false} mode="wait">
            {/* New Activities Section */}
            {newActivities.length > 0 && (
              <div key="new-section">
                {renderSectionHeader('New')}
                {newActivities.map((activity) =>
                  activity && activity.id ? (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, height: 0, overflow: 'hidden' }}
                      transition={{ duration: 0.3 }}
                      className="mb-4 last:mb-0"
                    >
                      <ActivityItem
                        activity={activity}
                        onView={onActivityViewed}
                      />
                    </motion.div>
                  ) : null
                )}
              </div>
            )}

            {/* Old Activities Section */}
            {oldActivities.length > 0 && (
              <div key="old-section">
                {renderSectionHeader('Earlier')}
                {oldActivities.map((activity) =>
                  activity && activity.id ? (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, height: 0, overflow: 'hidden' }}
                      transition={{ duration: 0.3 }}
                      className="mb-4 last:mb-0"
                    >
                      <ActivityItem
                        activity={activity}
                        onView={onActivityViewed}
                      />
                    </motion.div>
                  ) : null
                )}
              </div>
            )}
          </AnimatePresence>

          {/* Infinite Scroll Trigger */}
          {hasMore && (
            <div
              ref={loadMoreRef}
              className="flex justify-center items-center py-6"
            >
              {loading && (
                <div className="flex flex-col items-center">
                  <div className="relative">
                    <div className="absolute -inset-1 rounded-full bg-blue-500/20 animate-pulse blur-sm"></div>
                    <Loader2 className="h-8 w-8 animate-spin text-blue-500 relative z-10" />
                  </div>
                  <span className="text-sm text-neutral-600 dark:text-neutral-400 mt-3 font-medium">
                    Loading more activities...
                  </span>
                </div>
              )}
            </div>
          )}

          {/* End of List Message */}
          {!hasMore && activities.length > 0 && (
            <div className="text-center py-8 border-t border-neutral-200 dark:border-neutral-800 mt-4">
              <p className="text-sm text-neutral-500 dark:text-neutral-400">
                You&apos;ve reached the end of your activities
              </p>
              <p className="text-xs text-neutral-400 dark:text-neutral-500 mt-1">
                New activities will appear here when someone interacts with your business
              </p>
            </div>
          )}
        </>
      )}
    </div>
  );
}
