import { motion } from "framer-motion";
import { Upload } from "lucide-react";
import { cn } from "@/lib/utils";

interface UploadBoxProps {
  canAddMore: boolean;
  isDragging: boolean;
  imagesCount: number;
  galleryLimit: number;
  onDragEnter: (_e: React.DragEvent<HTMLDivElement>) => void;
  onDragOver: (_e: React.DragEvent<HTMLDivElement>) => void;
  onDragLeave: (_e: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (_e: React.DragEvent<HTMLDivElement>) => void;
  onClick: () => void;
}

export default function UploadBox({
  canAddMore,
  isDragging,
  imagesCount,
  galleryLimit,
  onDragEnter,
  onDragOver,
  onDragLeave,
  onDrop,
  onClick,
}: UploadBoxProps) {
  if (!canAddMore) return null;

  return (
    <motion.div
      className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-4 sm:p-5 transition-all duration-300 relative overflow-hidden"
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.15 }}
    >
      <div
        className={cn(
          "flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-8 transition-all duration-200 cursor-pointer",
          isDragging
            ? "border-amber-500 bg-amber-500/10"
            : "border-neutral-200 dark:border-neutral-700 bg-muted/30 hover:bg-muted/50"
        )}
        onDragEnter={onDragEnter}
        onDragOver={onDragOver}
        onDragLeave={onDragLeave}
        onDrop={onDrop}
        onClick={onClick}
      >
        <div className={cn(
          "w-12 h-12 rounded-full flex items-center justify-center mb-3 transition-all duration-200",
          isDragging
            ? "bg-amber-500/20 scale-110"
            : "bg-amber-500/10"
        )}>
          <Upload className={cn(
            "h-6 w-6 text-amber-500 transition-all duration-200",
            isDragging ? "opacity-100" : "opacity-80"
          )} />
        </div>
        <h3 className={cn(
          "text-lg font-medium mb-2 transition-all duration-200",
          isDragging ? "text-amber-500" : "text-foreground"
        )}>
          {isDragging ? "Drop image here" : "Upload New Photo"}
        </h3>
        <p className="text-sm text-muted-foreground text-center">
          {isDragging ? (
            "Release to upload"
          ) : (
            <>
              Click here or drag and drop images to upload
              <br />
              <span className="text-xs">JPG, PNG, WebP, or GIF (max. 15MB)</span>
            </>
          )}
        </p>
        <div className="text-xs text-amber-600 dark:text-amber-400 mt-2 font-medium">
          {imagesCount} of {galleryLimit} photos used
        </div>
      </div>
    </motion.div>
  );
}
