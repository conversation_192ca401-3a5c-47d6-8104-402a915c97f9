'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { User } from '@supabase/supabase-js';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { BusinessAppSidebar } from '@/components/sidebar/BusinessAppSidebar';
import { CustomerAppSidebar } from '@/components/sidebar/CustomerAppSidebar';
import MinimalHeader from '@/app/components/MinimalHeader';
import { ThemeToggle } from '@/app/components/ThemeToggle';
import BottomNav from '@/app/components/BottomNav';
import { cn } from '@/lib/utils';
import { PaymentMethodLimitationsProvider } from '@/app/context/PaymentMethodLimitationsContext';

interface ConditionalPostLayoutProps {
  children: React.ReactNode;
}

interface UserProfile {
  business_name?: string;
  logo_url?: string;
  avatar_url?: string;
}

/**
 * Conditional layout component that shows:
 * - Dashboard layout (header + sidebar) for authenticated users
 * - Simple public layout for non-authenticated users
 */
export default function ConditionalPostLayout({ children }: ConditionalPostLayoutProps) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [userType, setUserType] = useState<'business' | 'customer' | null>(null);
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    const getUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        setUser(user);

        if (user) {
          // Check if user has business profile
          const { data: businessProfile } = await supabase
            .from('business_profiles')
            .select('business_name, logo_url')
            .eq('user_id', user.id)
            .single();

          if (businessProfile) {
            setProfile(businessProfile);
            setUserType('business');
          } else {
            // Check for customer profile
            const { data: customerProfile } = await supabase
              .from('customer_profiles')
              .select('avatar_url')
              .eq('user_id', user.id)
              .single();

            if (customerProfile) {
              setProfile(customerProfile);
              setUserType('customer');
            }
          }
        }
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setLoading(false);
      }
    };

    getUser();
  }, [supabase]);

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-white dark:bg-black">
        <div className="max-w-2xl mx-auto py-4 px-4">
          {children}
        </div>
      </div>
    );
  }

  // If user is not authenticated, show simple public layout
  if (!user) {
    return (
      <div className="min-h-screen bg-white dark:bg-black">
        <div className="max-w-2xl mx-auto py-4 px-4">
          {children}
        </div>
      </div>
    );
  }

  // If user is authenticated, show dashboard layout
  if (userType === 'business' && profile) {
    // Business dashboard layout
    return (
      <PaymentMethodLimitationsProvider>
        <SidebarProvider>
          <BusinessAppSidebar
            businessName={profile.business_name || null}
            logoUrl={profile.logo_url || null}
            memberName={user.user_metadata?.full_name || user.user_metadata?.name || null}
            userPlan="free" // Default plan, could be fetched from subscription data
          />
          <SidebarInset>
            <MinimalHeader
              businessName={profile.business_name || null}
              logoUrl={profile.logo_url || null}
              userName={user.user_metadata?.full_name || user.user_metadata?.name || null}
            >
              <SidebarTrigger className="ml-auto md:ml-0" />
              <ThemeToggle variant="dashboard" />
            </MinimalHeader>

            <main className={cn(
              "flex-grow p-3 sm:p-4 md:p-5 pb-16 md:pb-6",
              "bg-white dark:bg-black"
            )}>
              {children}
            </main>
            <BottomNav />
          </SidebarInset>
        </SidebarProvider>
      </PaymentMethodLimitationsProvider>
    );
  } else if (userType === 'customer') {
    // Customer dashboard layout
    return (
      <SidebarProvider>
        <CustomerAppSidebar
          userName={user.user_metadata?.full_name || user.user_metadata?.name || null}
          userAvatarUrl={profile?.avatar_url || null}
        />
        <SidebarInset>
          <MinimalHeader
            userName={user.user_metadata?.full_name || user.user_metadata?.name || null}
            businessName={null}
            logoUrl={profile?.avatar_url || null}
          >
            <SidebarTrigger className="ml-auto md:ml-0" />
            <ThemeToggle variant="dashboard" />
          </MinimalHeader>

          <main className={cn(
            "flex-grow p-3 sm:p-4 md:p-5 pb-16 md:pb-6",
            "bg-white dark:bg-black"
          )}>
            {children}
          </main>
          <BottomNav />
        </SidebarInset>
      </SidebarProvider>
    );
  }

  // Fallback to public layout if user type is unknown
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <div className="max-w-2xl mx-auto py-4 px-4">
        {children}
      </div>
    </div>
  );
}
