"use client";

import React, { createContext, useContext, useState, ReactNode, useCallback, useEffect, useRef } from "react";

export type ProcessingStatus =
  | "idle"
  | "processing"
  | "success"
  | "error"
  | "razorpay_server_error" // Status for Razorpay server errors
  | "waiting_for_webhook"
  | "payment_authorized"
  | "payment_captured"
  | "future_payment_authorized" // New status for trial users with future payments
  | "subscription_created"
  | "subscription_authenticated"
  | "subscription_active"
  | "subscription_activated"
  | "subscription_pending"
  | "subscription_halted"
  | "subscription_cancelled"
  | "subscription_completed"
  | "subscription_expired"
  | "subscription_paused"
  | "subscription_resumed" // Status for resumed subscription
  | "refund_processing"
  | "refund_processed";

interface SubscriptionProcessingContextType {
  status: ProcessingStatus;
  message: string;
  startProcessing: (_message?: string) => void;
  completeProcessing: (_success: boolean, _message?: string) => void;
  resetProcessing: () => void;

  // Method for Razorpay server error
  setRazorpayServerError: (_message?: string) => void;

  // Methods for payment states
  setWaitingForWebhook: (_message?: string) => void;
  setPaymentAuthorized: (_message?: string) => void;
  setPaymentCaptured: (_message?: string) => void;
  setFuturePaymentAuthorized: (_message?: string) => void; // New method for trial users
  setPaymentMethodUpdated: (_message?: string) => void;

  // Methods for subscription states
  setSubscriptionCreated: (_message?: string) => void;
  setSubscriptionAuthenticated: (_message?: string) => void;
  setSubscriptionActive: (_message?: string) => void;
  setSubscriptionActivated: (_message?: string) => void;
  setSubscriptionPending: (_message?: string) => void;
  setSubscriptionHalted: (_message?: string) => void;
  setSubscriptionCancelled: (_message?: string) => void;
  setSubscriptionCompleted: (_message?: string) => void;
  setSubscriptionExpired: (_message?: string) => void;
  setSubscriptionPaused: (_message?: string) => void;
  setSubscriptionResumed: (_message?: string) => void;

  // Methods for refund states
  setRefundProcessing: (_message?: string) => void;
  setRefundProcessed: (_message?: string) => void;
}

const SubscriptionProcessingContext = createContext<
  SubscriptionProcessingContextType | undefined
>(undefined);

export function useSubscriptionProcessing() {
  const context = useContext(SubscriptionProcessingContext);
  if (context === undefined) {
    throw new Error(
      "useSubscriptionProcessing must be used within a SubscriptionProcessingProvider"
    );
  }
  return context;
}

interface SubscriptionProcessingProviderProps {
  children: ReactNode;
}

export function SubscriptionProcessingProvider({
  children,
}: SubscriptionProcessingProviderProps) {
  const [status, setStatus] = useState<ProcessingStatus>("idle");
  const [message, setMessage] = useState<string>("");

  // CRITICAL FIX: Add timeout cleanup to prevent memory leaks
  // Use useRef to avoid dependency issues in useCallback
  const activeTimeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  // Cleanup function to clear all active timeouts
  const clearAllTimeouts = useCallback(() => {
    activeTimeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    activeTimeoutsRef.current.clear();
  }, []); // No dependencies needed since we're using ref

  // Enhanced timeout management
  const addTimeout = useCallback((callback: () => void, delay: number) => {
    const timeoutId = setTimeout(() => {
      callback();
      // Remove from active timeouts when completed
      activeTimeoutsRef.current.delete(timeoutId);
    }, delay);

    // Add to active timeouts
    activeTimeoutsRef.current.add(timeoutId);
    return timeoutId;
  }, []);

  const resetProcessing = useCallback(() => {
    setStatus("idle");
    setMessage("");
    // Clear all active timeouts when resetting
    clearAllTimeouts();
  }, [clearAllTimeouts]);

  const startProcessing = (
    message = "Please wait while we process your subscription"
  ) => {
    setStatus("processing");
    setMessage(message);
  };

  const completeProcessing = useCallback((success: boolean, message?: string) => {
    if (success) {
      // For success, we'll use the subscription_created status instead of generic success
      setStatus("subscription_created");
    } else {
      setStatus("error");
    }

    if (message) {
      setMessage(message);
    } else {
      setMessage(
        success
          ? "Your subscription has been successfully created."
          : "There was an error processing your subscription. Please try again."
      );
    }

    // Only auto-hide error messages after 5 seconds
    // Success messages should stay visible until user takes action
    if (!success) {
      addTimeout(() => {
        resetProcessing();
      }, 5000);
    }
  }, [addTimeout, resetProcessing]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearAllTimeouts();
    };
  }, [clearAllTimeouts]);

  // New methods for specific subscription states
  const setWaitingForWebhook = useCallback((
    _message = "Waiting for payment confirmation..."
  ) => {
    setStatus("waiting_for_webhook");
    setMessage(_message);

    // Auto-reset after 30 seconds to prevent the message from being stuck
    // This is a safety measure in case the webhook is not received
    const timeoutId = addTimeout(() => {
      // Use a callback to get the current status to avoid closure issues
      setStatus(currentStatus => {
        if (currentStatus === "waiting_for_webhook") {
          console.log("[SUBSCRIPTION_PROCESSING] Auto-resetting webhook waiting state after timeout");
          setMessage("");
          return "idle";
        }
        return currentStatus;
      });
    }, 30000); // 30 seconds timeout

    // Return the timeout ID so it can be cleared if needed
    return timeoutId;
  }, [addTimeout]);

  const setPaymentAuthorized = (
    _message = "Payment authorized, setting up your subscription..."
  ) => {
    setStatus("payment_authorized");
    setMessage(_message);
  };

  const setPaymentCaptured = (
    _message = "Payment captured, activating your subscription..."
  ) => {
    setStatus("payment_captured");
    setMessage(_message);
  };

  // New method for future payments (trial users)
  const setFuturePaymentAuthorized = useCallback((
    _message = "Subscription authorized. Payment will be processed when your trial ends."
  ) => {
    setStatus("future_payment_authorized");
    setMessage(_message);

    // CRITICAL FIX: Use centralized timeout management to prevent memory leaks
    addTimeout(() => {
      resetProcessing();
    }, 8000);
  }, [addTimeout, resetProcessing]);

  const setSubscriptionCreated = (
    _message = "Subscription created, waiting for activation..."
  ) => {
    setStatus("subscription_created");
    setMessage(_message);
  };

  const setSubscriptionAuthenticated = (
    _message = "Subscription authenticated, waiting for activation..."
  ) => {
    setStatus("subscription_authenticated");
    setMessage(_message);
  };

  const setSubscriptionActivated = (
    _message = "Subscription activated successfully!"
  ) => {
    setStatus("subscription_activated");
    setMessage(_message);
    // Don't auto-hide - let user see the status
  };

  const setRefundProcessing = (
    _message = "Processing your refund request..."
  ) => {
    setStatus("refund_processing");
    setMessage(_message);
  };

  const setRefundProcessed = (_message = "Refund processed successfully!") => {
    setStatus("refund_processed");
    setMessage(_message);
    // Don't auto-hide - let user see the status
  };

  // Add new subscription status methods
  const setSubscriptionActive = (_message = "Your subscription is active!") => {
    setStatus("subscription_active");
    setMessage(_message);
  };

  const setSubscriptionPending = (
    _message = "Your subscription is pending..."
  ) => {
    setStatus("subscription_pending");
    setMessage(_message);
  };

  const setSubscriptionHalted = useCallback((
    _message = "Your subscription has been halted."
  ) => {
    setStatus("subscription_halted");
    setMessage(_message);

    // Auto-hide after 8 seconds
    addTimeout(() => {
      resetProcessing();
    }, 8000);
  }, [addTimeout, resetProcessing]);

  const setSubscriptionCancelled = useCallback((
    _message = "Your subscription has been cancelled successfully."
  ) => {
    setStatus("subscription_cancelled");
    setMessage(_message);

    // Auto-hide after 8 seconds
    addTimeout(() => {
      resetProcessing();
    }, 8000);
  }, [addTimeout, resetProcessing]);

  const setSubscriptionCompleted = useCallback((
    _message = "Your subscription has been completed."
  ) => {
    setStatus("subscription_completed");
    setMessage(_message);

    // Auto-hide after 8 seconds
    addTimeout(() => {
      resetProcessing();
    }, 8000);
  }, [addTimeout, resetProcessing]);

  const setSubscriptionExpired = useCallback((
    _message = "Your subscription has expired."
  ) => {
    setStatus("subscription_expired");
    setMessage(_message);

    // Auto-hide after 8 seconds
    addTimeout(() => {
      resetProcessing();
    }, 8000);
  }, [addTimeout, resetProcessing]);

  // Add method for Razorpay server error
  const setRazorpayServerError = useCallback((
    _message = "Razorpay servers are currently experiencing issues. Please try again in a few minutes."
  ) => {
    setStatus("razorpay_server_error");
    setMessage(_message);

    // Auto-hide after 10 seconds (longer than regular errors)
    addTimeout(() => {
      resetProcessing();
    }, 10000);
  }, [addTimeout, resetProcessing]);

  // Add methods for payment method update
  const setPaymentMethodUpdated = (
    _message = "Updating your payment method..."
  ) => {
    setStatus("processing");
    setMessage(_message);
  };

  // Add methods for subscription pause/resume
  const setSubscriptionPaused = useCallback((
    _message = "Your subscription has been paused. You can resume it anytime."
  ) => {
    setStatus("subscription_paused");
    setMessage(_message);

    // Auto-hide after 8 seconds
    addTimeout(() => {
      resetProcessing();
    }, 8000);
  }, [addTimeout, resetProcessing]);

  const setSubscriptionResumed = useCallback((
    _message = "Your subscription has been resumed successfully."
  ) => {
    setStatus("subscription_resumed");
    setMessage(_message);

    // Auto-hide after 8 seconds
    addTimeout(() => {
      resetProcessing();
    }, 8000);
  }, [addTimeout, resetProcessing]);

  const value = {
    status,
    message,
    startProcessing,
    completeProcessing,
    resetProcessing,
    setRazorpayServerError,
    setWaitingForWebhook,
    setPaymentAuthorized,
    setPaymentCaptured,
    setFuturePaymentAuthorized,
    setPaymentMethodUpdated,
    setSubscriptionCreated,
    setSubscriptionAuthenticated,
    setSubscriptionActive,
    setSubscriptionActivated,
    setSubscriptionPending,
    setSubscriptionHalted,
    setSubscriptionCancelled,
    setSubscriptionCompleted,
    setSubscriptionExpired,
    setSubscriptionPaused,
    setSubscriptionResumed,
    setRefundProcessing,
    setRefundProcessed,
  };

  return (
    <SubscriptionProcessingContext.Provider value={value}>
      {children}
    </SubscriptionProcessingContext.Provider>
  );
}
