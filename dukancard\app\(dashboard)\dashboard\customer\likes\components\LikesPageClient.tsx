"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Heart } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import LikeListClient from "../LikeListClient";
import { LikeSearch, LikePagination, LikeListSkeleton } from "@/app/components/shared/likes";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { formatIndianNumberShort } from "@/lib/utils";
import { LikeWithProfile } from "../actions";

interface LikesPageClientProps {
  initialLikes: LikeWithProfile[];
  totalCount: number;
  currentPage: number;
  searchTerm: string;
}

export default function LikesPageClient({
  initialLikes,
  totalCount,
  currentPage,
  searchTerm: initialSearchTerm
}: LikesPageClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / 10);

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  // Handle search
  const handleSearch = useCallback((newSearchTerm: string) => {
    setIsLoading(true);
    setSearchTerm(newSearchTerm);

    const params = new URLSearchParams(searchParams);
    if (newSearchTerm) {
      params.set('search', newSearchTerm);
    } else {
      params.delete('search');
    }
    params.delete('page'); // Reset to first page when searching

    router.push(`/dashboard/customer/likes?${params.toString()}`);
  }, [router, searchParams]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setIsLoading(true);

    const params = new URLSearchParams(searchParams);
    if (page > 1) {
      params.set('page', page.toString());
    } else {
      params.delete('page');
    }

    router.push(`/dashboard/customer/likes?${params.toString()}`);
  }, [router, searchParams]);

  // Reset loading state when data changes
  useEffect(() => {
    setIsLoading(false);
  }, [initialLikes]);

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="relative space-y-6 max-w-6xl mx-auto"
    >

      {/* Main content */}
      <motion.div variants={itemVariants} className="relative z-10">
        <Card className="border shadow-md bg-white dark:bg-black border-neutral-200 dark:border-neutral-800">
          <CardHeader className="pb-4 border-b border-neutral-100 dark:border-neutral-800">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
              <div className="p-2 rounded-lg bg-rose-100 dark:bg-rose-900/30 text-rose-500 dark:text-rose-400 self-start">
                <Heart className="w-5 h-5" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Liked Businesses ({formatIndianNumberShort(totalCount)})
                </h3>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                  Businesses you&apos;ve liked
                </p>
              </div>
            </div>

            {/* Search */}
            <div className="mt-4">
              <LikeSearch
                onSearch={handleSearch}
                initialSearchTerm={searchTerm}
                placeholder="Search businesses by name..."
              />
            </div>
          </CardHeader>

          <CardContent className="pt-4">
            {/* Like count */}
            {searchTerm && !isLoading && (
              <div className="mb-4 text-sm text-neutral-500 dark:text-neutral-400">
                Found {formatIndianNumberShort(totalCount)} {totalCount === 1 ? 'business' : 'businesses'}
                {searchTerm ? ` matching "${searchTerm}"` : ''}
              </div>
            )}

            {/* Show skeleton loader when loading */}
            {isLoading ? (
              <LikeListSkeleton />
            ) : (
              <>
                {/* Like List */}
                <LikeListClient initialLikes={initialLikes} />

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-6">
                    <LikePagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
