import AboutPageClient from "./AboutUsClient";
import { Metadata } from "next"; // Import Metadata type

export async function generateMetadata(): Promise<Metadata> {
  const title = "About Dukancard"; // More specific title
  const description =
    "Learn about Dukancard's mission to empower Indian businesses with digital cards. Discover our story, values, and the team driving innovation."; // Refined description
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/about`;
  const ogImage = `${siteUrl}/opengraph-image.png`; // Default OG image

  return {
    title, // Uses template from root layout: "About Dukancard - Dukancard"
    description,
    keywords: [ // Added keywords
      "about Dukancard",
      "Dukancard mission",
      "digital business card India",
      "online presence tool",
      "small business solutions",
      "Dukancard team",
    ],
    alternates: {
      canonical: "/about", // Relative canonical path
    },
    openGraph: {
      title: title, // OG title doesn't use template by default
      description: description,
      url: pageUrl,
      siteName: "Dukancard", // Added siteName
      type: "website", // Added type
      locale: "en_IN", // Added locale
      images: [ // Added image
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: "About Dukancard - Digital Business Cards",
        },
      ],
    },
    twitter: {
      card: "summary_large_image", // Added card type
      title: title,
      description: description,
      images: [ogImage], // Added image
    },
    // Add WebPage Schema
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: title,
        description: description,
        url: pageUrl,
        isPartOf: {
          "@type": "WebSite",
          name: "Dukancard",
          url: siteUrl,
        },
      }),
    },
  };
}

export default async function AboutUsPage() {
  return <AboutPageClient />;
}
