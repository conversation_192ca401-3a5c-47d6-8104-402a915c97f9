import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import GalleryPageClient from "./GalleryPageClient";
import { getGalleryImages } from "./actions";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Gallery - Dukancard Business",
  description: "Manage your business photo gallery",
  robots: "noindex, nofollow",
};

export default async function GalleryPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/login?message=Authentication required");
  }

  // Fetch the business profile
  const { data: profileData, error: profileError } = await supabase
    .from("business_profiles")
    .select("business_name, logo_url")
    .eq("id", user.id)
    .single();

  if (profileError) {
    console.error("Error fetching business profile:", profileError);
    return redirect("/dashboard/business?message=Failed to load profile");
  }

  // Get the current plan from payment_subscriptions
  const { data: subscriptionData, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("plan_id")
    .eq("business_profile_id", user.id)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
  }

  // Default to free plan if no subscription found
  const planId = subscriptionData?.plan_id || "free";

  // Fetch gallery images
  const { images, error } = await getGalleryImages();

  if (error) {
    console.error("Error fetching gallery images:", error);
  }

  return (
    <div className="w-full max-w-screen-xl mx-auto">
      <GalleryPageClient
        initialImages={images || []}
        userPlan={planId}
        businessName={profileData?.business_name || "Your Business"}
      />
    </div>
  );
}
