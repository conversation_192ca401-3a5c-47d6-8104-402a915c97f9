import React from 'react';
import { View, Text, TouchableOpacity, Linking } from 'react-native';
import { Phone, MessageCircle } from 'lucide-react-native';
import { BusinessDiscoveryData } from '../../lib/services/businessDiscovery';
import { Toast } from '../../lib/utils/toast';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface ContactActionButtonsProps {
  businessData: BusinessDiscoveryData;
  isDark: boolean;
}

export default function ContactActionButtons({ businessData, isDark }: ContactActionButtonsProps) {
  const styles = createPublicCardViewStyles(isDark);

  const handlePhonePress = () => {
    if (businessData.phone) {
      Linking.openURL(`tel:${businessData.phone}`);
    }
  };

  const handleWhatsAppPress = () => {
    if (businessData.whatsapp_number) {
      const message = `Hi ${businessData.business_name}, I found you through Dukancard!`;
      const url = `whatsapp://send?phone=${businessData.whatsapp_number}&text=${encodeURIComponent(message)}`;
      Linking.openURL(url).catch(() => {
        Toast.show('WhatsApp is not installed', 'error');
      });
    }
  };

  return (
    <View style={styles.contactButtonsSection}>
      {businessData.phone && (
        <TouchableOpacity style={styles.callButton} onPress={handlePhonePress}>
          <Phone color="#fff" size={18} />
          <Text style={styles.callButtonText}>Call Now</Text>
        </TouchableOpacity>
      )}
      {businessData.whatsapp_number && (
        <TouchableOpacity style={styles.messageButton} onPress={handleWhatsAppPress}>
          <MessageCircle color="#fff" size={18} />
          <Text style={styles.messageButtonText}>Message Now</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}
