"use client";

import ModernSearchSection from "./ModernSearchSection";
import DigitalCardFeature from "./DigitalCardFeature";
import HomeCategoriesSection from "./HomeCategoriesSection";
import HeroActionButtons from "./HeroActionButtons";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

interface StickyHeroSectionClientProps {
  isAuthenticated: boolean;
  userType: "business" | "customer" | null;
  businessCardData?: BusinessCardData;
  userPlan?: "free" | "basic" | "growth" | "pro" | "enterprise" | "trial";
}

export default function StickyHeroSectionClient({
  isAuthenticated,
  userType,
  businessCardData,
  userPlan
}: StickyHeroSectionClientProps) {

  return (
    <section className="relative w-full bg-white dark:bg-black pt-24 pb-16 md:pt-32 md:pb-24">
      {/* Simplified background - just a static gradient */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        {/* Simple static glow */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[400px] bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-full blur-[120px]"></div>

        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 bg-[url('/decorative/subtle-pattern.svg')] bg-repeat opacity-5 dark:opacity-10"></div>
      </div>

      {/* Mobile layout (stacked) with simplified animations */}
      <div className="md:hidden container mx-auto px-4">
        {/* Search section on top */}
        <div className="mb-6 w-full">
          <ModernSearchSection minimal={true} />
        </div>

        {/* Popular Categories section below search */}
        <div className="mb-12">
          <HomeCategoriesSection />
        </div>

        {/* Card section below */}
        <div>
          <div className="mb-6 text-center">
            {isAuthenticated && userType === "business" && businessCardData ? (
              <>
                <h2 className="text-2xl md:text-3xl font-bold mb-2">
                  Your Digital Business Card is <span className="text-[var(--brand-gold)]">Live!</span>
                </h2>
                <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                  Share your card with customers and <span className="font-semibold">grow your business</span> — <span className="text-[var(--brand-gold)] font-semibold">completely FREE</span>
                </p>
              </>
            ) : (
              <>
                <h2 className="text-2xl md:text-3xl font-bold mb-2">
                  Get Your Business <span className="text-[var(--brand-gold)]">Online Instantly</span>
                </h2>
                <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                  Launch your digital storefront in <span className="font-semibold">just 30 seconds</span> — <span className="text-[var(--brand-gold)] font-semibold">completely FREE</span>
                </p>
              </>
            )}
          </div>
          <DigitalCardFeature
            minimal={true}
            hideTitle={true}
            isAuthenticated={isAuthenticated}
            userType={userType}
            businessCardData={businessCardData}
            userPlan={userPlan}
          />

          {/* Action buttons below the card */}
          <div className="mt-6">
            <HeroActionButtons
              isAuthenticated={isAuthenticated}
              userType={userType}
              businessCardData={businessCardData}
            />
          </div>
        </div>
      </div>

      {/* Desktop layout with simplified animations */}
      <div className="hidden md:block container mx-auto px-4 max-w-7xl">
        <div className="flex flex-col lg:flex-row gap-8 lg:gap-16">
          {/* Left column - Search and Categories */}
          <div className="w-full lg:w-1/2">
            <div className="lg:sticky lg:top-24">
              {/* Search section */}
              <div className="mb-6 w-full">
                <ModernSearchSection minimal={true} />
              </div>

              {/* Popular Categories section below search */}
              <div>
                <HomeCategoriesSection />
              </div>
            </div>
          </div>

          {/* Right column - Card */}
          <div className="w-full lg:w-1/2">
            <div className="lg:sticky lg:top-24">
              <div className="mb-6 text-center">
                {isAuthenticated && userType === "business" && businessCardData ? (
                  <>
                    <h2 className="text-2xl md:text-3xl font-bold mb-2">
                      Your Digital Business Card is <span className="text-[var(--brand-gold)]">Live!</span>
                    </h2>
                    <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                      Share your card with customers and <span className="font-semibold">grow your business</span> — <span className="text-[var(--brand-gold)] font-semibold">completely FREE</span>
                    </p>
                  </>
                ) : (
                  <>
                    <h2 className="text-2xl md:text-3xl font-bold mb-2">
                      Get Your Business <span className="text-[var(--brand-gold)]">Online Instantly</span>
                    </h2>
                    <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                      Launch your digital storefront in <span className="font-semibold">just 30 seconds</span> — <span className="text-[var(--brand-gold)] font-semibold">completely FREE</span>
                    </p>
                  </>
                )}
              </div>
              <DigitalCardFeature
                minimal={true}
                hideTitle={true}
                isAuthenticated={isAuthenticated}
                userType={userType}
                businessCardData={businessCardData}
                userPlan={userPlan}
              />

              {/* Action buttons below the card */}
              <div className="mt-6">
                <HeroActionButtons
                  isAuthenticated={isAuthenticated}
                  userType={userType}
                  businessCardData={businessCardData}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
