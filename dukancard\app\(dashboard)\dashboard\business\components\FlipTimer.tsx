"use client";

import React, { useState, useEffect, useMemo } from "react";
import { intervalToDuration } from "date-fns";
import { CalendarClock } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface FlipTimerProps {
  endDate: string | Date;
  label?: string;
  tooltipText?: string;
  className?: string;
}

interface TimeUnit {
  label: string;
  value: number;
}

const FlipTimer: React.FC<FlipTimerProps> = ({
  endDate,
  label = "Trial ends in:",
  tooltipText = "Your trial will expire soon. Upgrade to continue using all features.",
  className,
}) => {
  const [now, setNow] = useState(new Date());
  const [timeUnits, setTimeUnits] = useState<TimeUnit[]>([]);

  const targetDate = useMemo(() => {
    // Parse the date and ensure it's treated as IST (UTC+5:30)
    let date: Date;

    if (typeof endDate === "string") {
      // For string dates, create a new Date object
      date = new Date(endDate);
    } else {
      // For Date objects, use as is
      date = endDate;
    }

    // Validate the date
    return !isNaN(date.getTime()) ? date : null;
  }, [endDate]);

  useEffect(() => {
    if (!targetDate) return;

    const updateTime = () => {
      const currentTime = new Date();
      setNow(currentTime);

      if (targetDate && currentTime < targetDate) {
        const duration = intervalToDuration({
          start: currentTime,
          end: targetDate,
        });

        const units: TimeUnit[] = [];

        // Always show a complete breakdown of time units
        // Calculate years, months, days, hours, minutes, seconds
        const diffMs = targetDate.getTime() - currentTime.getTime();

        // Calculate years (approximate)
        const millisecondsInYear = 1000 * 60 * 60 * 24 * 365.25; // Account for leap years
        const years = Math.floor(diffMs / millisecondsInYear);
        let remainder = diffMs % millisecondsInYear;

        // Calculate months (approximate)
        const millisecondsInMonth = 1000 * 60 * 60 * 24 * 30.44; // Average month length
        const months = Math.floor(remainder / millisecondsInMonth);
        remainder = remainder % millisecondsInMonth;

        // Calculate days
        const millisecondsInDay = 1000 * 60 * 60 * 24;
        const days = Math.floor(remainder / millisecondsInDay);
        remainder = remainder % millisecondsInDay;

        // Hours, minutes, seconds from duration object
        const hours = duration.hours || 0;
        const minutes = duration.minutes || 0;
        const seconds = duration.seconds || 0;

        // Add time units to the display, but limit to 5 units maximum to avoid overcrowding
        const allUnits = [];

        if (years > 0) {
          allUnits.push({ label: "years", value: years });
        }

        if (months > 0) {
          allUnits.push({ label: "months", value: months });
        }

        if (days > 0) {
          allUnits.push({ label: "days", value: days });
        }

        // Always include hours, minutes, and seconds
        allUnits.push(
          { label: "hrs", value: hours },
          { label: "min", value: minutes },
          { label: "sec", value: seconds }
        );

        // Always include seconds and take up to 5 other units
        // Extract seconds first
        const secondsUnit = allUnits.pop(); // This is the seconds unit

        // Take up to 5 other units
        units.push(...allUnits.slice(0, 5));

        // Always add seconds at the end
        if (secondsUnit) {
          units.push(secondsUnit);
        }

        setTimeUnits(units);
      }
    };

    // Initial update
    updateTime();

    // Set up interval
    const intervalId = setInterval(updateTime, 1000);

    // Cleanup
    return () => clearInterval(intervalId);
  }, [targetDate]);

  if (!targetDate || now >= targetDate) {
    return null; // Don't render if date is invalid or trial ended
  }

  // Determine if this is a short or long countdown
  const isLongCountdown = timeUnits.some(
    (unit) => unit.label === "months" || unit.label === "years"
  );

  // Determine color theme based on time remaining
  const getColorTheme = () => {
    if (isLongCountdown) {
      return {
        gradient:
          "from-blue-100 to-blue-50 dark:from-blue-900/40 dark:to-blue-900/20",
        border: "border-blue-200 dark:border-blue-800/50",
        text: "text-blue-600 dark:text-blue-400",
        shadow: "shadow-blue-200/20 dark:shadow-blue-900/20",
        glow: "after:bg-blue-500/10 dark:after:bg-blue-400/10",
      };
    }

    // Less than a day remaining
    if (!timeUnits.some((unit) => unit.label === "days")) {
      return {
        gradient:
          "from-amber-100 to-amber-50 dark:from-amber-900/40 dark:to-amber-900/20",
        border: "border-amber-200 dark:border-amber-800/50",
        text: "text-amber-600 dark:text-amber-400",
        shadow: "shadow-amber-200/20 dark:shadow-amber-900/20",
        glow: "after:bg-amber-500/10 dark:after:bg-amber-400/10",
      };
    }

    // Default (days remaining)
    return {
      gradient:
        "from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/5 dark:from-[var(--brand-gold)]/30 dark:to-[var(--brand-gold)]/10",
      border:
        "border-[var(--brand-gold)]/30 dark:border-[var(--brand-gold)]/20",
      text: "text-[var(--brand-gold)] dark:text-[var(--brand-gold)]",
      shadow:
        "shadow-[var(--brand-gold)]/10 dark:shadow-[var(--brand-gold)]/10",
      glow: "after:bg-[var(--brand-gold)]/10 dark:after:bg-[var(--brand-gold)]/10",
    };
  };

  const colorTheme = getColorTheme();

  return (
    <div className={`flex flex-col items-center w-full ${className || ""}`}>
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-1.5 mb-1.5">
              <div className="transition-transform hover:scale-110">
                <CalendarClock className={`w-4 h-4 ${colorTheme.text}`} />
              </div>
              <span className="text-xs font-medium text-muted-foreground">
                {label}
              </span>
            </div>
          </TooltipTrigger>
          <TooltipContent
            side="bottom"
            className="bg-neutral-800/95 dark:bg-neutral-950/95 backdrop-blur-sm border border-neutral-700/50 dark:border-[var(--brand-gold)]/20 text-white text-xs p-3 rounded-lg shadow-lg"
          >
            <p>{tooltipText}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <div className="flex gap-1 xs:gap-1.5 sm:gap-2 flex-wrap justify-center">
        {timeUnits.map((unit, index) => (
          <div key={index} className="flex flex-col items-center">
            <div
              className={`relative w-8 h-8 xs:w-9 xs:h-9 sm:w-10 sm:h-10 md:w-11 md:h-11 overflow-hidden rounded-md ${colorTheme.shadow} shadow-md after:absolute after:inset-0 after:rounded-md after:opacity-30 after:blur-xl ${colorTheme.glow}`}
            >
              {/* Top half static overlay (shadow effect) */}
              <div className="absolute top-0 left-0 right-0 h-[1px] bg-white/40 dark:bg-white/10 z-10"></div>

              {/* Bottom half static overlay (shadow effect) */}
              <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-black/10 dark:bg-black/20 z-10"></div>

              {/* Horizontal divider line */}
              <div className="absolute top-1/2 left-0 right-0 h-[1px] bg-black/10 dark:bg-white/10 z-10"></div>

              <div
                key={`${unit.label}-${unit.value}`}
                className={`absolute inset-0 flex items-center justify-center bg-gradient-to-b ${colorTheme.gradient} rounded-md border ${colorTheme.border}`}
              >
                <span className="text-xs xs:text-sm sm:text-base md:text-lg font-mono font-bold text-neutral-800 dark:text-neutral-200">
                  {unit.value.toString().padStart(2, "0")}
                </span>
              </div>
            </div>
            <span className="text-[7px] xs:text-[8px] sm:text-[9px] font-medium text-muted-foreground mt-0.5">
              {unit.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FlipTimer;
