"use server";

import { checkBusinessSlugAvailability } from "@/lib/utils/slugUtils";
import { generateSlug } from "../utils/slugGenerator";
import { validateSlugFormat } from "../validation/businessCardValidation";
import { nanoid, MAX_SLUG_ATTEMPTS } from "../utils/constants";

/**
 * Generates a unique slug for a business
 * @param businessName - The business name to generate slug from
 * @param currentSlug - Current slug if any
 * @param userId - User ID for availability checking
 * @returns Object with success status and final slug or error
 */
export async function generateUniqueSlug(
  businessName: string,
  currentSlug: string,
  userId: string
): Promise<{ success: boolean; slug?: string; error?: string }> {
  const desiredSlug = currentSlug || generateSlug(businessName);

  let isUnique = false;
  let checkSlug = desiredSlug;
  let attempts = 0;

  while (!isUnique && attempts < MAX_SLUG_ATTEMPTS) {
    // Use the shared slug availability check
    const { available, error: slugCheckError } = await checkBusinessSlugAvailability(checkSlug, userId);

    if (slugCheckError) {
      console.error("Slug Check Error:", slugCheckError);
      return { success: false, error: "Error checking slug availability." };
    }

    if (available) {
      isUnique = true;
      const finalSlug = checkSlug;
      
      // Validate the final slug format
      const slugValidation = validateSlugFormat(finalSlug);
      if (!slugValidation.success) {
        return {
          success: false,
          error: "Invalid business slug format generated. Please set one manually.",
        };
      }
      
      return { success: true, slug: finalSlug };
    } else {
      attempts++;
      checkSlug = `${desiredSlug}-${nanoid()}`;
      if (attempts === MAX_SLUG_ATTEMPTS) {
        return {
          success: false,
          error: `Could not generate a unique slug for '${desiredSlug}'. Please try setting one manually.`,
        };
      }
    }
  }

  return { success: false, error: "Failed to generate unique slug." };
}

/**
 * Action to check slug availability (wrapper for shared utility)
 * @param slug - The slug to check
 * @returns Object with availability status
 */
export async function checkSlugAvailability(
  slug: string
): Promise<{ available: boolean; error?: string }> {
  return checkBusinessSlugAvailability(slug);
}
