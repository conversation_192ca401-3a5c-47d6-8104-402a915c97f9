import { DashboardLayout } from '@/components/shared/layout/DashboardLayout';
import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Alert
} from 'react-native';
import { supabase } from '@/lib/supabase';
import { SubscriptionCard } from '@/components/social/SubscriptionCard';
import { SearchComponent } from '@/components/social/SearchComponent';
import { EmptyState } from '@/components/shared/ui/EmptyState';
import { LoadingSpinner } from '@/components/shared/ui/LoadingSpinner';
import { SubscriptionListSkeleton } from '@/components/social/SkeletonLoaders';
import { subscriptionsService } from '@/lib/services/socialService';

interface BusinessProfileData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface SubscriptionWithProfile {
  id: string;
  business_profiles: BusinessProfileData | null;
}

export default function CustomerSubscriptionsScreen() {
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [user, setUser] = useState<any>(null);

  // Get current user
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error || !user) {
        Alert.alert('Error', 'Please log in to view your subscriptions');
        return;
      }
      setUser(user);
    };
    getCurrentUser();
  }, []);

  // Fetch subscriptions
  const fetchSubscriptions = useCallback(async (page: number = 1, search: string = '', isRefresh: boolean = false) => {
    if (!user) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const result = await subscriptionsService.fetchSubscriptions(user.id, page, 10, search);

      if (page === 1 || isRefresh) {
        setSubscriptions(result.items);
      } else {
        setSubscriptions(prev => [...prev, ...result.items]);
      }

      setTotalCount(result.totalCount);
      setHasMore(result.hasMore);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      Alert.alert('Error', 'Failed to load subscriptions. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  }, [user]);

  // Initial load
  useEffect(() => {
    if (user) {
      fetchSubscriptions(1, searchTerm);
    }
  }, [user, fetchSubscriptions, searchTerm]);

  // Handle search
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
    fetchSubscriptions(1, term);
  }, [fetchSubscriptions]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setCurrentPage(1);
    fetchSubscriptions(1, searchTerm, true);
  }, [fetchSubscriptions, searchTerm]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchSubscriptions(currentPage + 1, searchTerm);
    }
  }, [loadingMore, hasMore, currentPage, searchTerm, fetchSubscriptions]);

  // Handle unsubscribe
  const handleUnsubscribe = useCallback(async (subscriptionId: string) => {
    try {
      await subscriptionsService.unsubscribe(subscriptionId);
      setSubscriptions(prev => prev.filter(sub => sub.id !== subscriptionId));
      setTotalCount(prev => prev - 1);
      Alert.alert('Success', 'Successfully unsubscribed from business');
    } catch (error) {
      console.error('Error unsubscribing:', error);
      Alert.alert('Error', 'Failed to unsubscribe. Please try again.');
    }
  }, []);

  // Render subscription item
  const renderSubscription = ({ item }: { item: SubscriptionWithProfile }) => (
    <SubscriptionCard
      subscription={item}
      onUnsubscribe={handleUnsubscribe}
    />
  );

  // Render footer
  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#D4AF37" />
      </View>
    );
  };

  if (loading) {
    return (
      <DashboardLayout
        userName={user?.user_metadata?.name || "User"}
        showNotifications={true}
      >
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>🔔 Subscriptions</Text>
            <Text style={styles.subtitle}>Loading your subscriptions...</Text>
          </View>
          <View style={styles.listContainer}>
            <SubscriptionListSkeleton count={5} />
          </View>
        </View>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      userName={user?.user_metadata?.name || "User"}
      showNotifications={true}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>🔔 Subscriptions</Text>
          <Text style={styles.subtitle}>
            {totalCount} {totalCount === 1 ? 'business' : 'businesses'} you&apos;re following
          </Text>
        </View>

        <SearchComponent
          value={searchTerm}
          onChangeText={handleSearch}
          placeholder="Search businesses..."
        />

        {subscriptions.length === 0 ? (
          <EmptyState
            title="No subscriptions yet"
            description="You haven&apos;t subscribed to any businesses yet. Discover businesses and follow them to see their updates."
            actionText="Discover Businesses"
            onAction={() => {/* Navigate to discover */}}
          />
        ) : (
          <FlatList
            data={subscriptions}
            renderItem={renderSubscription}
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#D4AF37']}
                tintColor="#D4AF37"
              />
            }
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.1}
            ListFooterComponent={renderFooter}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>
    </DashboardLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});
