"use client";

import React from "react";
import MinimalHeader from "@/app/components/MinimalHeader";
import { ThemeToggle } from "@/app/components/ThemeToggle";
import BottomNav from "@/app/components/BottomNav"; // Import BottomNav
// Removed Button import as it's no longer directly used here
import {
  SidebarProvider,
  SidebarInset,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { CustomerAppSidebar } from "@/components/sidebar/CustomerAppSidebar";
import { cn } from "@/lib/utils";
import { UserDataProvider } from "@/contexts/UserDataContext";

interface CustomerDashboardClientLayoutProps {
  children: React.ReactNode;
  initialUserName: string | null;
  // Removed initialUserEmail as it's not used in this layout
  initialUserAvatarUrl: string | null;
}

export default function CustomerDashboardClientLayout({
  children,
  initialUserName,
  // Removed initialUserEmail from destructuring
  initialUserAvatarUrl,
}: CustomerDashboardClientLayoutProps) {

  // Pass props directly to components

  return (
    <UserDataProvider>
      <SidebarProvider>
      <CustomerAppSidebar
        userName={initialUserName}
        userAvatarUrl={initialUserAvatarUrl}
      />
      <SidebarInset>
        {/* Header is now inside SidebarInset */}
        <MinimalHeader
          userName={initialUserName} // Pass initial prop directly
          businessName={null} // No business context for customer
          logoUrl={initialUserAvatarUrl} // Pass initial prop directly
        >
          {/* Sidebar Trigger replaces old buttons */}
          <SidebarTrigger className="ml-auto md:ml-0" /> {/* Adjust margin as needed */}

          {/* Theme Toggle remains in header */}
          <ThemeToggle variant="dashboard" />
        </MinimalHeader>

        {/* Main Content Area */}
        <main
          className={cn(
            "flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto pb-16 md:pb-6", // Standardized padding across all screen sizes
            "bg-white dark:bg-black" // Use white for light mode and black for dark mode
          )}
        >
          {children}
        </main>
        <BottomNav />
      </SidebarInset>
      </SidebarProvider>
    </UserDataProvider>
  );
}
