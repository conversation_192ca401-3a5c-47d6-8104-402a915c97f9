/**
 * Gallery Plan Limits Utility for React Native
 * Matches the plan limits from Next.js implementation
 */

export type PlanType = 'free' | 'basic' | 'growth' | 'pro' | 'enterprise' | 'trial';

/**
 * Get the gallery image limit for a given plan
 * @param planType The plan type
 * @returns The gallery image limit
 */
export function getGalleryLimit(planType: PlanType | string | null | undefined): number {
  if (!planType) return 1; // Default to free plan limit

  switch (planType) {
    case 'free':
      return 1;
    case 'basic':
      return 3;
    case 'growth':
      return 10;
    case 'pro':
      return 50;
    case 'enterprise':
      return 100;
    case 'trial':
      return 3; // Trial users get the same as basic plan
    default:
      return 1; // Default to free plan limit
  }
}

/**
 * Check if a user can add more gallery images based on their plan and current count
 * @param planType The plan type
 * @param currentCount The current number of gallery images
 * @returns Whether the user can add more gallery images
 */
export function canAddMoreGalleryImages(
  planType: PlanType | string | null | undefined,
  currentCount: number
): boolean {
  const limit = getGalleryLimit(planType);
  return currentCount < limit;
}

/**
 * Apply plan limits to gallery images array
 * @param images Array of gallery images
 * @param planType The plan type
 * @returns Limited array of gallery images
 */
export function applyGalleryPlanLimits<T>(
  images: T[],
  planType: PlanType | string | null | undefined
): T[] {
  const limit = getGalleryLimit(planType);
  return images.slice(0, limit);
}

/**
 * Get plan display name
 * @param planType The plan type
 * @returns Human-readable plan name
 */
export function getPlanDisplayName(planType: PlanType | string | null | undefined): string {
  if (!planType) return 'Free';

  switch (planType) {
    case 'free':
      return 'Free';
    case 'basic':
      return 'Basic';
    case 'growth':
      return 'Growth';
    case 'pro':
      return 'Pro';
    case 'enterprise':
      return 'Enterprise';
    case 'trial':
      return 'Trial';
    default:
      return 'Free';
  }
}

/**
 * Get plan limit description for gallery
 * @param planType The plan type
 * @returns Description of gallery limits for the plan
 */
export function getGalleryLimitDescription(planType: PlanType | string | null | undefined): string {
  const limit = getGalleryLimit(planType);
  const planName = getPlanDisplayName(planType);
  
  if (limit === 1) {
    return `${planName} plan allows ${limit} photo.`;
  } else {
    return `${planName} plan allows ${limit} photos.`;
  }
}
