"use client";

import { LucideIcon } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

interface CustomerAnimatedMetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description: string;
  color: "blue" | "indigo" | "purple" | "rose" | "red" | "yellow" | "brand";
  href?: string;
}

export default function CustomerAnimatedMetricCard({
  title,
  value,
  icon: Icon,
  description,
  color,
  href,
}: CustomerAnimatedMetricCardProps) {
  // Define color variants with enhanced glow effects
  const colorVariants = {
    blue: {
      bgLight: "bg-blue-100",
      bgDark: "dark:bg-blue-900/30",
      textLight: "text-blue-600",
      textDark: "dark:text-blue-400",
      borderHover: "group-hover:border-blue-300 dark:group-hover:border-blue-700",
      glowColor: "rgba(59, 130, 246, 0.4)",
      borderColor: "border-blue-200/50 dark:border-blue-700/50",
      innerGlow: "bg-blue-500/5 dark:bg-blue-500/10",
      circleColor: "bg-blue-500/30",
      circleGlow: "shadow-blue-500/60",
      shadowGlow: "shadow-blue-500/40",
    },
    indigo: {
      bgLight: "bg-indigo-100",
      bgDark: "dark:bg-indigo-900/30",
      textLight: "text-indigo-600",
      textDark: "dark:text-indigo-400",
      borderHover: "group-hover:border-indigo-300 dark:group-hover:border-indigo-700",
      glowColor: "rgba(99, 102, 241, 0.4)",
      borderColor: "border-indigo-200/50 dark:border-indigo-700/50",
      innerGlow: "bg-indigo-500/5 dark:bg-indigo-500/10",
      circleColor: "bg-indigo-500/30",
      circleGlow: "shadow-indigo-500/60",
      shadowGlow: "shadow-indigo-500/40",
    },
    purple: {
      bgLight: "bg-purple-100",
      bgDark: "dark:bg-purple-900/30",
      textLight: "text-purple-600",
      textDark: "dark:text-purple-400",
      borderHover: "group-hover:border-purple-300 dark:group-hover:border-purple-700",
      glowColor: "rgba(168, 85, 247, 0.4)",
      borderColor: "border-purple-200/50 dark:border-purple-700/50",
      innerGlow: "bg-purple-500/5 dark:bg-purple-500/10",
      circleColor: "bg-purple-500/30",
      circleGlow: "shadow-purple-500/60",
      shadowGlow: "shadow-purple-500/40",
    },
    rose: {
      bgLight: "bg-rose-100",
      bgDark: "dark:bg-rose-900/30",
      textLight: "text-rose-600",
      textDark: "dark:text-rose-400",
      borderHover: "group-hover:border-rose-300 dark:group-hover:border-rose-700",
      glowColor: "rgba(244, 63, 94, 0.4)",
      borderColor: "border-rose-200/50 dark:border-rose-700/50",
      innerGlow: "bg-rose-500/5 dark:bg-rose-500/10",
      circleColor: "bg-rose-500/30",
      circleGlow: "shadow-rose-500/60",
      shadowGlow: "shadow-rose-500/40",
    },
    red: {
      bgLight: "bg-red-100",
      bgDark: "dark:bg-red-900/30",
      textLight: "text-red-500",
      textDark: "dark:text-red-400",
      borderHover: "group-hover:border-red-300 dark:group-hover:border-red-700",
      glowColor: "rgba(239, 68, 68, 0.4)",
      borderColor: "border-red-200/50 dark:border-red-700/50",
      innerGlow: "bg-red-500/5 dark:bg-red-500/10",
      circleColor: "bg-red-500/30",
      circleGlow: "shadow-red-500/60",
      shadowGlow: "shadow-red-500/40",
    },
    yellow: {
      bgLight: "bg-yellow-100",
      bgDark: "dark:bg-yellow-900/30",
      textLight: "text-yellow-500",
      textDark: "dark:text-yellow-400",
      borderHover: "group-hover:border-yellow-300 dark:group-hover:border-yellow-700",
      glowColor: "rgba(234, 179, 8, 0.4)",
      borderColor: "border-yellow-200/50 dark:border-yellow-700/50",
      innerGlow: "bg-yellow-500/5 dark:bg-yellow-500/10",
      circleColor: "bg-yellow-500/30",
      circleGlow: "shadow-yellow-500/60",
      shadowGlow: "shadow-yellow-500/40",
    },
    brand: {
      bgLight: "bg-amber-100",
      bgDark: "dark:bg-amber-900/30",
      textLight: "text-amber-600",
      textDark: "dark:text-amber-400",
      borderHover: "group-hover:border-amber-300 dark:group-hover:border-amber-700",
      glowColor: "rgba(194, 157, 91, 0.4)", // --brand-gold-rgb values
      borderColor: "border-amber-200/50 dark:border-amber-700/50",
      innerGlow: "bg-amber-500/5 dark:bg-amber-500/10",
      circleColor: "bg-amber-500/30",
      circleGlow: "shadow-amber-500/60",
      shadowGlow: "shadow-amber-500/40",
    },
  };

  const selectedColor = colorVariants[color];

  const cardContent = (
    <>
      {/* Strong inner glow effect that fills the card */}
      <div className={`absolute inset-0 ${selectedColor.innerGlow} opacity-80 group-hover:opacity-100 transition-opacity duration-300`} />

      {/* Content */}
      <div className="relative z-10">
        {/* Icon */}
        <div className="flex items-center justify-center mb-3">
          <div className={`p-2 rounded-lg bg-white/50 dark:bg-neutral-800/50 ${selectedColor.shadowGlow} group-hover:shadow-md transition-all duration-300`}>
            <Icon className={`w-5 h-5 ${selectedColor.textLight} ${selectedColor.textDark}`} />
          </div>
        </div>

        {/* Value */}
        <div className="text-center">
          <div className="text-xl font-bold text-neutral-900 dark:text-neutral-100 mb-1 group-hover:scale-110 transition-transform duration-300">
            {value}
          </div>
          <div className="text-xs font-medium text-neutral-600 dark:text-neutral-400">
            {title}
          </div>
        </div>

        {/* Description */}
        <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-2 text-center">
          {description}
        </p>

        {/* Interactive Button - only show for cards with href */}
        {href && (
          <div className="mt-3 relative z-20">
            <div className="relative group inline-flex w-full">
              {/* Glow effects for the button */}
              <div
                className="absolute inset-0 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none"
                style={{
                  backgroundColor: color === 'brand'
                    ? 'rgba(194, 157, 91, 0.05)'
                    : selectedColor.glowColor.replace('0.4', '0.05')
                }}
              />

              {/* Border glow effect */}
              <div
                className="absolute inset-0 rounded-lg pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300"
                style={{
                  boxShadow: color === 'brand'
                    ? `inset 0 0 20px rgba(194, 157, 91, 0.2), 0 0 20px rgba(194, 157, 91, 0.3)`
                    : `inset 0 0 20px ${selectedColor.glowColor.replace('0.4', '0.2')}, 0 0 20px ${selectedColor.glowColor.replace('0.4', '0.3')}`
                }}
              />

              {/* Decorative glow circles */}
              <div
                className="absolute -top-1 -right-1 w-4 h-4 rounded-full opacity-80 group-hover:opacity-100 transition-all duration-300 blur-sm pointer-events-none"
                style={{
                  backgroundColor: color === 'brand'
                    ? 'rgba(194, 157, 91, 0.3)'
                    : selectedColor.glowColor.replace('0.4', '0.3'),
                  boxShadow: color === 'brand'
                    ? '0 0 15px rgba(194, 157, 91, 0.6)'
                    : `0 0 15px ${selectedColor.glowColor.replace('0.4', '0.6')}`
                }}
              />
              <div
                className="absolute -bottom-1 -left-1 w-3 h-3 rounded-full opacity-60 group-hover:opacity-90 transition-all duration-300 blur-sm pointer-events-none"
                style={{
                  backgroundColor: color === 'brand'
                    ? 'rgba(194, 157, 91, 0.3)'
                    : selectedColor.glowColor.replace('0.4', '0.3'),
                  boxShadow: color === 'brand'
                    ? '0 0 10px rgba(194, 157, 91, 0.6)'
                    : `0 0 10px ${selectedColor.glowColor.replace('0.4', '0.6')}`
                }}
              />

              <Button
                asChild
                variant="outline"
                size="sm"
                className={`
                  w-full text-xs font-medium relative overflow-hidden
                  ${selectedColor.textLight} ${selectedColor.textDark}
                  bg-current/5 border-current/10 hover:bg-current/15 hover:border-current/30
                `}
              >
                <Link href={href} className="relative group">
                  View {title}

                  {/* Shimmer effect - only on hover */}
                  <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100 group-hover:animate-[shimmer_0.6s_ease-in-out] transition-opacity duration-300" />
                </Link>
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Strong decorative colored glow elements */}
      <div className={`absolute -top-2 -right-2 w-10 h-10 ${selectedColor.circleColor} rounded-full ${selectedColor.circleGlow} opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md`} />
      <div className={`absolute -bottom-2 -left-2 w-8 h-8 ${selectedColor.circleColor} rounded-full ${selectedColor.circleGlow} opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md`} />
    </>
  );

  return (
    <div className={`
      group relative overflow-hidden rounded-xl p-4
      bg-white dark:bg-black
      border ${selectedColor.borderColor}
      ${selectedColor.shadowGlow} shadow-lg
      hover:shadow-xl hover:${selectedColor.shadowGlow}
      transition-all duration-300
      cursor-default
    `}>
      {cardContent}
    </div>
  );
}
