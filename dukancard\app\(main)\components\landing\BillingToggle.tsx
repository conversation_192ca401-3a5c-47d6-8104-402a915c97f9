"use client";

import { Button } from "@/components/ui/button";

interface BillingToggleProps {
  billingCycle: "monthly" | "yearly";
  setBillingCycle: (_cycle: "monthly" | "yearly") => void;
}

export default function BillingToggle({
  billingCycle,
  setBillingCycle,
}: BillingToggleProps) {
  return (
    <div className="border border-[var(--brand-gold)] rounded-full relative inline-flex">
      {/* Static background */}
      <div className="absolute -inset-2 bg-gradient-to-r from-blue-500/20 via-[var(--brand-gold)]/30 to-blue-500/20 rounded-full blur-lg opacity-60" />

      <div className="space-x-2 bg-white/80 dark:bg-black/50 backdrop-blur-sm p-1.5 rounded-full border border-neutral-200/50 dark:border-neutral-800/50 inline-flex shadow-md relative z-10">
        <Button
          onClick={() => setBillingCycle("monthly")}
          variant={billingCycle === "monthly" ? "default" : "ghost"}
          size="sm"
          className={`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
            billingCycle === "monthly"
              ? "bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md"
              : "text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"
          }`}
        >
          <span>Monthly</span>
        </Button>
        <Button
          onClick={() => setBillingCycle("yearly")}
          variant={billingCycle === "yearly" ? "default" : "ghost"}
          size="sm"
          className={`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 flex items-center gap-2 ${
            billingCycle === "yearly"
              ? "bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md"
              : "text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"
          }`}
        >
          <span>Yearly</span>
          <span className="bg-gradient-to-r from-green-600 to-green-500 text-white text-xs px-2 py-0.5 rounded-full shadow-sm">
            Save 20%
          </span>
        </Button>
      </div>
    </div>
  );
}
