import { MetadataRoute } from "next";
import { createSitemapClient } from "@/utils/supabase/sitemap";

// Force dynamic rendering to prevent build-time generation
export const dynamic = 'force-dynamic';

// Use ISR with a long revalidation period (24 hours = 86400 seconds)
export const revalidate = 86400;

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";

  // Always include the base blog URL to ensure the sitemap is never empty
  const baseEntries: MetadataRoute.Sitemap = [
    {
      url: `${siteUrl}/blog`,
      lastModified: new Date(),
      changeFrequency: "daily",
      priority: 0.8,
    }
  ];

  try {
    // Fetch published blogs using the sitemap client
    let blogEntries: MetadataRoute.Sitemap = [];

    try {
      const supabaseClient = createSitemapClient();
      
      const { data: blogs, error } = await supabaseClient
        .from("blogs")
        .select("slug, updated_at, published_at")
        .eq("status", "published")
        .order("published_at", { ascending: false });

      if (error) {
        console.error("Error fetching blogs for sitemap:", error);
        return baseEntries; // Return base entries if fetching fails
      }

      if (!blogs || blogs.length === 0) {
        return baseEntries;
      }

      // Define type for blog based on select query
      type BlogData = {
        slug: string;
        updated_at: string | null;
        published_at: string | null;
      };

      // Filter and map to sitemap entries
      blogEntries = blogs
        .filter((blog: BlogData) => {
          const isValid = blog.slug && blog.slug.trim() !== "";
          return isValid;
        })
        .map((blog: BlogData) => ({
          url: `${siteUrl}/blog/${blog.slug}`,
          lastModified: blog.updated_at ? new Date(blog.updated_at) : new Date(),
          changeFrequency: "weekly" as const,
          priority: 0.7, // High priority for blog posts
        }));

      const finalSitemap = [...baseEntries, ...blogEntries];
      return finalSitemap;

    } catch (error) {
      console.error("Error in blog sitemap generation:", error);
      return baseEntries; // Return base entries if any error occurs
    }
  } catch (error) {
    console.error("Error creating sitemap client:", error);
    return baseEntries; // Return base entries if client creation fails
  }
}
