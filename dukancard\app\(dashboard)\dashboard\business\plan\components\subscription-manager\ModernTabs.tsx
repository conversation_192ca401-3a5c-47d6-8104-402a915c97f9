"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";

interface TabItem {
  id: string;
  label: React.ReactNode;
  icon?: React.ReactNode;
}

interface ModernTabsProps {
  tabs: TabItem[];
  defaultTab?: string;
  value?: string;
  onChange?: (_value: string) => void;
  className?: string;
  tabClassName?: string;
  activeTabClassName?: string;
  inactiveTabClassName?: string;
  indicatorClassName?: string;
  indicatorLayoutId?: string;
  children?: React.ReactNode;
}

export const ModernTabs = ({
  tabs,
  defaultTab,
  value,
  onChange,
  className,
  tabClassName,
  activeTabClassName,
  inactiveTabClassName,
  indicatorClassName,
  indicatorLayoutId = "activeTab",
  children,
}: ModernTabsProps) => {
  const [activeTab, setActiveTab] = useState(value || defaultTab || tabs[0]?.id);
  const [hoveredTab, setHoveredTab] = useState<string | null>(null);

  // Update active tab when value prop changes
  useEffect(() => {
    if (value && value !== activeTab) {
      setActiveTab(value);
    }
  }, [value, activeTab]);

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    onChange?.(tabId);
  };

  return (
    <div className={cn("w-full space-y-4", className)}>
      {/* Tab List */}
      <div className="relative flex items-center justify-center p-1 rounded-xl bg-gradient-to-br from-white/80 to-white/40 dark:from-black/40 dark:to-black/20 backdrop-blur-sm border border-neutral-200/80 dark:border-neutral-800/80 shadow-sm">
        <div className="absolute inset-0 overflow-hidden rounded-xl">
          {/* Animated background particles */}
          {Array.from({ length: 5 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 rounded-full bg-primary/30"
              initial={{
                x: `${Math.random() * 100}%`,
                y: `${Math.random() * 100}%`,
                opacity: 0.2
              }}
              animate={{
                y: [`${Math.random() * 100}%`, `${Math.random() * 100}%`],
                opacity: [0.1, 0.3, 0.1]
              }}
              transition={{
                repeat: Infinity,
                duration: Math.random() * 5 + 5,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>

        {/* Tab buttons */}
        <div className="relative z-10 flex w-full rounded-lg p-1">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            const isHovered = hoveredTab === tab.id;

            return (
              <motion.button
                key={tab.id}
                className={cn(
                  "relative flex-1 flex items-center justify-center gap-2 py-2.5 px-3 text-sm font-medium rounded-lg transition-all duration-200 z-10",
                  tabClassName,
                  isActive
                    ? cn("text-primary-foreground", activeTabClassName)
                    : cn(
                        "text-muted-foreground hover:text-foreground",
                        inactiveTabClassName
                      )
                )}
                onClick={() => handleTabChange(tab.id)}
                onMouseEnter={() => setHoveredTab(tab.id)}
                onMouseLeave={() => setHoveredTab(null)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {/* Tab content */}
                <div className="relative z-10 flex items-center gap-2">
                  {tab.icon && (
                    <motion.div
                      animate={{
                        rotate: isHovered ? [0, -5, 5, 0] : 0,
                        scale: isHovered ? [1, 1.1, 1] : 1
                      }}
                      transition={{ duration: 0.5 }}
                    >
                      {tab.icon}
                    </motion.div>
                  )}
                  <span>{tab.label}</span>
                </div>

                {/* Active tab indicator (visible only for active tab) */}
                {isActive && (
                  <motion.div
                    className={cn(
                      "absolute inset-0 rounded-lg bg-primary shadow-md",
                      indicatorClassName
                    )}
                    layoutId={indicatorLayoutId}
                    transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  />
                )}
              </motion.button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className="relative">
        <AnimatePresence mode="wait">
          {React.Children.map(children, (child) => {
            if (!React.isValidElement(child)) return null;

            // Check if this child should be visible based on the active tab
            const childProps = child.props as { value: string };
            const isVisible = childProps.value === activeTab;

            if (!isVisible) return null;

            return (
              <motion.div
                key={childProps.value}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
                className="w-full"
              >
                {child}
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>
    </div>
  );
};

export const ModernTabsContent = ({
  value,
  children,
  className,
}: {
  value: string;
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("w-full", className)} data-value={value}>
      {children}
    </div>
  );
};
