import { Suspense } from "react";
import type { <PERSON>ada<PERSON> } from "next";
import AuthCallbackWrapper from "./AuthCallbackWrapper";

export const dynamicParams = true;

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Authenticating",
    robots: "noindex, nofollow",
  };
}

export default function AuthCallbackPage() {
  // Wrap AuthCallbackWrapper with Suspense as it likely uses useSearchParams
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AuthCallbackWrapper />
    </Suspense>
  );
}
