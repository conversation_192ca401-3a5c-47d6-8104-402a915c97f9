import { customAlphabet } from "nanoid";

// Nanoid generator for unique slugs
export const nanoid = customAlphabet("1234567890abcdef", 6);

// Maximum attempts for slug generation
export const MAX_SLUG_ATTEMPTS = 5;

// File upload constants
export const LOGO_MAX_SIZE_MB = 15;
export const ALLOWED_IMAGE_TYPES = ["image/png", "image/jpeg", "image/gif", "image/webp"];

// Logo compression is now handled client-side

// Storage bucket name
export const STORAGE_BUCKET = "business";
