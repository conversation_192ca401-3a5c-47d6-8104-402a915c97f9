"use client";

import React from "react";
import ConfirmationDialog from "./ConfirmationDialog";

interface WebhookWaitingIndicatorProps {
  isVisible: boolean;
  message?: string;
  description?: string;
}

/**
 * A wrapper around ConfirmationDialog for backward compatibility
 * This component is being used in the subscription manager to show webhook waiting state
 */
export default function WebhookWaitingIndicator({
  isVisible,
  message = "Waiting for confirmation from Ra<PERSON><PERSON><PERSON>",
  description = "Please wait while we receive webhook confirmation. This may take a moment.",
}: WebhookWaitingIndicatorProps) {
  return (
    <ConfirmationDialog
      isOpen={isVisible}
      message={message}
      description={description}
    />
  );
}
