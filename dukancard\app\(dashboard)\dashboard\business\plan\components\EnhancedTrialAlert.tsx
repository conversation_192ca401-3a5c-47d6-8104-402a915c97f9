"use client";

import { Clock, AlertTriangle } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import FlipTimer from "../../../business/components/FlipTimer";

interface EnhancedTrialAlertProps {
  trialEndDate: string;
}

export default function EnhancedTrialAlert({
  trialEndDate,
}: EnhancedTrialAlertProps) {
  const trialEnd = new Date(trialEndDate);
  const timeRemaining = formatDistanceToNow(trialEnd, { addSuffix: true });
  const isTrialEndingSoon = trialEnd.getTime() - Date.now() < 3 * 24 * 60 * 60 * 1000; // 3 days

  return (
    <div className="w-full opacity-100 transition-all duration-500" style={{ transform: 'translateY(0)' }}>
      <div
        className={`w-full rounded-lg border ${
          isTrialEndingSoon
            ? "border-amber-200 dark:border-amber-800/50 bg-gradient-to-r from-amber-50 to-amber-100/70 dark:from-amber-950/40 dark:to-amber-900/30"
            : "border-blue-200 dark:border-blue-800/50 bg-gradient-to-r from-blue-50 to-blue-100/70 dark:from-blue-950/40 dark:to-blue-900/30"
        } backdrop-blur-sm shadow-sm relative overflow-hidden`}
      >
        {/* Animated background glow */}
        <div className="absolute inset-0 pointer-events-none">
          <div
            className={`absolute inset-0 ${
              isTrialEndingSoon
                ? "bg-amber-400/5 dark:bg-amber-400/10"
                : "bg-blue-400/5 dark:bg-blue-400/10"
            } rounded-full blur-3xl`}
          />
        </div>

        <div className="relative z-10 p-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-start gap-3">
              {isTrialEndingSoon ? (
                <AlertTriangle className="h-5 w-5 flex-shrink-0 text-amber-600 dark:text-amber-500 mt-0.5" />
              ) : (
                <Clock className="h-5 w-5 flex-shrink-0 text-blue-600 dark:text-blue-500 mt-0.5" />
              )}
              <div>
                <h3 className={`text-base font-semibold ${
                  isTrialEndingSoon
                    ? "text-amber-800 dark:text-amber-400"
                    : "text-blue-800 dark:text-blue-400"
                }`}>
                  {isTrialEndingSoon
                    ? "Your trial is ending soon!"
                    : "You're on a free trial"}
                </h3>
                <p className={`text-sm mt-1 ${
                  isTrialEndingSoon
                    ? "text-amber-700 dark:text-amber-500"
                    : "text-blue-700 dark:text-blue-500"
                }`}>
                  Your trial period ends {timeRemaining}. Choose a plan below to continue using premium features.
                </p>
              </div>
            </div>

            <div className="flex-shrink-0">
              <div className={`rounded-md p-3 ${
                isTrialEndingSoon
                  ? "bg-amber-100/50 dark:bg-amber-900/30 border border-amber-200/50 dark:border-amber-800/30"
                  : "bg-blue-100/50 dark:bg-blue-900/30 border border-blue-200/50 dark:border-blue-800/30"
              }`}>
                <FlipTimer
                  endDate={trialEndDate}
                  label="Trial ends in:"
                  tooltipText="Your trial will end on this date. Choose a plan to continue using premium features."
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
