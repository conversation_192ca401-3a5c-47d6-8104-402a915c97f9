import { getTextureById, DEFAULT_TEXTURE_ID } from "./CardTextures";
import { useMemo } from "react";

interface CardBackgroundEffectsProps {
  finalThemeColor: string;
}

export default function CardBackgroundEffects({ finalThemeColor: _finalThemeColor }: CardBackgroundEffectsProps) {
  // Get texture details based on default texture ID
  const textureDetails = useMemo(() => getTextureById(DEFAULT_TEXTURE_ID), []);

  return (
    <>
      {/* Card background with subtle pattern */}
      <div
        className="absolute inset-0 pointer-events-none z-5"
        style={{
          backgroundImage: `url("/decorative/card-texture.svg")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          opacity: 0.6,
        }}
      ></div>

      {/* Custom texture overlay with subtle theme compatibility */}
      {textureDetails.path && (
        <div
          className="absolute inset-0 mix-blend-overlay pointer-events-none texture-background z-10 dark:opacity-[var(--dark-opacity)] opacity-[var(--light-opacity)]"
          style={
            {
              backgroundImage: textureDetails.path
                ? `url(${textureDetails.path})`
                : "none",
              "--dark-opacity": `${textureDetails.darkModeOpacity * 0.7}`,
              "--light-opacity": `${textureDetails.lightModeOpacity * 0.7}`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "repeat",
            } as React.CSSProperties
          }
        ></div>
      )}

      {/* Subtle pattern overlay */}
      <div
        className="absolute inset-0 pointer-events-none z-10"
        style={{
          backgroundImage: `url("/decorative/subtle-pattern.svg")`,
          backgroundRepeat: "repeat",
          backgroundSize: "20px 20px",
          opacity: 0.15,
        }}
      ></div>

      {/* Subtle embossed effect overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-black/20 mix-blend-overlay pointer-events-none z-15 opacity-20 dark:opacity-15"></div>
    </>
  );
}
