"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowRight, Share2 } from "lucide-react";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import EnhancedCardActions from "@/app/components/shared/EnhancedCardActions";

interface HeroActionButtonsProps {
  isAuthenticated: boolean;
  userType: "business" | "customer" | null;
  businessCardData?: BusinessCardData;
}

export default function HeroActionButtons({
  isAuthenticated,
  userType,
  businessCardData
}: HeroActionButtonsProps) {
  const [isClient, setIsClient] = useState(false);

  // Set isClient to true after component mounts
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Share handler for business users
  const handleShare = () => {
    if (!businessCardData?.business_slug) {
      toast.error("Business slug not available.");
      return;
    }

    const shareUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in'}/${businessCardData.business_slug}`;
    const shareText = `Check out my digital business card: ${businessCardData.business_name || "My Business"} on Dukancard!`;

    // Use Web Share API if available
    if (navigator.share) {
      navigator.share({
        title: `${businessCardData.business_name} - Digital Business Card`,
        text: shareText,
        url: shareUrl,
      }).catch((error) => {
        console.error('Error sharing:', error);
        // Fallback to clipboard
        copyToClipboard(shareUrl);
      });
    } else {
      // Fallback to clipboard
      copyToClipboard(shareUrl);
    }
  };

  // Copy to clipboard helper
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => toast.success("Link copied to clipboard!"))
      .catch(() => toast.error("Failed to copy link."));
  };

  // Show different buttons based on user type
  if (isAuthenticated && userType === "business" && businessCardData) {
    // Format address for the download QR functionality
    const formatAddress = (data: BusinessCardData) => {
      const addressParts = [
        data.address_line,
        data.locality,
        data.city,
        data.state,
        data.pincode,
      ].filter(Boolean);
      return addressParts.join(", ") || "Address not available";
    };

    return (
      <div className="w-full max-w-sm mx-auto space-y-5">
        {/* Download QR Button - using the same component as public cards */}
        <EnhancedCardActions
          businessSlug={businessCardData.business_slug || ""}
          businessName={businessCardData.business_name || ""}
          ownerName={businessCardData.member_name || ""}
          businessAddress={formatAddress(businessCardData)}
          themeColor={businessCardData.theme_color || "#F59E0B"}
        />

        {/* Share Button - matching the style of download button */}
        <div className="w-full relative group">
          {/* Button glow effect with properly rounded corners */}
          {isClient && (
            <motion.div
              className="absolute -inset-0.5 rounded-full blur-md"
              style={{
                background: "linear-gradient(to right, rgba(34, 197, 94, 0.6), rgba(34, 197, 94, 0.8))"
              }}
              initial={{ opacity: 0.7 }}
              animate={{
                opacity: [0.7, 0.9, 0.7],
                boxShadow: [
                  "0 0 15px 2px rgba(34, 197, 94, 0.3)",
                  "0 0 20px 4px rgba(34, 197, 94, 0.5)",
                  "0 0 15px 2px rgba(34, 197, 94, 0.3)"
                ]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "loop",
                ease: "easeInOut"
              }}
            />
          )}

          <Button
            onClick={handleShare}
            disabled={!businessCardData.business_slug}
            className={cn(
              "w-full py-6 relative overflow-hidden group",
              "bg-green-600 hover:bg-green-700",
              "text-white font-medium text-base",
              "border-none rounded-full shadow-lg hover:shadow-xl",
              "transition-all duration-300 ease-out"
            )}
          >
            {/* Shimmer effect */}
            <span className="absolute inset-0 w-full h-full overflow-hidden">
              <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out pointer-events-none" />
            </span>

            <div className="flex items-center justify-center gap-3 relative z-10">
              <div className="bg-white/20 p-2 rounded-lg">
                <Share2 className="h-5 w-5 text-white" />
              </div>
              <span className="text-white font-semibold">Share Your Card</span>
            </div>
          </Button>
        </div>
      </div>
    );
  }

  // Default button for non-business users or non-authenticated users
  return (
    <Link href="/login">
      <div className="flex justify-center items-center">
        <Button className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black dark:text-neutral-900 px-6 py-2 rounded-full font-medium text-base relative overflow-hidden group">
        <span className="flex items-center">
          List Your Business Now
          <ArrowRight className="ml-2 w-4 h-4" />
        </span>
      </Button>
      </div>
    </Link>
  );
}
