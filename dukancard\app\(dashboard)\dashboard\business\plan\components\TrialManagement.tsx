"use client";

import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { activateTrialForFirstTimePaidSubscriber } from "@/lib/actions/subscription";
import { PricingPlan } from "@/lib/PricingPlans";

interface TrialManagementProps {
  dialogPlan: PricingPlan | null;
  billingCycle: "monthly" | "yearly";
  setDialogLoading: (_loading: boolean) => void;
  setIsFirstTimePaidPlanDialogOpen: (_open: boolean) => void;
  setActiveTab: (_tab: "plans" | "subscription") => void;
  startProcessing: (_message: string) => void;
  completeProcessing: (_success: boolean, _message?: string) => void;
  setSubscriptionCreated: (_message: string) => void;
}

export function useTrialManagement({
  dialogPlan,
  billingCycle,
  setDialogLoading,
  setIsFirstTimePaidPlanDialogOpen,
  setActiveTab,
  startProcessing,
  completeProcessing,
  setSubscriptionCreated,
}: TrialManagementProps) {
  const router = useRouter();

  // Handle trial activation for first-time paid subscribers
  const handleActivateTrial = async () => {
    if (!dialogPlan) return;

    // Set dialog loading state to true
    setDialogLoading(true);

    try {
      // Start processing state
      startProcessing("Activating your free trial...");

      // Close the dialog
      setIsFirstTimePaidPlanDialogOpen(false);

      // Call the server action to activate the trial
      const result = await activateTrialForFirstTimePaidSubscriber(
        dialogPlan.id as "basic" | "growth" | "pro" | "enterprise", // Type assertion to match PlanId
        billingCycle
      );

      if (!result.success) {
        // Show error toast with specific error message
        const errorMessage = result.error || "Failed to activate trial. Please try again.";

        toast.error("Trial Activation Error", {
          description: errorMessage,
        });

        completeProcessing(false, errorMessage);
        return;
      }

      // Show success toast
      toast.success("Trial Activated", {
        description: `Your 1-month free trial of the ${dialogPlan.name} plan has been activated successfully.`,
      });

      // Set subscription created status
      setSubscriptionCreated(`Your ${dialogPlan.name} plan trial has been activated successfully.`);

      // Set active tab to subscription
      setActiveTab("subscription");

      // Reset dialog loading state
      setDialogLoading(false);

      // Refresh the page to get the latest subscription status
      setTimeout(() => {
        router.refresh();
      }, 1500);
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : "An unexpected error occurred. Please try again.";

      // Show error toast
      toast.error("Trial Activation Error", {
        description: errorMessage,
      });

      // Reset processing state
      completeProcessing(false, errorMessage);

      // Reset dialog loading state
      setDialogLoading(false);
    }
  };

  return {
    handleActivateTrial,
  };
}
