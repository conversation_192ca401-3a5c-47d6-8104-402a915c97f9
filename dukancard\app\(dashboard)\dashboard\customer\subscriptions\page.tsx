import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert<PERSON>riangle, Bell } from 'lucide-react';
import SubscriptionsPageClient from './components/SubscriptionsPageClient';
import { Suspense } from 'react';
import { SubscriptionListSkeleton } from '@/app/components/shared/subscriptions';
import { requireCompleteProfile } from '@/lib/actions/customerProfiles/addressValidation';

// Import the fetchSubscriptions function
import { fetchSubscriptions } from './actions';

export const metadata: Metadata = {
  title: "My Subscriptions - Dukancard",
  robots: "noindex, nofollow",
};

export default async function CustomerSubscriptionsPage({
  searchParams
}: {
  searchParams: Promise<{ search?: string; page?: string }>
}) {
  // Properly await searchParams to fix the error
  const { search, page: pageParam } = await searchParams;
  const supabase = await createClient();
  const page = pageParam ? parseInt(pageParam) : 1;
  const searchTerm = search || "";

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/login?message=Please log in to view your subscriptions.');
  }

  // Check if customer has complete address
  await requireCompleteProfile(user.id);

  try {
    // Fetch subscriptions with pagination and search
    const subscriptionsResult = await fetchSubscriptions(
      user.id,
      page,
      10, // 10 items per page
      searchTerm
    );

    // Wrap in a div to ensure proper layout with the skeleton
    return (
      <div className="relative space-y-6 max-w-6xl mx-auto">
        <Suspense fallback={
          <div className="relative z-10">
            <Card className="border shadow-md bg-white dark:bg-black border-neutral-200 dark:border-neutral-800">
              <CardHeader className="pb-4 border-b border-neutral-100 dark:border-neutral-800">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                  <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-500 dark:text-blue-400 self-start">
                    <Bell className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                      Subscribed Businesses
                    </h3>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                      Businesses you&apos;re following for updates
                    </p>
                  </div>
                </div>

                {/* Search skeleton */}
                <div className="mt-4">
                  <Skeleton className="h-10 w-full rounded-md" />
                </div>
              </CardHeader>

              <CardContent className="pt-4">
                <SubscriptionListSkeleton />
              </CardContent>
            </Card>
          </div>
        }>
          <SubscriptionsPageClient
            initialSubscriptions={subscriptionsResult.items}
            totalCount={subscriptionsResult.totalCount}
            currentPage={subscriptionsResult.currentPage}
            searchTerm={searchTerm}
          />
        </Suspense>
      </div>
    );
  } catch (_error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Could not load your subscriptions. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }
}
