"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ChevronRight } from "lucide-react";

interface NavItem {
  id: string;
  title: string;
}

interface PolicyNavigationProps {
  items: NavItem[];
}

export default function PolicyNavigation({ items }: PolicyNavigationProps) {
  const [activeSection, setActiveSection] = useState<string>("");

  // Handle scroll and update active section
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 100;

      // Find the section that is currently in view
      for (let i = items.length - 1; i >= 0; i--) {
        const section = document.getElementById(items[i].id);
        if (section && section.offsetTop <= scrollPosition) {
          setActiveSection(items[i].id);
          break;
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Initial check

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [items]);

  // Smooth scroll to section
  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 80, // Adjust for header height
        behavior: "smooth",
      });
      setActiveSection(id);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.3 },
    },
  };

  return (
    <Card className="p-4 border border-border shadow-sm">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-2"
      >
        <h3 className="font-medium text-lg mb-3">Table of Contents</h3>
        <div className="space-y-1.5">
          {items.map((item, index) => (
            <motion.div
              key={item.id}
              variants={itemVariants}
              custom={index}
              whileHover={{ x: 5 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-left ${
                  activeSection === item.id
                    ? "bg-muted text-[var(--brand-gold)]"
                    : ""
                }`}
                onClick={() => scrollToSection(item.id)}
              >
                <ChevronRight
                  className={`h-4 w-4 mr-2 transition-transform duration-200 ${
                    activeSection === item.id ? "rotate-90" : ""
                  }`}
                />
                {item.title}
              </Button>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </Card>
  );
}
