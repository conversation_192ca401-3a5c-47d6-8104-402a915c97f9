"use client";

import { motion } from "framer-motion";
import { Star } from "lucide-react";
import EnhancedReviewListClient from "./EnhancedReviewListClient";
import ReviewsBackground from "./ReviewsBackground";
import { cn } from "@/lib/utils";
import { formatIndianNumberShort } from "@/lib/utils";

interface ReviewsPageClientProps {
  reviewsCount: number;
}

export default function ReviewsPageClient({ reviewsCount }: ReviewsPageClientProps) {
  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-6"
    >
      <motion.div variants={itemVariants} className="mb-6">
        {/* Main card container - updated to match business dashboard style */}
        <div className={cn(
          "rounded-xl border border-neutral-200 dark:border-neutral-800",
          "bg-white dark:bg-black", // Changed to black in dark mode
          "shadow-md p-4 sm:p-5 md:p-6 mb-4",
          "transition-all duration-300 hover:shadow-lg",
          "relative overflow-hidden"
        )}>
          {/* Background with subtle pattern only - no glow effect */}
          <ReviewsBackground />

          {/* Card background with subtle pattern */}
          <div
            className="absolute inset-0 pointer-events-none opacity-5 dark:opacity-10"
            style={{
              backgroundImage: `url("/decorative/card-texture.svg")`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }}
          ></div>

          {/* Content with relative positioning */}
          <div className="relative z-10">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
              <div className="p-2 rounded-lg bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 self-start">
                <Star className="w-4 sm:w-5 h-4 sm:h-5" />
              </div>
              <div className="flex-1">
                <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Your Reviews ({formatIndianNumberShort(reviewsCount)})
                </h3>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                  Reviews you&apos;ve written for businesses
                </p>
              </div>
            </div>

            {/* Enhanced Review List - now handles its own data fetching */}
            <EnhancedReviewListClient />
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
