'use client';

import { useMemo } from 'react';
import { SubscriptionList, SubscriptionData } from '@/app/components/shared/subscriptions';
import { SubscriptionWithProfile } from './actions';

interface SubscriptionListClientProps {
  initialSubscriptions: SubscriptionWithProfile[];
}

export default function SubscriptionListClient({ initialSubscriptions }: SubscriptionListClientProps) {
  // Transform the data to match the shared component interface
  const transformedSubscriptions: SubscriptionData[] = useMemo(() => {
    return initialSubscriptions.map(sub => ({
      id: sub.id,
      profile: sub.business_profiles ? {
        id: sub.business_profiles.id,
        name: sub.business_profiles.business_name,
        slug: sub.business_profiles.business_slug,
        logo_url: sub.business_profiles.logo_url,
        city: sub.business_profiles.city,
        state: sub.business_profiles.state,
        pincode: sub.business_profiles.pincode,
        address_line: sub.business_profiles.address_line,
        type: 'business' as const,
      } : null
    })).filter(sub => sub.profile !== null) as SubscriptionData[];
  }, [initialSubscriptions]);

  return (
    <SubscriptionList
      initialSubscriptions={transformedSubscriptions}
      showUnsubscribe={true}
      emptyMessage="You haven't subscribed to any businesses yet."
      emptyDescription="Subscribe to businesses to receive updates and notifications."
      showDiscoverButton={true}
    />
  );
}
