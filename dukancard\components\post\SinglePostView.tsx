'use client';

import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';
import UnifiedPostCard from '@/components/feed/shared/UnifiedPostCard';

interface SinglePostViewProps {
  post: UnifiedPost;
}

/**
 * Client component for displaying a single post
 * Uses the same UnifiedPostCard component as the feed for exact consistency
 */
export default function SinglePostView({ post }: SinglePostViewProps) {
  return (
    <UnifiedPostCard
      post={post}
      index={0}
      showActualAspectRatio={true}
      disablePostClick={true}
      enableImageFullscreen={true}
    />
  );
}




