"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Lightbulb, Target, Rocket } from "lucide-react";
import { Card } from "@/components/ui/card";

export default function MissionVisionSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    },
  };

  const cards = [
    {
      title: "Our Mission",
      icon: <Target className="w-10 h-10 text-[var(--brand-gold)]" />,
      content: "To democratize digital presence by providing affordable, premium-quality digital business cards and online tools to businesses of all sizes across India."
    },
    {
      title: "Our Vision",
      icon: <Lightbulb className="w-10 h-10 text-[var(--brand-gold)]" />,
      content: "A future where every business, from corner shops to corporations, has an equal opportunity to thrive in the digital economy with tools that were once only available to large enterprises."
    },
    {
      title: "Our Goal",
      icon: <Rocket className="w-10 h-10 text-[var(--brand-gold)]" />,
      content: "To empower 100,000 Indian businesses with digital tools by 2026, creating a network of digitally-enabled entrepreneurs who can compete effectively in the modern marketplace."
    }
  ];

  return (
    <section
      ref={sectionRef}
      className="py-20 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto"
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="text-center mb-16"
      >
        <motion.h2
          variants={itemVariants}
          className="text-3xl md:text-4xl font-bold text-foreground mb-4"
        >
          Our <span className="text-[var(--brand-gold)]">Purpose</span> & Direction
        </motion.h2>
        <motion.p
          variants={itemVariants}
          className="text-lg text-muted-foreground max-w-2xl mx-auto"
        >
          Guided by a clear mission and ambitious vision, we&apos;re building the future of digital business tools.
        </motion.p>
      </motion.div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="grid grid-cols-1 md:grid-cols-3 gap-8"
      >
        {cards.map((card, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            whileHover={{
              y: -10,
              transition: { type: "spring", stiffness: 300 }
            }}
          >
            <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/20 p-8 h-full hover:border-primary/50 dark:hover:border-[var(--brand-gold)]/50 transition-all duration-300 hover:shadow-lg dark:hover:shadow-lg dark:hover:shadow-[var(--brand-gold)]/10">
              <div className="flex flex-col h-full">
                <div className="mb-6 flex justify-center">
                  {/* Icon with glow effect */}
                  <motion.div
                    className="relative"
                    whileHover={{ scale: 1.1 }}
                  >
                    {card.icon}
                    <motion.div
                      className="absolute inset-0 rounded-full blur-md -z-10"
                      animate={{
                        opacity: [0.2, 0.4, 0.2],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        repeatType: "reverse",
                        delay: index * 0.3,
                      }}
                      style={{
                        backgroundColor: "rgba(var(--brand-gold-rgb), 0.2)",
                      }}
                    />
                  </motion.div>
                </div>

                <h3 className="text-2xl font-semibold text-card-foreground mb-4 text-center">
                  {card.title}
                </h3>

                <p className="text-muted-foreground text-center flex-grow">
                  {card.content}
                </p>
              </div>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </section>
  );
}
