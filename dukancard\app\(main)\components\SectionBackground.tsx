"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";

interface SectionBackgroundProps {
  className?: string;
  variant?: "gold" | "blue" | "purple" | "gradient" | "subtle";
  intensity?: "low" | "medium" | "high";
  animate?: boolean;
  children?: React.ReactNode;
}

export default function SectionBackground({
  className = "",
  variant = "gold",
  intensity = "medium",
  animate = true,
  children,
}: SectionBackgroundProps) {
  const [_isClient, setIsClient] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Only render on client side and detect mobile
  useEffect(() => {
    setIsClient(true);
    setIsMobile(window.innerWidth < 768);

    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Get background colors based on variant and intensity
  const getBgColors = () => {
    const intensityMap = {
      low: { light: "5", dark: "10" },
      medium: { light: "10", dark: "15" },
      high: { light: "15", dark: "20" },
    };

    const intensityValue = intensityMap[intensity];

    switch (variant) {
      case "gold":
        return {
          primary: `bg-[var(--brand-gold)]/${intensityValue.light} dark:bg-[var(--brand-gold)]/${intensityValue.dark}`,
          secondary: "bg-blue-500/5 dark:bg-blue-500/10",
        };
      case "blue":
        return {
          primary: `bg-blue-500/${intensityValue.light} dark:bg-blue-500/${intensityValue.dark}`,
          secondary: "bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10",
        };
      case "purple":
        return {
          primary: `bg-purple-500/${intensityValue.light} dark:bg-purple-500/${intensityValue.dark}`,
          secondary: "bg-blue-500/5 dark:bg-blue-500/10",
        };
      case "gradient":
        return {
          primary: `bg-gradient-to-br from-[var(--brand-gold)]/${intensityValue.light} to-blue-500/${intensityValue.light} dark:from-[var(--brand-gold)]/${intensityValue.dark} dark:to-blue-500/${intensityValue.dark}`,
          secondary: "bg-transparent",
        };
      case "subtle":
      default:
        return {
          primary: `bg-neutral-200/${intensityValue.light} dark:bg-neutral-800/${intensityValue.dark}`,
          secondary: "bg-neutral-300/5 dark:bg-neutral-600/10",
        };
    }
  };

  const { primary, secondary } = getBgColors();

  return (
    <div className={`absolute inset-0 overflow-hidden -z-10 ${className}`}>
      {/* Primary blob */}
      <motion.div
        className={`absolute rounded-full blur-3xl opacity-70 ${primary}`}
        style={{
          width: isMobile ? "70%" : "50%",
          height: isMobile ? "70%" : "50%",
          top: "10%",
          right: "5%",
        }}
        animate={animate ? { opacity: 0.65 } : {}}
        transition={animate ? { duration: 4, repeat: Infinity, repeatType: "reverse" } : {}}
      />

      {/* Secondary blob */}
      <motion.div
        className={`absolute rounded-full blur-3xl opacity-60 ${secondary}`}
        style={{
          width: isMobile ? "60%" : "40%",
          height: isMobile ? "60%" : "40%",
          bottom: "10%",
          left: "5%",
        }}
        animate={animate ? { opacity: 0.55 } : {}}
        transition={animate ? { duration: 5, repeat: Infinity, repeatType: "reverse", delay: 1 } : {}}
      />

      {/* Render children if provided */}
      {children}
    </div>
  );
}
