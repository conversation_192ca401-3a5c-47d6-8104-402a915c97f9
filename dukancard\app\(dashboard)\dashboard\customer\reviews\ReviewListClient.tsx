'use client';

import { useState } from 'react';
import ReviewCard from '@/app/components/shared/reviews/ReviewCard';

// Re-define interfaces needed for props
interface BusinessProfileDataForReview {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
}

interface ReviewData {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: BusinessProfileDataForReview | null;
}

interface ReviewListClientProps {
  initialReviews: ReviewData[];
}

export default function ReviewListClient({ initialReviews }: ReviewListClientProps) {
  const [reviews, setReviews] = useState(initialReviews);

  const handleDeleteSuccess = (reviewIdToRemove: string) => {
    setReviews(currentReviews =>
      currentReviews.filter(review => review.id !== reviewIdToRemove)
    );
  };

  if (reviews.length === 0) {
    return (
      <div className="rounded-lg border border-neutral-200 dark:border-neutral-800 bg-neutral-50 dark:bg-neutral-800/50 p-8 text-center">
        <p className="text-neutral-600 dark:text-neutral-400">You haven&apos;t written any reviews yet.</p>
        <p className="text-sm text-neutral-500 dark:text-neutral-500 mt-2">
          Your reviews for businesses will appear here.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {reviews.map((review) => (
        <ReviewCard
          key={review.id}
          review={review}
          onDeleteSuccess={handleDeleteSuccess}
        />
      ))}
    </div>
  );
}
