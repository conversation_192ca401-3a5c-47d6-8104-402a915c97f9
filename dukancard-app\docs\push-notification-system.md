# Push Notification System Documentation

## Overview

The DukanCard push notification system provides intelligent, frequency-controlled notifications for business users when they receive activities like likes, subscriptions, and ratings. The system follows industry best practices from Facebook/Instagram to ensure high engagement without notification fatigue.

## System Architecture

```
Business Activity → Database Trigger → Notification Queue → Queue Processor → Push Service → User Device
```

### Key Components

1. **Database Layer**
   - `push_tokens` - Stores user push notification tokens
   - `push_notification_queue` - Reliable notification queue
   - `notification_analytics` - Performance tracking and optimization
   - Database triggers - Automatic notification queuing

2. **Service Layer**
   - `PushNotificationService` - Core notification handling
   - `NotificationFrequencyManager` - Intelligent frequency control
   - `PushNotificationQueueProcessor` - Background queue processing

3. **Client Integration**
   - `usePushNotifications` hook - React Native integration
   - Intersection Observer - View tracking for read status
   - Context providers - State management

## How It Works

### 1. Notification Triggering
```sql
-- When someone likes a business
INSERT INTO business_activities (business_profile_id, user_id, activity_type)
VALUES ('business-uuid', 'customer-uuid', 'like');

-- <PERSON><PERSON> automatically fires
-- → send_business_activity_push_notification()
-- → Inserts into push_notification_queue
```

### 2. Queue Processing
```typescript
// Background processor runs every 30 seconds
PushNotificationQueueProcessor.startProcessing();

// Fetches unprocessed notifications
// Applies frequency controls
// Sends via Expo push service
// Marks as processed
```

### 3. Frequency Management
```typescript
// Intelligent controls prevent spam
const shouldSend = await frequencyManager.shouldSendNotification(
  businessId, 
  'like', 
  'low' // priority
);

// Rules:
// - Max 5 notifications per day
// - 30 minutes between similar types
// - Quiet hours: 10 PM - 8 AM
// - Batching for similar notifications
```

## Notification Types & Priorities

| Type | Priority | Description | Example |
|------|----------|-------------|---------|
| `like` | Low | User liked business | "John liked your business" |
| `subscribe` | Medium | User subscribed | "John subscribed to your business" |
| `rating` | High | User rated business | "John rated your business 5/5" |
| `visit` | Low | Business card visit | "John visited your business card" |

## Best Practices Implementation

### 1. Frequency Control
- **Daily Limit**: Maximum 5 notifications per day per user
- **Minimum Interval**: 30 minutes between similar notification types
- **Quiet Hours**: No notifications between 10 PM - 8 AM
- **Priority Override**: High priority notifications bypass some limits

### 2. Intelligent Timing
- **User Engagement Patterns**: Send during user's active hours (9 AM - 6 PM default)
- **Batching**: Group similar notifications ("You received 3 new likes")
- **Delay Processing**: 1-second delay before marking as read to ensure user sees them

### 3. Content Optimization
- **Personalization**: Include user names in notification content
- **Clear CTAs**: Action-oriented messages under 50 characters
- **Rich Context**: Include business slug for direct navigation
- **Emoji Usage**: Visual indicators (❤️ for likes, ⭐ for ratings)

## Read/Unread Logic

### Problem Solved
Previously, notifications were marked as read immediately when modal/page opened, causing all notifications to appear in "Earlier" section.

### Solution Implemented
1. **Snapshot Approach**: Capture initial read status when modal/page opens
2. **View Tracking**: Use Intersection Observer to track when notifications are viewed
3. **Delayed Marking**: Only mark as read when ALL unread notifications have been viewed
4. **Section Persistence**: "New" vs "Earlier" based on initial read status, not current status

### React Native Implementation
```typescript
// Capture snapshot when modal opens
const [notificationsSnapshot, setNotificationsSnapshot] = useState<ActivityData[]>([]);

// Track viewed notifications
const markAsViewed = useCallback((notificationId: string) => {
  const wasInitiallyUnread = notificationsSnapshot.find(n => n.id === notificationId)?.is_read === false;
  if (wasInitiallyUnread) {
    setViewedUnreadIds(prev => new Set([...prev, notificationId]));
  }
}, [notificationsSnapshot]);

// Mark as read when all viewed
useEffect(() => {
  const initiallyUnreadIds = notificationsSnapshot.filter(n => !n.is_read).map(n => n.id);
  if (initiallyUnreadIds.length > 0 && initiallyUnreadIds.every(id => viewedUnreadIds.has(id))) {
    setTimeout(() => markAllAsRead(), 1000);
  }
}, [viewedUnreadIds, notificationsSnapshot]);
```

### Next.js Implementation
```typescript
// Similar approach with Intersection Observer
useEffect(() => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        onView(activity.id);
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.5 });
}, []);
```

## Database Schema

### Push Tokens Table
```sql
CREATE TABLE push_tokens (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    token TEXT NOT NULL,
    user_type TEXT NOT NULL CHECK (user_type IN ('customer', 'business')),
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android', 'web')),
    device_info JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Notification Queue Table
```sql
CREATE TABLE push_notification_queue (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type TEXT NOT NULL,
    data JSONB NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);
```

### Analytics Table
```sql
CREATE TABLE notification_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type TEXT NOT NULL,
    priority TEXT NOT NULL DEFAULT 'medium',
    sent_at TIMESTAMP WITH TIME ZONE NOT NULL,
    date DATE NOT NULL,
    opened BOOLEAN DEFAULT FALSE,
    opened_at TIMESTAMP WITH TIME ZONE,
    action_taken BOOLEAN DEFAULT FALSE,
    action_type TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Usage Examples

### Register Push Token
```typescript
const pushService = PushNotificationService.getInstance();
await pushService.registerPushToken(userId, 'business');
```

### Send Business Activity Notification
```typescript
await pushService.sendBusinessActivityNotification({
  type: 'like',
  businessId: 'business-uuid',
  userId: 'customer-uuid',
  userName: 'John Doe',
  userType: 'customer',
  businessSlug: 'johns-cafe'
});
```

### Track Notification Performance
```sql
-- Daily open rates
SELECT 
    date,
    notification_type,
    COUNT(*) as total_sent,
    COUNT(*) FILTER (WHERE opened = TRUE) as total_opened,
    ROUND(COUNT(*) FILTER (WHERE opened = TRUE) * 100.0 / COUNT(*), 2) as open_rate
FROM notification_analytics 
WHERE date >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY date, notification_type;
```

## Monitoring & Analytics

### Key Metrics
- **Delivery Rate**: Percentage of notifications successfully sent
- **Open Rate**: Percentage of notifications opened by users
- **Action Rate**: Percentage of opened notifications that led to user actions
- **Frequency Compliance**: Adherence to daily limits and timing rules

### Health Checks
- Queue processing time and backlog size
- Failed notification retry counts
- Token validity and cleanup status
- Frequency rule violations

## Deployment Considerations

### Environment Setup
1. **Expo Push Notifications**: Configure Expo project for push notifications
2. **Database Migrations**: Apply all push notification table schemas
3. **Background Processing**: Ensure queue processor runs in production
4. **Token Management**: Implement token cleanup and refresh logic

### Performance Optimization
1. **Batch Processing**: Process notifications in batches of 100
2. **Index Optimization**: Proper indexes on queue and analytics tables
3. **Data Cleanup**: Regular cleanup of old processed notifications
4. **Caching**: Cache user engagement patterns for intelligent timing

## Security & Privacy

### Data Protection
- Push tokens encrypted in transit and at rest
- User consent required before registering tokens
- Ability to disable notifications per user
- Automatic token cleanup for inactive users

### Access Control
- RLS policies restrict access to user's own data
- Service role permissions for background processing
- Audit logging for notification analytics

## Future Enhancements

1. **A/B Testing**: Test different notification content and timing
2. **Machine Learning**: Optimize send times based on user behavior
3. **Rich Notifications**: Add images and action buttons
4. **Cross-Platform**: Extend to web push notifications
5. **Segmentation**: Target notifications based on user segments
