'use client';

import { AlertTriangle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useState } from "react";

interface UpiPaymentMethodWarningProps {
  paymentMethod: string | null;
  onContinue: () => void;
  onCancel: () => void;
}

export function UpiPaymentMethodWarning({
  paymentMethod,
  onContinue,
  onCancel
}: UpiPaymentMethodWarningProps) {
  const [open, setOpen] = useState(true);

  const handleContinue = () => {
    setOpen(false);
    onContinue();
  };

  const handleCancel = () => {
    setOpen(false);
    onCancel();
  };

  // Determine the scenario and appropriate messaging
  const isNullPaymentMethod = !paymentMethod;
  const isUpiOrEmandate = paymentMethod && ['upi', 'emandate', 'enach'].includes(paymentMethod.toLowerCase());

  const formattedPaymentMethod = paymentMethod
    ? (paymentMethod.toLowerCase() === 'upi'
        ? 'UPI'
        : paymentMethod.toLowerCase() === 'emandate'
          ? 'E-Mandate'
          : paymentMethod.toLowerCase() === 'enach'
            ? 'E-NACH'
            : paymentMethod)
    : 'Unknown';

  // Determine title and description based on scenario
  const getTitle = () => {
    if (isNullPaymentMethod) {
      return "Payment Method Required";
    }
    return "Payment Method Limitation";
  };

  const getDescription = () => {
    if (isNullPaymentMethod) {
      return "We need to create a new subscription for your plan change due to missing payment method information.";
    }
    return `Your current payment method (${formattedPaymentMethod}) has limitations when changing plans.`;
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            {getTitle()}
          </DialogTitle>
          <DialogDescription>
            {getDescription()}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert variant="default" className="bg-amber-50 text-amber-800 border-amber-200">
            <AlertTitle className="text-amber-800 font-medium">Important Information</AlertTitle>
            <AlertDescription className="text-amber-700">
              {isNullPaymentMethod ? (
                <>
                  <p className="mb-2">
                    Due to missing payment method information, we need to create a new subscription for your plan change.
                  </p>
                  <p className="mb-2">
                    If you continue, we will:
                  </p>
                  <ul className="list-disc pl-5 space-y-1 mb-2">
                    <li>Create a new subscription with your selected plan</li>
                    <li>Your current subscription will remain active until the new one is confirmed</li>
                    <li>You will need to complete payment authorization for the new subscription</li>
                    <li>Once confirmed, your old subscription will be automatically cancelled</li>
                  </ul>
                </>
              ) : isUpiOrEmandate ? (
                <>
                  <p className="mb-2">
                    Due to RBI regulations and Razorpay limitations, we cannot directly update subscriptions that use {formattedPaymentMethod} as the payment method.
                  </p>
                  <p className="mb-2">
                    If you continue, we will:
                  </p>
                  <ul className="list-disc pl-5 space-y-1 mb-2">
                    <li>Create a new subscription with your selected plan</li>
                    <li>Your current subscription will remain active until the new one is confirmed</li>
                    <li>You will need to complete payment authorization for the new subscription</li>
                    <li>Once confirmed, your old subscription will be automatically cancelled</li>
                  </ul>
                </>
              ) : (
                <>
                  <p className="mb-2">
                    Due to payment method limitations, we need to create a new subscription for your plan change.
                  </p>
                  <p className="mb-2">
                    If you continue, we will:
                  </p>
                  <ul className="list-disc pl-5 space-y-1 mb-2">
                    <li>Create a new subscription with your selected plan</li>
                    <li>Your current subscription will remain active until the new one is confirmed</li>
                    <li>You will need to complete payment authorization for the new subscription</li>
                    <li>Once confirmed, your old subscription will be automatically cancelled</li>
                  </ul>
                </>
              )}
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter className="flex flex-row justify-between sm:justify-between gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleContinue}>
            Continue Anyway
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function UpiPaymentMethodAlert({
  paymentMethod
}: {
  paymentMethod: string;
}) {
  const formattedPaymentMethod = paymentMethod.toLowerCase() === 'upi'
    ? 'UPI'
    : paymentMethod.toLowerCase() === 'emandate'
      ? 'E-Mandate'
      : paymentMethod.toLowerCase() === 'enach'
        ? 'E-NACH'
        : paymentMethod;

  return (
    <Alert variant="default" className="bg-amber-50 text-amber-800 border-amber-200 mb-4">
      <AlertTriangle className="h-4 w-4 text-amber-600" />
      <AlertTitle className="text-amber-800 font-medium">Payment Method Limitation</AlertTitle>
      <AlertDescription className="text-amber-700">
        Your current payment method ({formattedPaymentMethod}) has limitations when changing plans.
        If you switch plans, your current subscription will be cancelled immediately, and you will lose any remaining days from your current billing cycle with no refund.
      </AlertDescription>
    </Alert>
  );
}
