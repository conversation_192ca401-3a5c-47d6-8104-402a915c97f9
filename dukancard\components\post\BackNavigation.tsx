'use client';

import { useRouter } from 'next/navigation';

/**
 * Back navigation component for single post pages
 */
export default function BackNavigation() {
  const router = useRouter();

  const handleBack = () => {
    // Try to go back in history, fallback to home page
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/');
    }
  };

  return (
    <div className="mb-4">
      <button
        onClick={handleBack}
        className="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors cursor-pointer"
      >
        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
        Back to Feed
      </button>
    </div>
  );
}
