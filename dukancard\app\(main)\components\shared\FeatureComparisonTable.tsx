"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>, Minus, HelpCircle } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { PricingPlan } from "@/lib/PricingPlans";

interface FeatureComparisonTableProps {
  plans: PricingPlan[];
  title?: string;
  subtitle?: string;
  className?: string;
}

// Feature categories for grouping
const featureCategories = [
  { id: "core", name: "Core Features" },
  { id: "analytics", name: "Analytics & Insights" },
  { id: "customization", name: "Customization" },
  { id: "management", name: "Management" },
  { id: "support", name: "Support" },
];

// Feature definitions with categories and tooltips - restored from original
const featureDefinitions = [
  // Core Features
  {
    id: "digital-card",
    name: "Digital Business Card",
    category: "core",
    tooltip: "Create a digital business card with your contact information"
  },
  {
    id: "qr-code",
    name: "QR Code Generation",
    category: "core",
    tooltip: "Generate a QR code that links to your digital card"
  },
  {
    id: "social-links",
    name: "Social Media Links",
    category: "core",
    tooltip: "Add links to your social media profiles"
  },
  {
    id: "digital-storefront",
    name: "Digital Storefront",
    category: "core",
    tooltip: "Get a shareable dukancard.in/[your-slug] web presence"
  },
  {
    id: "product-listings",
    name: "Product Listings",
    category: "core",
    tooltip: "Showcase your products or services with details and images"
  },
  {
    id: "customer-subscriptions",
    name: "Customer Subscriptions",
    category: "core",
    tooltip: "Allow customers to subscribe to your business"
  },
  {
    id: "business-subscriptions",
    name: "Business Subscriptions",
    category: "core",
    tooltip: "Subscribe to other businesses and let them subscribe to you"
  },
  {
    id: "ratings-reviews",
    name: "Ratings & Reviews",
    category: "core",
    tooltip: "Collect and display customer reviews"
  },
  {
    id: "like-feature",
    name: "Like Feature",
    category: "core",
    tooltip: "Let customers like your business card"
  },

  // Analytics Features
  {
    id: "basic-analytics",
    name: "Basic Analytics (Views/Clicks)",
    category: "analytics",
    tooltip: "Track views and clicks on your digital card"
  },
  {
    id: "product-views",
    name: "Product Views (Coming Soon)",
    category: "analytics",
    tooltip: "Track views and engagement for individual products"
  },
  {
    id: "advanced-analytics",
    name: "Advanced Analytics (Coming Soon)",
    category: "analytics",
    tooltip: "Comprehensive analytics with custom reports and insights"
  },

  // Media Features
  {
    id: "photo-gallery",
    name: "Photo Gallery",
    category: "core",
    tooltip: "Upload and display images in a gallery"
  },

  // Customization Features
  {
    id: "custom-themes",
    name: "Custom Theme & Colors",
    category: "customization",
    tooltip: "Customize the theme and colors of your digital card"
  },
  {
    id: "custom-branding",
    name: "Custom Branding",
    category: "customization",
    tooltip: "Remove Dukancard branding from your digital card"
  },
  {
    id: "no-ads",
    name: "No Ads",
    category: "customization",
    tooltip: "No advertisements displayed on your digital card"
  },

  // Management Features
  {
    id: "order-management",
    name: "Order Management (Coming Soon)",
    category: "management",
    tooltip: "Accept and manage customer orders with delivery tracking"
  },
  {
    id: "custom-domain",
    name: "Custom Domain (Coming Soon)",
    category: "management",
    tooltip: "Use your own domain for your digital business card"
  },

  // Support Features
  {
    id: "priority-support",
    name: "Priority Support",
    category: "support",
    tooltip: "Get priority email and chat support"
  },
  {
    id: "api-access",
    name: "API Access",
    category: "support",
    tooltip: "Integrate Dukancard with your existing business systems"
  },
];

export default function FeatureComparisonTable({ 
  plans, 
  title = "Feature Comparison",
  subtitle = "Compare features across plans to find the perfect fit for your business needs.",
  className = ""
}: FeatureComparisonTableProps) {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Filter features by category if one is selected
  const filteredFeatures = selectedCategory 
    ? featureDefinitions.filter(f => f.category === selectedCategory)
    : featureDefinitions;

  // Helper function to get feature value for a plan - restored from original logic
  const getFeatureValue = (featureName: string, plan: PricingPlan) => {
    // Handle core features that all plans have
    if (["Digital Business Card", "QR Code Generation", "Social Media Links", "Digital Storefront", "Customer Subscriptions", "Business Subscriptions", "Ratings & Reviews", "Like Feature"].includes(featureName)) {
      return <CheckCircle className="w-5 h-5 text-green-500 mx-auto" />;
    }

    // Handle Product Listings with specific limits
    if (featureName === "Product Listings") {
      if (plan.id === "free") return "5 Products";
      if (plan.id === "basic") return "15 Products";
      if (plan.id === "growth") return "50 Products";
      if (plan.id === "pro" || plan.id === "enterprise") return "Unlimited";
      return <Minus className="w-5 h-5 text-neutral-400 mx-auto" />;
    }

    // Handle Basic Analytics
    if (featureName === "Basic Analytics (Views/Clicks)") {
      if (plan.id === "free") return <Minus className="w-5 h-5 text-neutral-400 mx-auto" />;
      return <CheckCircle className="w-5 h-5 text-green-500 mx-auto" />;
    }

    // Handle Product Views (Coming Soon)
    if (featureName === "Product Views (Coming Soon)") {
      if (plan.id === "free" || plan.id === "basic") return <Minus className="w-5 h-5 text-neutral-400 mx-auto" />;
      return "Coming Soon";
    }

    // Handle Advanced Analytics (Coming Soon)
    if (featureName === "Advanced Analytics (Coming Soon)") {
      if (plan.id === "free" || plan.id === "basic" || plan.id === "growth") return <Minus className="w-5 h-5 text-neutral-400 mx-auto" />;
      return "Coming Soon";
    }

    // Handle Photo Gallery with specific limits
    if (featureName === "Photo Gallery") {
      if (plan.id === "free") return "1 Image";
      if (plan.id === "basic") return "3 Images";
      if (plan.id === "growth") return "10 Images";
      if (plan.id === "pro") return "50 Images";
      if (plan.id === "enterprise") return "100 Images";
      return <Minus className="w-5 h-5 text-neutral-400 mx-auto" />;
    }

    // Handle Customization Features (Pro and Enterprise only)
    if (["Custom Theme & Colors", "Custom Branding", "No Ads"].includes(featureName)) {
      if (plan.id === "pro" || plan.id === "enterprise") return <CheckCircle className="w-5 h-5 text-green-500 mx-auto" />;
      return <Minus className="w-5 h-5 text-neutral-400 mx-auto" />;
    }

    // Handle Order Management (Coming Soon) - Growth and above
    if (featureName === "Order Management (Coming Soon)") {
      if (plan.id === "free" || plan.id === "basic") return <Minus className="w-5 h-5 text-neutral-400 mx-auto" />;
      return <CheckCircle className="w-5 h-5 text-green-500 mx-auto" />;
    }

    // Handle Custom Domain (Coming Soon) - Pro and Enterprise
    if (featureName === "Custom Domain (Coming Soon)") {
      if (plan.id === "free" || plan.id === "basic" || plan.id === "growth") return <Minus className="w-5 h-5 text-neutral-400 mx-auto" />;
      return "Coming Soon";
    }

    // Handle Priority Support - Pro and Enterprise only
    if (featureName === "Priority Support") {
      if (plan.id === "pro" || plan.id === "enterprise") return <CheckCircle className="w-5 h-5 text-green-500 mx-auto" />;
      return <Minus className="w-5 h-5 text-neutral-400 mx-auto" />;
    }

    // Handle API Access - Enterprise only (potential)
    if (featureName === "API Access") {
      if (plan.id === "enterprise") return "(Potential)";
      return <Minus className="w-5 h-5 text-neutral-400 mx-auto" />;
    }

    // Default fallback
    return <Minus className="w-5 h-5 text-neutral-400 mx-auto" />;
  };

  return (
    <div className={cn("w-full", className)}>
      {/* Section Title */}
      <div className="text-center mb-8">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
          {title.split(" ").map((word, index) => (
            <span key={index}>
              {word === "Comparison" ? (
                <span className="text-[var(--brand-gold)] relative">
                  {word}
                  <motion.div
                    className="absolute -bottom-1 left-0 h-1 bg-[var(--brand-gold)]/30 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: "100%" }}
                    transition={{ duration: 0.7, delay: 0.3 }}
                  />
                </span>
              ) : (
                word
              )}
              {index < title.split(" ").length - 1 && " "}
            </span>
          ))}
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          {subtitle}
        </p>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap justify-center gap-2 mb-8">
        <button
          onClick={() => setSelectedCategory(null)}
          className={cn(
            "px-4 py-2 rounded-full text-sm font-medium transition-colors",
            selectedCategory === null
              ? "bg-[var(--brand-gold)] text-black dark:text-neutral-900"
              : "bg-muted text-muted-foreground hover:text-foreground"
          )}
        >
          All Features
        </button>
        {featureCategories.map((category) => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={cn(
              "px-4 py-2 rounded-full text-sm font-medium transition-colors",
              selectedCategory === category.id
                ? "bg-[var(--brand-gold)] text-black dark:text-neutral-900"
                : "bg-muted text-muted-foreground hover:text-foreground"
            )}
          >
            {category.name}
          </button>
        ))}
      </div>

      {/* Comparison Table */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-neutral-200 dark:border-neutral-800">
          <thead>
            <tr className="bg-neutral-100 dark:bg-neutral-800">
              <th className="p-4 text-left font-semibold text-foreground border-b border-neutral-200 dark:border-neutral-800">
                Feature
              </th>
              {plans.map((plan) => (
                <th
                  key={plan.id}
                  className="p-4 text-center font-semibold text-foreground border-b border-neutral-200 dark:border-neutral-800"
                >
                  <div className={cn(
                    plan.featured ? "text-[var(--brand-gold)]" : "",
                    "flex flex-col items-center"
                  )}>
                    <span>{plan.name.replace(" Plan", "")}</span>
                    {plan.mostPopular && (
                      <span className="text-xs mt-1 bg-[var(--brand-gold)]/20 text-[var(--brand-gold)] px-2 py-0.5 rounded-full">
                        Most Popular
                      </span>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredFeatures.map((feature) => (
              <tr
                key={feature.id}
                className="border-b border-neutral-200 dark:border-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-900"
              >
                <td className="p-4 text-left text-foreground font-medium">
                  <div className="flex items-center">
                    {feature.name}
                    {feature.tooltip && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="ml-2 h-4 w-4 text-neutral-400 cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs">{feature.tooltip}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </td>
                {plans.map((plan) => (
                  <td key={plan.id} className="p-4 text-center">
                    <div className="flex justify-center">
                      {getFeatureValue(feature.name, plan)}
                    </div>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
