"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Mail, Loader2, Save, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { updateCustomerEmail } from "../actions";

// Email form schema
const EmailFormSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
});

interface EmailFormProps {
  currentEmail?: string | null;
  isRequired?: boolean;
  isGoogleUser?: boolean;
}

export default function EmailForm({ currentEmail, isRequired = false, isGoogleUser = false }: EmailFormProps) {
  const [isPending, startTransition] = useTransition();
  const [message, setMessage] = useState<string | null>(null);

  const form = useForm<z.infer<typeof EmailFormSchema>>({
    resolver: zodResolver(EmailFormSchema),
    defaultValues: {
      email: currentEmail || "",
    },
  });

  const onSubmit = (values: z.infer<typeof EmailFormSchema>) => {
    // Check if email has actually changed
    if (values.email === currentEmail) {
      toast.info("Email address is the same as current.");
      return;
    }

    setMessage(null);
    startTransition(async () => {
      try {
        const formData = new FormData();
        formData.append("email", values.email);

        const result = await updateCustomerEmail(
          { message: null, success: false },
          formData
        );

        if (result.success) {
          toast.success(result.message || "Email updated successfully!");
          setMessage(result.message || "Email updated successfully!");
          // Don't reset form - keep the new email value
        } else {
          toast.error(result.message || "Failed to update email");
          setMessage(result.message || "Failed to update email");
        }
      } catch (error) {
        const errorMessage = "An unexpected error occurred";
        toast.error(errorMessage);
        setMessage(errorMessage);
        console.error("Email update error:", error);
      }
    });
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-2">
          <Mail className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          Email Address
          {isRequired && (
            <span className="text-red-500 text-sm font-normal">(Required)</span>
          )}
        </CardTitle>
        {isRequired && !currentEmail && !isGoogleUser && (
          <div className="flex items-center gap-2 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
            <AlertCircle className="w-4 h-4 text-amber-600 dark:text-amber-400 flex-shrink-0" />
            <p className="text-sm text-amber-700 dark:text-amber-300">
              Please add your email address to continue using the dashboard.
            </p>
          </div>
        )}
        {isGoogleUser && (
          <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <AlertCircle className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
            <p className="text-sm text-blue-700 dark:text-blue-300">
              This email is linked to your Google account and cannot be changed. You can add a phone number for alternative login access.
            </p>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                    Email Address
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>"
                      type="email"
                      {...field}
                      disabled={isPending || isGoogleUser}
                      readOnly={isGoogleUser}
                      className={`border-neutral-200 dark:border-neutral-700 ${
                        isGoogleUser
                          ? "bg-neutral-100 dark:bg-neutral-900 text-neutral-500 dark:text-neutral-400 cursor-not-allowed"
                          : "bg-neutral-50 dark:bg-neutral-800"
                      }`}
                    />
                  </FormControl>
                  <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400">
                    {isGoogleUser
                      ? "This email is linked to your Google account and cannot be modified."
                      : currentEmail
                        ? "This will update your email address in your account."
                        : "This email will be used for account notifications and password reset."
                    }
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {message && (
              <div
                className={`p-3 rounded-lg text-sm ${
                  message.includes("success") || message.includes("updated")
                    ? "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800"
                    : "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800"
                }`}
              >
                {message}
              </div>
            )}

            <Button
              type="submit"
              disabled={isPending || !form.formState.isValid || isGoogleUser}
              className={`w-full text-white ${
                isGoogleUser
                  ? "bg-neutral-400 dark:bg-neutral-600 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700"
              }`}
            >
              {isGoogleUser ? (
                <>
                  <Mail className="w-4 h-4 mr-2" />
                  Google Account Email
                </>
              ) : isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Updating Email...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {currentEmail ? "Update Email" : "Add Email"}
                </>
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
