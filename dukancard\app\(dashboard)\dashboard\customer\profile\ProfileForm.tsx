'use client';

import React, { useEffect, useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { updateCustomerProfile, type ProfileFormState } from './actions';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { User, Save, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

// Re-define schema slightly for client-side use with react-hook-form
const ProfileFormSchema = z.object({
  name: z.string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),
});

type ProfileFormData = z.infer<typeof ProfileFormSchema>;

interface ProfileFormProps {
  initialName: string | null;
}

export function ProfileForm({ initialName }: ProfileFormProps) {
  const [isPending, startTransition] = useTransition();
  const [formState, setFormState] = useState<ProfileFormState>({
    message: null,
    errors: {},
    success: false
  });

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(ProfileFormSchema),
    defaultValues: {
      name: initialName || '',
    },
    mode: 'onChange', // Validate on change
  });

  useEffect(() => {
    console.log('Form state changed:', formState);

    // Check if we've received a response from the server
    if (formState.message !== null || Object.keys(formState.errors || {}).length > 0) {
      console.log('Response received from server');

      if (formState.success) {
        toast.success(formState.message || 'Profile updated successfully!');
        // Optionally reset form or redirect, but revalidation handles UI update
      } else if (!formState.success) {
        // Show general errors if they exist and aren't field specific
        // Field specific errors are handled by react-hook-form
        if (!formState.errors || Object.keys(formState.errors).length === 0) {
           toast.error(formState.message);
        }
      }
    }
  }, [formState]);

   // Update default value if initialName changes after mount (e.g., due to revalidation)
   useEffect(() => {
    if (initialName) {
      form.reset({ name: initialName });
    }
  }, [initialName, form]);


  // Handle form submission with React's startTransition
  const onSubmit = async (data: ProfileFormData) => {
    console.log('Form submission started');

    // Create FormData from the form values
    const formData = new FormData();
    formData.append('name', data.name);

    // Use startTransition to handle the server action
    startTransition(async () => {
      try {
        console.log('Dispatching form data to server action');

        // Create initial state to pass to the server action
        const initialState: ProfileFormState = {
          message: null,
          errors: {},
          success: false
        };

        // Call the server action with the initial state and form data
        const result = await updateCustomerProfile(initialState, formData);
        console.log('Server action completed:', result);

        // Update the local form state with the result
        setFormState(result);
      } catch (error) {
        console.error('Error submitting form:', error);
        setFormState({
          message: 'An unexpected error occurred. Please try again.',
          success: false,
          errors: {}
        });
        toast.error('An unexpected error occurred. Please try again.');
      }
    });
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      {/* Display non-field specific errors from server action */}
      {formState.message && !formState.success && (!formState.errors || Object.keys(formState.errors).length === 0) && (
        <div className="p-3 rounded-md bg-red-50 dark:bg-red-950/30 text-red-600 dark:text-red-400 text-sm">
          {formState.message}
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="name" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Full Name
        </Label>
        <div className="relative">
          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400 dark:text-neutral-500" />
          <Input
            id="name"
            {...form.register('name')}
            className={cn(
              "pl-10 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",
              "focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]",
              "transition-all duration-200",
              isPending && "opacity-70"
            )}
            placeholder="Your full name"
            aria-invalid={!!form.formState.errors.name || !!formState.errors?.name}
            aria-describedby="name-error"
            disabled={isPending}
          />
        </div>
        {/* Client-side validation error */}
        {form.formState.errors.name && (
          <p id="name-error" className="text-sm font-medium text-red-500 dark:text-red-400 mt-1">
            {form.formState.errors.name.message}
          </p>
        )}
        {/* Server-side validation error */}
        {formState.errors?.name && (
          <p id="name-error-server" className="text-sm font-medium text-red-500 dark:text-red-400 mt-1">
            {formState.errors.name.join(', ')}
          </p>
        )}
      </div>

      <div className="mt-6 flex justify-end">
        <div className="relative group">
          {/* Border glow effect - matches button size */}
          <div
            className="absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300"
            style={{
              boxShadow: `inset 0 0 20px rgba(147, 51, 234, 0.2), 0 0 20px rgba(147, 51, 234, 0.3)`
            }}
          />

          {/* Strong decorative colored glow elements - positioned relative to button */}
          <div className="absolute -top-1 -right-1 w-6 h-6 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none" />
          <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none" />

          <Button
            type="submit"
            disabled={isPending}
            variant="outline"
            size="sm"
            className={`
              relative overflow-hidden rounded-xl p-3
              bg-white dark:bg-black
              border border-purple-200/50 dark:border-purple-700/50
              shadow-purple-500/40 shadow-lg
              hover:shadow-xl hover:shadow-purple-500/40
              transition-all duration-300
              text-purple-500 dark:text-purple-400
              hover:bg-purple-500/5 dark:hover:bg-purple-500/10
              text-xs sm:text-sm h-auto
              ${isPending ? 'cursor-not-allowed opacity-80' : ''}
            `}
          >
            <AnimatePresence mode="wait">
              {isPending ? (
                <motion.div
                  key="saving"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 10 }}
                  className="flex items-center justify-center"
                >
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </motion.div>
              ) : (
                <motion.div
                  key="save"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 10 }}
                  className="flex items-center justify-center"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </motion.div>
              )}
            </AnimatePresence>
          </Button>
        </div>
      </div>
    </form>
  );
}
