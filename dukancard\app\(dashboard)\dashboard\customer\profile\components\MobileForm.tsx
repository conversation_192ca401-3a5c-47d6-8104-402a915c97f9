"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Phone, Loader2, Save, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { updateCustomerMobile } from "../actions";
import { IndianMobileSchema } from "@/lib/schemas/authSchemas";

// Mobile form schema
const MobileFormSchema = z.object({
  mobile: IndianMobileSchema,
});

interface MobileFormProps {
  currentMobile?: string | null;
  isRequired?: boolean;
}

export default function MobileForm({ currentMobile, isRequired = false }: MobileFormProps) {
  const [isPending, startTransition] = useTransition();
  const [message, setMessage] = useState<string | null>(null);

  // Process current mobile to remove +91 prefix for display
  const processedCurrentMobile = currentMobile 
    ? currentMobile.replace(/^\+91/, '') 
    : '';

  const form = useForm<z.infer<typeof MobileFormSchema>>({
    resolver: zodResolver(MobileFormSchema),
    defaultValues: {
      mobile: processedCurrentMobile,
    },
  });

  const onSubmit = (values: z.infer<typeof MobileFormSchema>) => {
    // Check if mobile has actually changed
    if (values.mobile === processedCurrentMobile) {
      toast.info("Mobile number is the same as current.");
      return;
    }

    setMessage(null);
    startTransition(async () => {
      try {
        const formData = new FormData();
        formData.append("mobile", values.mobile);

        const result = await updateCustomerMobile(
          { message: null, success: false },
          formData
        );

        if (result.success) {
          toast.success(result.message || "Mobile number updated successfully!");
          setMessage(result.message || "Mobile number updated successfully!");
          // Don't reset form - keep the new mobile value
        } else {
          toast.error(result.message || "Failed to update mobile number");
          setMessage(result.message || "Failed to update mobile number");
        }
      } catch (error) {
        const errorMessage = "An unexpected error occurred";
        toast.error(errorMessage);
        setMessage(errorMessage);
        console.error("Mobile update error:", error);
      }
    });
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-2">
          <Phone className="w-5 h-5 text-green-600 dark:text-green-400" />
          Mobile Number
          {isRequired && (
            <span className="text-red-500 text-sm font-normal">(Required)</span>
          )}
        </CardTitle>
        {isRequired && !currentMobile && (
          <div className="flex items-center gap-2 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
            <AlertCircle className="w-4 h-4 text-amber-600 dark:text-amber-400 flex-shrink-0" />
            <p className="text-sm text-amber-700 dark:text-amber-300">
              Please add your mobile number to continue using the dashboard.
            </p>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="mobile"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                    Mobile Number
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-neutral-500 dark:text-neutral-400">
                        +91
                      </div>
                      <Input
                        placeholder="9876543210"
                        type="tel"
                        {...field}
                        disabled={isPending}
                        className="pl-12 bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                        maxLength={10}
                      />
                    </div>
                  </FormControl>
                  <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400">
                    {currentMobile 
                      ? "This will update your mobile number in your account."
                      : "This mobile number will be used for account access and notifications."
                    }
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {message && (
              <div
                className={`p-3 rounded-lg text-sm ${
                  message.includes("success") || message.includes("updated")
                    ? "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800"
                    : "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800"
                }`}
              >
                {message}
              </div>
            )}

            <Button
              type="submit"
              disabled={isPending || !form.formState.isValid}
              className="w-full bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 text-white"
            >
              {isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Updating Mobile...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {currentMobile ? "Update Mobile" : "Add Mobile"}
                </>
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
