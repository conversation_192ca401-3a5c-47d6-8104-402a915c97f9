"use client";

import { useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Clock, Trash } from "lucide-react";

// Convert 24-hour time format to 12-hour AM/PM format for display
const formatTimeTo12Hour = (time24: string): string => {
  if (!time24 || time24.length < 5) return time24;

  const [hourStr, minuteStr] = time24.split(":");
  const hour = parseInt(hourStr, 10);

  if (isNaN(hour)) return time24;

  const period = hour >= 12 ? "PM" : "AM";
  const hour12 = hour % 12 || 12; // Convert 0 to 12 for 12 AM

  return `${hour12}:${minuteStr} ${period}`;
};

// Define the structure for business hours
export interface DayHours {
  isOpen: boolean;
  openTime: string;
  closeTime: string;
}

export interface BusinessHours {
  monday: DayHours;
  tuesday: DayHours;
  wednesday: DayHours;
  thursday: DayHours;
  friday: DayHours;
  saturday: DayHours;
  sunday: DayHours;
}

// Empty hours template with all days closed and default times
const EMPTY_HOURS: DayHours = {
  isOpen: false,
  openTime: "09:00",
  closeTime: "18:00",
};

// Empty business hours with all days closed
const EMPTY_BUSINESS_HOURS: BusinessHours = {
  monday: { ...EMPTY_HOURS },
  tuesday: { ...EMPTY_HOURS },
  wednesday: { ...EMPTY_HOURS },
  thursday: { ...EMPTY_HOURS },
  friday: { ...EMPTY_HOURS },
  saturday: { ...EMPTY_HOURS },
  sunday: { ...EMPTY_HOURS },
};

interface BusinessHoursEditorProps {
  value: BusinessHours | null | undefined;
  onChange: (_value: BusinessHours | null) => void;
}

// Define day groups for easier management
type DayGroup = "weekdays" | "weekend" | "all";

interface DayGroupConfig {
  label: string;
  days: (keyof BusinessHours)[];
}

const DAY_GROUPS: Record<DayGroup, DayGroupConfig> = {
  weekdays: {
    label: "Mon-Fri",
    days: ["monday", "tuesday", "wednesday", "thursday", "friday"],
  },
  weekend: {
    label: "Sat-Sun",
    days: ["saturday", "sunday"],
  },
  all: {
    label: "All Days",
    days: [
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
      "sunday",
    ],
  },
};

export function BusinessHoursEditor({
  value,
  onChange,
}: BusinessHoursEditorProps) {
  // Initialize state with either the provided value or empty hours
  const [hours, setHours] = useState<BusinessHours>(() => {
    try {
      // If value is null or undefined, return empty hours (all days closed)
      if (!value) {
        return { ...EMPTY_BUSINESS_HOURS };
      }

      // Ensure all days are present in the value
      const initialValue = { ...EMPTY_BUSINESS_HOURS };

      // Only override with existing values if they match our expected structure
      Object.keys(EMPTY_BUSINESS_HOURS).forEach((day) => {
        const typedDay = day as keyof BusinessHours;
        if (value[typedDay] && typeof value[typedDay] === "object") {
          const dayData = value[typedDay] as Partial<DayHours>;
          initialValue[typedDay] = {
            isOpen:
              typeof dayData.isOpen === "boolean" ? dayData.isOpen : false,
            openTime:
              typeof dayData.openTime === "string" ? dayData.openTime : "09:00",
            closeTime:
              typeof dayData.closeTime === "string"
                ? dayData.closeTime
                : "18:00",
          };
        }
      });

      return initialValue;
    } catch (error) {
      console.error("Error parsing business hours:", error);
      return { ...EMPTY_BUSINESS_HOURS };
    }
  });

  // No longer tracking active group as we use tabs instead

  // Track if the business is open on specific days
  const [isOpen, setIsOpen] = useState<Record<DayGroup, boolean>>(() => {
    // Initialize based on current hours
    return {
      weekdays: DAY_GROUPS.weekdays.days.some((day) => hours[day].isOpen),
      weekend: DAY_GROUPS.weekend.days.some((day) => hours[day].isOpen),
      all: Object.values(hours).some((dayHour) => dayHour.isOpen),
    };
  });

  // Track hours for each group
  const [groupHours, setGroupHours] = useState<
    Record<DayGroup, { openTime: string; closeTime: string }>
  >(() => {
    // Initialize with the first day's hours from each group that is open
    // or default hours if none are open
    const getGroupTime = (
      group: DayGroup
    ): { openTime: string; closeTime: string } => {
      const openDay = DAY_GROUPS[group].days.find((day) => hours[day].isOpen);
      if (openDay) {
        return {
          openTime: hours[openDay].openTime,
          closeTime: hours[openDay].closeTime,
        };
      }
      return { openTime: "09:00", closeTime: "18:00" };
    };

    return {
      weekdays: getGroupTime("weekdays"),
      weekend: getGroupTime("weekend"),
      all: getGroupTime("all"),
    };
  });

  // Update hours for a specific group
  const updateGroupHours = (
    group: DayGroup,
    isOpenValue: boolean,
    openTime?: string,
    closeTime?: string
  ) => {
    // Update the group's open status
    setIsOpen((prev) => ({
      ...prev,
      [group]: isOpenValue,
    }));

    // Update the group's hours if provided
    if (openTime !== undefined && closeTime !== undefined) {
      setGroupHours((prev) => ({
        ...prev,
        [group]: { openTime, closeTime },
      }));
    }

    // Apply changes to all days in the group
    const newHours = { ...hours };
    DAY_GROUPS[group].days.forEach((day) => {
      newHours[day] = {
        isOpen: isOpenValue,
        openTime: openTime || groupHours[group].openTime,
        closeTime: closeTime || groupHours[group].closeTime,
      };
    });

    // Update state and notify parent
    setHours(newHours);
    onChange(newHours);
  };

  // Reset all hours to empty (all days closed)
  const resetAll = () => {
    setHours({ ...EMPTY_BUSINESS_HOURS });
    setIsOpen({ weekdays: false, weekend: false, all: false });
    setGroupHours({
      weekdays: { openTime: "09:00", closeTime: "18:00" },
      weekend: { openTime: "09:00", closeTime: "18:00" },
      all: { openTime: "09:00", closeTime: "18:00" },
    });
    onChange({ ...EMPTY_BUSINESS_HOURS });
  };

  return (
    <div className="border border-neutral-200 dark:border-neutral-700 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 overflow-hidden">
      <div>
        <div className="flex items-center justify-between px-4 py-3 border-b border-neutral-200 dark:border-neutral-700">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-neutral-500" />
            <h3 className="text-sm font-medium">Business Hours</h3>
          </div>
        </div>

        <div className="p-4 space-y-4">
          <div className="space-y-4">
            {/* Weekdays section */}
            <div className="rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden">
              <div className="flex items-center justify-between p-3 bg-neutral-100 dark:bg-neutral-800">
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="weekdays-open"
                    checked={isOpen.weekdays}
                    onCheckedChange={(checked) => {
                      updateGroupHours("weekdays", checked === true);
                    }}
                  />
                  <Label
                    htmlFor="weekdays-open"
                    className="font-medium cursor-pointer"
                  >
                    {DAY_GROUPS.weekdays.label}
                  </Label>
                </div>
              </div>

              {isOpen.weekdays && (
                <div className="p-3 bg-white dark:bg-neutral-900">
                  <div className="flex flex-wrap items-center gap-2">
                    <Label className="text-xs text-neutral-500 w-full sm:w-auto">
                      Hours:
                    </Label>
                    <div className="flex items-center gap-2 flex-1 flex-wrap">
                      <Input
                        type="time"
                        value={groupHours.weekdays.openTime}
                        onChange={(e) => {
                          updateGroupHours(
                            "weekdays",
                            true,
                            e.target.value,
                            groupHours.weekdays.closeTime
                          );
                        }}
                        className="w-32 py-1 px-2 text-sm"
                      />
                      <span className="text-neutral-500 text-xs">to</span>
                      <Input
                        type="time"
                        value={groupHours.weekdays.closeTime}
                        onChange={(e) => {
                          updateGroupHours(
                            "weekdays",
                            true,
                            groupHours.weekdays.openTime,
                            e.target.value
                          );
                        }}
                        className="w-32 py-1 px-2 text-sm"
                      />
                      <span className="text-neutral-500 text-xs ml-2">
                        {formatTimeTo12Hour(groupHours.weekdays.openTime)} -{" "}
                        {formatTimeTo12Hour(groupHours.weekdays.closeTime)}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Weekend section */}
            <div className="rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden">
              <div className="flex items-center justify-between p-3 bg-neutral-100 dark:bg-neutral-800">
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="weekend-open"
                    checked={isOpen.weekend}
                    onCheckedChange={(checked) => {
                      updateGroupHours("weekend", checked === true);
                    }}
                  />
                  <Label
                    htmlFor="weekend-open"
                    className="font-medium cursor-pointer"
                  >
                    {DAY_GROUPS.weekend.label}
                  </Label>
                </div>
              </div>

              {isOpen.weekend && (
                <div className="p-3 bg-white dark:bg-neutral-900">
                  <div className="flex flex-wrap items-center gap-2">
                    <Label className="text-xs text-neutral-500 w-full sm:w-auto">
                      Hours:
                    </Label>
                    <div className="flex items-center gap-2 flex-1 flex-wrap">
                      <Input
                        type="time"
                        value={groupHours.weekend.openTime}
                        onChange={(e) => {
                          updateGroupHours(
                            "weekend",
                            true,
                            e.target.value,
                            groupHours.weekend.closeTime
                          );
                        }}
                        className="w-32 py-1 px-2 text-sm"
                      />
                      <span className="text-neutral-500 text-xs">to</span>
                      <Input
                        type="time"
                        value={groupHours.weekend.closeTime}
                        onChange={(e) => {
                          updateGroupHours(
                            "weekend",
                            true,
                            groupHours.weekend.openTime,
                            e.target.value
                          );
                        }}
                        className="w-32 py-1 px-2 text-sm"
                      />
                      <span className="text-neutral-500 text-xs ml-2">
                        {formatTimeTo12Hour(groupHours.weekend.openTime)} -{" "}
                        {formatTimeTo12Hour(groupHours.weekend.closeTime)}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Reset button */}
      <div className="flex justify-end p-4 border-t border-neutral-200 dark:border-neutral-700">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={resetAll}
          className="text-xs"
        >
          <Trash className="h-3 w-3 mr-1" />
          Reset All
        </Button>
      </div>
    </div>
  );
}
