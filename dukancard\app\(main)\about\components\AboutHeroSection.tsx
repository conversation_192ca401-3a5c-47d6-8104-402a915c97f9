"use client";

import { useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ArrowRight, Star } from "lucide-react";
import Link from "next/link";
import AboutAnimatedBackground from "./animations/AboutAnimatedBackground";

export default function AboutHeroSection() {
  const sectionRef = useRef<HTMLDivElement>(null);

  // Scroll-based animations
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"],
  });

  // Parallax effect values
  const y = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);

  // Animation variants
  const titleVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const subtitleVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, delay: 0.2 }
    }
  };

  const buttonVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, delay: 0.4 }
    }
  };

  return (
    <section
      ref={sectionRef}
      className="relative min-h-[90vh] flex items-center justify-center py-12 sm:py-16 md:py-20 px-4 md:px-6 lg:px-8 overflow-hidden"
    >
      {/* Animated background */}
      <AboutAnimatedBackground variant="gradient" intensity="medium" />

      {/* Decorative elements - hidden on small screens */}
      <motion.div
        className="hidden sm:block absolute top-20 right-[10%] text-[var(--brand-gold)]/20 dark:text-[var(--brand-gold)]/30"
        style={{ y }}
        animate={{
          rotate: [0, 360],
          scale: [1, 1.1, 1],
        }}
        transition={{
          rotate: { duration: 20, repeat: Infinity, ease: "linear" },
          scale: { duration: 3, repeat: Infinity, repeatType: "reverse" }
        }}
      >
        <Star size={40} />
      </motion.div>

      <motion.div
        className="hidden sm:block absolute bottom-20 left-[10%] text-blue-500/20 dark:text-blue-500/30"
        style={{ y: useTransform(scrollYProgress, [0, 1], [0, -100]) }}
        animate={{
          rotate: [0, -360],
          scale: [1, 1.1, 1],
        }}
        transition={{
          rotate: { duration: 25, repeat: Infinity, ease: "linear" },
          scale: { duration: 4, repeat: Infinity, repeatType: "reverse", delay: 1 }
        }}
      >
        <Star size={32} />
      </motion.div>

      {/* Content container */}
      <motion.div
        className="relative z-10 text-center w-full max-w-4xl mx-auto px-4"
        style={{ opacity }}
      >
        <motion.h1
          variants={titleVariants}
          initial="hidden"
          animate="visible"
          className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-4 sm:mb-6"
        >
          Empowering <span className="text-[var(--brand-gold)]">Every Business</span> with Digital Excellence
        </motion.h1>

        <motion.p
          variants={subtitleVariants}
          initial="hidden"
          animate="visible"
          className="text-base sm:text-lg md:text-xl text-muted-foreground mb-6 sm:mb-8 md:mb-10 max-w-3xl mx-auto"
        >
          At Dukancard, we&apos;re on a mission to democratize digital presence. We believe every business,
          regardless of size, deserves powerful tools to thrive in the digital economy.
        </motion.p>

        <motion.div
          variants={buttonVariants}
          initial="hidden"
          animate="visible"
          className="flex flex-col sm:flex-row gap-4 justify-center w-full"
        >
          <Link href="/pricing" className="w-full sm:w-auto">
            <Button className="cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 px-4 sm:px-6 md:px-8 py-4 sm:py-5 md:py-6 rounded-full font-medium text-base sm:text-lg flex gap-2 items-center justify-center w-full sm:w-auto">
              Get Started <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
            </Button>
          </Link>
          <Link href="/contact" className="w-full sm:w-auto">
            <Button
              variant="outline"
              className="cursor-pointer border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 hover:text-[var(--brand-gold)] dark:hover:bg-[var(--brand-gold)]/10 px-4 sm:px-6 md:px-8 py-4 sm:py-5 md:py-6 rounded-full font-medium text-base sm:text-lg w-full sm:w-auto"
            >
              Contact Us
            </Button>
          </Link>
        </motion.div>
      </motion.div>
    </section>
  );
}
