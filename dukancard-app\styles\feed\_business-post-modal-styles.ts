import { StyleSheet } from 'react-native';

export const createBusinessPostModalStyles = () => {
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    backButton: {
      padding: 8,
      borderRadius: 20,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    postButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
    },
    postButtonText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '600',
    },
    content: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
      paddingHorizontal: 16,
    },
    userInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 16,
    },
    avatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
    },
    avatarPlaceholder: {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
    },
    avatarText: {
      color: '#FFFFFF',
      fontSize: 18,
      fontWeight: '600',
    },
    userName: {
      marginLeft: 12,
      fontSize: 16,
      fontWeight: '600',
    },
    textInputContainer: {
      position: 'relative',
      marginBottom: 16,
    },
    textInput: {
      minHeight: 120,
      fontSize: 16,
      lineHeight: 24,
      paddingVertical: 12,
      paddingHorizontal: 16,
      paddingBottom: 32, // Extra padding for character counter
      borderWidth: 1,
      borderRadius: 12,
    },
    characterCounter: {
      position: 'absolute',
      bottom: 8,
      right: 12,
    },
    characterCountText: {
      fontSize: 12,
      fontWeight: '500',
    },
    imageContainer: {
      position: 'relative',
      marginBottom: 16,
      marginHorizontal: -16, // Extend to full width by negating ScrollView padding
    },
    imagePreview: {
      width: '100%',
      minHeight: 200, // Ensure minimum height to prevent square default
      borderRadius: 12,
      resizeMode: 'contain', // Don't crop, show full image in original dimensions
    },
    removeImageButton: {
      position: 'absolute',
      top: 8,
      right: 8,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      borderRadius: 16,
      padding: 6,
    },
    productSelectorContainer: {
      marginBottom: 16,
    },
    bottomActions: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderTopWidth: 1,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 12,
      flex: 1,
      justifyContent: 'center',
      marginHorizontal: 8,
    },
    actionButtonText: {
      marginLeft: 8,
      fontSize: 14,
      fontWeight: '500',
    },
    selectedProductsContainer: {
      marginTop: 16,
    },
    selectedProductsHeader: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 12,
    },
    selectedProductItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      marginBottom: 8,
    },
    selectedProductImage: {
      width: 40,
      height: 40,
      borderRadius: 6,
      marginRight: 12,
    },
    selectedProductDetails: {
      flex: 1,
    },
    selectedProductName: {
      fontSize: 14,
      fontWeight: '500',
      marginBottom: 4,
    },
    selectedProductPriceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    selectedProductDiscountedPrice: {
      fontSize: 13,
      fontWeight: '600',
      marginRight: 8,
    },
    selectedProductOriginalPrice: {
      fontSize: 12,
      textDecorationLine: 'line-through',
    },
    selectedProductPrice: {
      fontSize: 13,
      fontWeight: '500',
    },
    removeProductButton: {
      padding: 4,
      marginLeft: 8,
    },
  });
};
