"use client";

import { Card } from "@/components/ui/card";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";

interface AnimatedAuthCardProps {
  children: React.ReactNode;
  className?: string;
}

export default function AnimatedAuthCard({
  children,
  className = "",
}: AnimatedAuthCardProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <motion.div
      className="relative"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Card glow effect on hover */}
      {isClient && (
        <motion.div
          className="absolute -inset-1 rounded-2xl bg-gradient-to-r from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/30 blur-lg"
          initial={{ opacity: 0.3 }}
          animate={{ opacity: 0.6 }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
      )}

      <Card
        className={`bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-4 sm:p-6 md:p-8 rounded-xl sm:rounded-2xl shadow-lg dark:shadow-[var(--brand-gold)]/10 relative overflow-hidden ${className}`}
      >
        {/* Subtle shimmer effect */}
        {isClient && (
          <motion.div
            className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 dark:via-white/5 to-transparent pointer-events-none"
            initial={{ x: "-100%" }}
            animate={{ x: "100%" }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        )}

        {children}
      </Card>
    </motion.div>
  );
}
