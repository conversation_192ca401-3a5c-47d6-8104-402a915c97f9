"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
// Alert components not used in this file
import { ArrowRight, Crown, ShieldCheck } from "lucide-react";
import Link from "next/link";
import { PricingPlan } from "@/lib/PricingPlans";
import FlipTimer from "./FlipTimer";
import { cn } from "@/lib/utils";
import { SUBSCRIPTION_STATUS } from "@/lib/razorpay/webhooks/handlers/utils";

interface AnimatedSubscriptionStatusProps {
  subscriptionStatus: "active" | "trial" | "inactive";
  planDetails?: PricingPlan;
  trialEndDate?: string | null;
  planCycle?: string | null;
  subscription?: {
    subscription_status?: string | null;
    plan_id?: string | null;
    plan_cycle?: string | null;
  } | null;
}

export default function AnimatedSubscriptionStatus({
  subscriptionStatus,
  planDetails,
  trialEndDate,
  planCycle,
  subscription,
}: AnimatedSubscriptionStatusProps) {

  // Get plan icon based on plan ID - used in badge rendering
  const getPlanIcon = () => {
    if (!planDetails) return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;

    switch (planDetails.id) {
      case "premium":
      case "enterprise":
        return <Crown className="w-3.5 h-3.5 mr-1" />;
      case "pro":
        return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;
      case "growth":
        return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;
      case "basic":
        return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;
      case "free":
        return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;
      default:
        return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;
    }
  };

  // Get badge styles based on plan ID
  const getPlanBadgeStyles = () => {
    if (!planDetails) return "";

    switch (planDetails.id) {
      case "premium":
      case "enterprise":
        return "border-[var(--brand-gold)]/50 text-[var(--brand-gold)]";
      case "pro":
        return "border-blue-500/50 text-blue-500 dark:border-blue-400/50 dark:text-blue-400";
      case "growth":
        return "border-green-500/50 text-green-500 dark:border-green-400/50 dark:text-green-400";
      case "basic":
        return "border-purple-500/50 text-purple-500 dark:border-purple-400/50 dark:text-purple-400";
      case "free":
        return "border-neutral-500/50 text-neutral-500 dark:border-neutral-400/50 dark:text-neutral-400";
      default:
        return "border-green-500/50 text-green-500 dark:border-green-400/50 dark:text-green-400";
    }
  };

  return (
    <div className="h-full">
      <div
        className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-2 xs:p-3 sm:p-4 md:p-5 transition-all duration-300 hover:shadow-lg overflow-hidden h-full flex flex-col relative"
      >

        <div className="relative z-10 flex flex-col h-full">
          <div className="flex items-center justify-between gap-2 mb-4 pb-3 border-b border-neutral-100 dark:border-neutral-800">
            <div className="flex items-center gap-2">
              <div className="p-1.5 sm:p-2 rounded-lg bg-primary/10 text-primary">
                <ShieldCheck className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
              </div>
              <h3 className="text-sm sm:text-base font-semibold text-neutral-800 dark:text-neutral-100 truncate">
                Subscription Status
              </h3>
            </div>
            {subscriptionStatus === "active" && (
              <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse" />
            )}
            {subscriptionStatus === "trial" && (
              <div className="w-3 h-3 rounded-full bg-amber-500 animate-pulse" />
            )}
          </div>

          <div className="flex-1 flex flex-col justify-center">
            {subscriptionStatus === "active" && planDetails && (
              <div className="flex flex-col items-center justify-center text-center">
                <div>
                  <Badge
                    variant="outline"
                    className={cn("border px-2 py-1 mb-2", getPlanBadgeStyles())}
                  >
                    {getPlanIcon()}
                    {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? "Paused" : "Active"}
                  </Badge>
                </div>

                <h3 className="text-base sm:text-lg font-medium mb-1">
                  {planDetails.name}
                </h3>
                {/* Only show plan cycle for paid plans, not for free plan */}
                {planDetails.id !== "free" && (
                  <p className="text-xs sm:text-sm font-medium text-primary mb-1">
                    {planCycle === "yearly" ? "Yearly Plan" : "Monthly Plan"}
                  </p>
                )}

                {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? (
                  <p className="text-xs sm:text-sm text-amber-600 dark:text-amber-400 max-w-[250px] mx-auto px-2 mb-3">
                    Your subscription is currently paused. Your business card is offline until you resume your subscription.
                  </p>
                ) : (
                  <p className="text-xs sm:text-sm text-muted-foreground max-w-[250px] mx-auto px-2 mb-3">
                    Your subscription is active and all features are enabled
                  </p>
                )}

                <div className="mt-2 w-full px-2 mb-2">
                  <Button asChild size="sm" variant="outline" className="w-full sm:w-auto">
                    <Link href="/dashboard/business/plan" className="flex items-center justify-center gap-1">
                      <span className="whitespace-nowrap">Manage Plan</span> <ArrowRight className="w-3.5 h-3.5 flex-shrink-0" />
                    </Link>
                  </Button>
                </div>
              </div>
            )}

            {subscriptionStatus === "trial" && trialEndDate && (
              <div className="flex flex-col items-center justify-center text-center">
                <div>
                  <Badge
                    variant="outline"
                    className="border border-[var(--brand-gold)]/50 text-[var(--brand-gold)] px-2 py-1 mb-2"
                  >
                    <Crown className="w-3.5 h-3.5 mr-1" />
                    Trial
                  </Badge>
                </div>

                <h3 className="text-base sm:text-lg font-medium mb-1">
                  {planDetails ? planDetails.name : "Premium"} Trial
                </h3>
                {/* Only show plan cycle for paid plans, not for free plan */}
                {planDetails && planDetails.id !== "free" && (
                  <p className="text-xs sm:text-sm font-medium text-[var(--brand-gold)] mb-2">
                    {planCycle === "yearly" ? "Yearly Plan" : "Monthly Plan"}
                  </p>
                )}

                <div className="w-full overflow-x-auto px-1">
                  <FlipTimer endDate={trialEndDate} label="" tooltipText="Your trial will expire soon. Choose a plan to continue using all features." />
                </div>

                <div className="mt-4 w-full px-2 mb-2">
                  <div className="relative group">
                    {/* Strong inner glow effect that fills only the button */}
                    <div className="absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none" />

                    {/* Border glow effect - matches button size */}
                    <div
                      className="absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300"
                      style={{
                        boxShadow: `inset 0 0 20px rgba(194, 157, 91, 0.2), 0 0 20px rgba(194, 157, 91, 0.3)`
                      }}
                    />

                    {/* Strong decorative colored glow elements - positioned relative to button */}
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none" />
                    <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none" />

                    <Button
                      asChild
                      variant="outline"
                      size="sm"
                      className="gap-1 w-full sm:w-auto border-amber-200/50 dark:border-amber-700/50 shadow-amber-500/40 shadow-lg hover:shadow-xl hover:shadow-amber-500/40 transition-all duration-300 text-amber-500 dark:text-amber-400 hover:bg-amber-500/5 dark:hover:bg-amber-500/10 rounded-xl"
                    >
                      <Link href="/dashboard/business/plan" className="flex items-center justify-center relative">
                        <span className="whitespace-nowrap">Choose a Plan</span> <ArrowRight className="w-3.5 h-3.5 ml-1 flex-shrink-0" />

                        {/* Shimmer effect - only on hover */}
                        <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100 group-hover:animate-[shimmer_0.6s_ease-in-out] transition-opacity duration-300" />
                      </Link>
                    </Button>
                  </div>
                </div>


              </div>
            )}

            {subscriptionStatus === "inactive" && (
              <div className="flex flex-col items-center justify-center text-center">
                <div>
                  <Badge
                    variant="outline"
                    className={cn("border px-2 py-1 mb-2",
                      subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails
                        ? getPlanBadgeStyles()
                        : "border-neutral-500/50 text-neutral-500 dark:border-neutral-400/50 dark:text-neutral-400"
                    )}
                  >
                    {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails
                      ? getPlanIcon()
                      : <ShieldCheck className="w-3.5 h-3.5 mr-1" />
                    }
                    {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? "Paused" : "Free Plan"}
                  </Badge>
                </div>

                <h3 className="text-base sm:text-lg font-medium mb-1">
                  {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails
                    ? planDetails.name
                    : "Free Plan"
                  }
                </h3>

                {/* Only show plan cycle for paid plans with halted subscription */}
                {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails && planDetails.id !== "free" && (
                  <p className="text-xs sm:text-sm font-medium text-primary mb-1">
                    {planCycle === "yearly" ? "Yearly Plan" : "Monthly Plan"}
                  </p>
                )}

                {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? (
                  <p className="text-xs sm:text-sm text-amber-600 dark:text-amber-400 max-w-[250px] mx-auto px-2 mb-3">
                    Your subscription is currently paused. Your business card is offline until you resume your subscription.
                  </p>
                ) : (
                  <p className="text-xs sm:text-sm text-muted-foreground max-w-[250px] mx-auto px-2 mb-3">
                    You are on the free plan with limited features. Upgrade to access more features.
                  </p>
                )}

                <div className="mt-2 w-full px-2 mb-2">
                  <div className="relative group">
                    {/* Strong inner glow effect that fills only the button */}
                    <div className="absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none" />

                    {/* Border glow effect - matches button size */}
                    <div
                      className="absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300"
                      style={{
                        boxShadow: `inset 0 0 20px rgba(194, 157, 91, 0.2), 0 0 20px rgba(194, 157, 91, 0.3)`
                      }}
                    />

                    {/* Strong decorative colored glow elements - positioned relative to button */}
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none" />
                    <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none" />

                    <Button
                      asChild
                      variant="outline"
                      size="sm"
                      className="w-full sm:w-auto border-amber-200/50 dark:border-amber-700/50 shadow-amber-500/40 shadow-lg hover:shadow-xl hover:shadow-amber-500/40 transition-all duration-300 text-amber-500 dark:text-amber-400 hover:bg-amber-500/5 dark:hover:bg-amber-500/10 rounded-xl"
                    >
                      <Link href="/dashboard/business/plan" className="flex items-center justify-center gap-1 relative">
                        <span className="whitespace-nowrap">
                          {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? "Manage Plan" : "Upgrade Plan"}
                        </span>
                        <ArrowRight className="w-3.5 h-3.5 flex-shrink-0" />

                        {/* Shimmer effect - only on hover */}
                        <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100 group-hover:animate-[shimmer_0.6s_ease-in-out] transition-opacity duration-300" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
