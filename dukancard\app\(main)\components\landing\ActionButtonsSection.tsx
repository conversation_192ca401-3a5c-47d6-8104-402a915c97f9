"use client";

import { useRef } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Globe, Store } from "lucide-react";

export default function ActionButtonsSection() {
  const sectionRef = useRef<HTMLDivElement>(null);

  return (
    <div ref={sectionRef} className="relative w-full py-12 md:py-16 overflow-hidden">
      {/* Simple background */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        {/* Static gradient background */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[var(--brand-gold)]/5 to-transparent dark:via-[var(--brand-gold)]/10"></div>
      </div>

      <div className="w-full max-w-4xl mx-auto py-8 px-4">
        <div className="text-center mb-10">
          <div className="relative inline-block">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-foreground mb-4">
              Ready to <span className="text-[var(--brand-gold)] relative">Get Started</span>?
            </h2>
          </div>

          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Create your own digital business card or explore businesses in your area
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-3xl mx-auto">
          {/* Create Your Presence Card */}
          <div className="bg-gradient-to-br from-white to-[var(--brand-gold)]/5 dark:from-neutral-900 dark:to-[var(--brand-gold)]/10 rounded-xl border border-[var(--brand-gold)]/20 dark:border-[var(--brand-gold)]/30 p-8 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group">
            <div className="flex flex-col items-center text-center relative z-10">
              <div className="p-4 bg-[var(--brand-gold)]/10 rounded-full mb-5 relative">
                <Store className="h-10 w-10 text-[var(--brand-gold)]" />
              </div>

              <h3 className="text-2xl font-bold mb-3">
                Create Your Presence
              </h3>

              <p className="text-muted-foreground mb-8 text-base">
                Build your digital business card and showcase your products and services online
              </p>

              <Link href="/login" className="w-full">
                <Button className="w-full bg-gradient-to-r from-[var(--brand-gold)] to-amber-500 hover:from-[var(--brand-gold-light)] hover:to-amber-600 text-[var(--brand-gold-foreground)] py-6 text-lg font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden group">
                  <span className="relative z-10">Get Started</span>
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>

          {/* Discover Business Nearby Card */}
          <div className="bg-gradient-to-br from-white to-blue-500/5 dark:from-neutral-900 dark:to-blue-500/10 rounded-xl border border-blue-500/20 dark:border-blue-500/30 p-8 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group">
            <div className="flex flex-col items-center text-center relative z-10">
              <div className="p-4 bg-blue-500/10 rounded-full mb-5 relative">
                <Globe className="h-10 w-10 text-blue-500 dark:text-blue-400" />
              </div>

              <h3 className="text-2xl font-bold mb-3">
                Discover Business Nearby
              </h3>

              <p className="text-muted-foreground mb-8 text-base">
                Find local businesses, products, and services in your area
              </p>

              <Link href="/discover" className="w-full">
                <Button variant="outline" className="w-full border-2 border-blue-500 text-blue-500 hover:bg-blue-500/10 py-6 text-lg font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden group">
                  <span className="relative z-10">Explore Now</span>
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
