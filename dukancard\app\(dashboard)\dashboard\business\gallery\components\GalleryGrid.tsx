import { motion } from "framer-motion";
import { Image as ImageIcon, Info } from "lucide-react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from '@dnd-kit/sortable';
import Image from "next/image";
import { cn } from "@/lib/utils";
import { GalleryImage } from "../types";
import { ReorderState } from "../types/galleryTypes";
import SortableImageItem from "./SortableImageItem";
import StaticImageItem from "./StaticImageItem";
import ReorderControls from "./ReorderControls";
import EmptyGallery from "./EmptyGallery";

interface GalleryGridProps {
  images: GalleryImage[];
  imagesCount: number;
  galleryLimit: number;
  canAddMore: boolean;
  isDragging: boolean;
  userPlan: string;
  isClient: boolean;
  reorderState: ReorderState;
  activeId: string | null;
  onDragStart: (_event: DragStartEvent) => void;
  onDragEnd: (_event: DragEndEvent) => void;
  onSaveOrder: () => void;
  onResetOrder: () => void;
  onViewImage: (_url: string) => void;
  onDeleteImage: (_image: GalleryImage) => void;
  onUploadClick: () => void;
}

export default function GalleryGrid({
  images: _images,
  imagesCount,
  galleryLimit,
  canAddMore,
  isDragging,
  userPlan,
  isClient,
  reorderState,
  activeId,
  onDragStart,
  onDragEnd,
  onSaveOrder,
  onResetOrder,
  onViewImage,
  onDeleteImage,
  onUploadClick,
}: GalleryGridProps) {
  // Drag and drop sensors for reordering
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Require 8px of movement before activating drag
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  return (
    <motion.div
      className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-4 sm:p-5 transition-all duration-300 relative overflow-hidden"
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.2 }}
    >
      {/* Subtle background pattern */}
      <div
        className="absolute inset-0 pointer-events-none opacity-5"
        style={{
          backgroundImage: `url("/decorative/subtle-pattern.svg")`,
          backgroundRepeat: "repeat",
          backgroundSize: "20px 20px",
        }}
      ></div>

      {/* Content with relative positioning */}
      <div className="relative z-10">
        {/* Gallery Header */}
        <div className="flex items-center justify-between mb-6 pb-3 border-b border-neutral-100 dark:border-neutral-800">
          <div className="flex items-center gap-2">
            <div className="p-2 rounded-lg bg-amber-500/10 text-amber-500">
              <ImageIcon className="w-4 sm:w-5 h-4 sm:h-5" />
            </div>
            <h2 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
              Photo Gallery
            </h2>
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <span className="font-medium">{imagesCount}</span>
            <span className="mx-1">/</span>
            <span>{galleryLimit}</span>
            <Info className="ml-1 h-3.5 w-3.5 opacity-70" />
          </div>
        </div>

        {/* Gallery Content */}
        <div className="relative">
          {reorderState.orderedImages.length === 0 ? (
            <EmptyGallery
              canAddMore={canAddMore}
              isDragging={isDragging}
              userPlan={userPlan}
              galleryLimit={galleryLimit}
              onUploadClick={onUploadClick}
            />
          ) : (
            <div className="space-y-4">
              {/* Reorder Instructions and Controls - Only show when client-side and drag is available */}
              {reorderState.orderedImages.length > 1 && isClient && (
                <ReorderControls
                  isReordering={reorderState.isReordering}
                  hasUnsavedChanges={reorderState.hasUnsavedChanges}
                  isSavingOrder={reorderState.isSavingOrder}
                  onSaveOrder={onSaveOrder}
                  onResetOrder={onResetOrder}
                />
              )}

              {isClient ? (
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragStart={onDragStart}
                  onDragEnd={onDragEnd}
                >
                  <SortableContext
                    items={reorderState.orderedImages.map(img => img.id)}
                    strategy={rectSortingStrategy}
                  >
                    <div
                      className={cn(
                        "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-5 transition-all duration-200",
                        reorderState.isReordering && "bg-amber-500/5 rounded-lg p-2"
                      )}
                    >
                      {reorderState.orderedImages.map((image) => (
                        <SortableImageItem
                          key={image.id}
                          image={image}
                          onViewImage={onViewImage}
                          onDeleteImage={onDeleteImage}
                        />
                      ))}
                    </div>
                  </SortableContext>

                  <DragOverlay>
                    {activeId ? (
                      <div className="aspect-square relative overflow-hidden rounded-lg border shadow-lg opacity-90 transform rotate-3 scale-105">
                        {(() => {
                          const activeImage = reorderState.orderedImages.find(img => img.id === activeId);
                          return activeImage ? (
                            <Image
                              src={activeImage.url}
                              alt="Dragging image"
                              fill
                              className="object-cover"
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            />
                          ) : null;
                        })()}
                      </div>
                    ) : null}
                  </DragOverlay>
                </DndContext>
              ) : (
                // Fallback for server-side rendering - static grid without drag functionality
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-5">
                  {reorderState.orderedImages.map((image) => (
                    <StaticImageItem
                      key={image.id}
                      image={image}
                      onViewImage={onViewImage}
                      onDeleteImage={onDeleteImage}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
}
