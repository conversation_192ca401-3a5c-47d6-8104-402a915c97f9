import { useState } from "react";
import { toast } from "sonner";
import { validateFile, createPreviewUrl } from "../utils/fileValidation";
import { UploadState } from "../types/galleryTypes";

interface UseDragAndDropProps {
  canAddMore: boolean;
  userPlan: string;
  galleryLimit: number;
  updateUploadState: (_updates: Partial<UploadState>) => void;
}

export const useDragAndDrop = ({
  canAddMore,
  userPlan,
  galleryLimit,
  updateUploadState,
}: UseDragAndDropProps) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // Only set dragging to false if we're leaving the entire gallery area
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragging(false);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) {
      setIsDragging(true);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    // Check if user can add more images
    if (!canAddMore) {
      toast.error("Gallery limit reached", {
        description: `You've reached your ${userPlan} plan limit of ${galleryLimit} photos. Please upgrade to add more.`,
      });
      return;
    }

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0]; // Take only the first file
      if (validateFile(file)) {
        updateUploadState({
          selectedFile: file,
          previewUrl: createPreviewUrl(file),
          uploadDialogOpen: true,
        });
      }
    }
  };

  const handleFileSelect = (file: File) => {
    if (validateFile(file)) {
      updateUploadState({
        selectedFile: file,
        previewUrl: createPreviewUrl(file),
      });
    }
  };

  return {
    isDragging,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
    handleFileSelect,
  };
};
