import { useState, useEffect } from "react";
import { GalleryImage } from "../types";
import { UploadState, DeleteState, LightboxState } from "../types/galleryTypes";

export const useGalleryState = (initialImages: GalleryImage[]) => {
  const [images, setImages] = useState<GalleryImage[]>(initialImages);
  const [refreshKey, setRefreshKey] = useState(0);
  const [isClient, setIsClient] = useState(false);

  // Upload state
  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    selectedFile: null,
    previewUrl: null,
    uploadDialogOpen: false,
  });

  // Delete state
  const [deleteState, setDeleteState] = useState<DeleteState>({
    isDeleting: false,
    selectedImage: null,
    deleteDialogOpen: false,
  });

  // Lightbox state
  const [lightboxState, setLightboxState] = useState<LightboxState>({
    lightboxImage: null,
  });

  // Set client-side flag to prevent hydration issues
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Clear preview when upload dialog closes
  useEffect(() => {
    if (!uploadState.uploadDialogOpen) {
      setUploadState(prev => ({
        ...prev,
        selectedFile: null,
        previewUrl: null,
      }));
    }
  }, [uploadState.uploadDialogOpen]);

  const updateImages = (newImages: GalleryImage[]) => {
    setImages(newImages);
    setRefreshKey(prev => prev + 1);
  };

  const updateUploadState = (updates: Partial<UploadState>) => {
    setUploadState(prev => ({ ...prev, ...updates }));
  };

  const updateDeleteState = (updates: Partial<DeleteState>) => {
    setDeleteState(prev => ({ ...prev, ...updates }));
  };

  const updateLightboxState = (updates: Partial<LightboxState>) => {
    setLightboxState(prev => ({ ...prev, ...updates }));
  };

  return {
    images,
    refreshKey,
    isClient,
    uploadState,
    deleteState,
    lightboxState,
    updateImages,
    updateUploadState,
    updateDeleteState,
    updateLightboxState,
  };
};
