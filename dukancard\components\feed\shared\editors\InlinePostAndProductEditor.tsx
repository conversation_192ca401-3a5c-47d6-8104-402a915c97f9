"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Check, X, Package } from "lucide-react";
import { toast } from "sonner";
import { updatePost } from "@/lib/actions/posts";
import ProductSelector from "@/components/feed/shared/forms/ProductSelector";
import { motion, AnimatePresence } from "framer-motion";

export interface InlinePostAndProductEditorProps {
  postId: string;
  initialContent: string;
  initialProductIds: string[];
  initialImageUrl?: string | null;
  onSave?: (newContent: string, newProductIds: string[]) => void;
  onCancel?: () => void;
  className?: string;
}

export default function InlinePostAndProductEditor({
  postId,
  initialContent,
  initialProductIds,
  initialImageUrl,
  onSave,
  onCancel,
  className = "",
}: InlinePostAndProductEditorProps) {
  const [content, setContent] = useState(initialContent);
  const [productIds, setProductIds] = useState<string[]>(initialProductIds);
  const [isLoading, setIsLoading] = useState(false);
  const [charCount, setCharCount] = useState(initialContent.length);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const maxChars = 2000;
  const isOverLimit = charCount > maxChars;
  const hasContentChanges = content.trim() !== initialContent.trim();
  const hasProductChanges = JSON.stringify(productIds.sort()) !== JSON.stringify(initialProductIds.sort());
  const hasChanges = hasContentChanges || hasProductChanges;

  // Focus textarea when component mounts
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
      // Set cursor to end of text
      textareaRef.current.setSelectionRange(content.length, content.length);
    }
  }, [content.length]);

  // Handle content change
  const handleContentChange = (value: string) => {
    setContent(value);
    setCharCount(value.length);
  };

  // Handle save
  const handleSave = async () => {
    if (!hasChanges) {
      onCancel?.();
      return;
    }

    if (isOverLimit) {
      toast.error("Content too long", {
        description: `Please reduce content to ${maxChars} characters or less.`
      });
      return;
    }

    if (content.trim().length === 0 && productIds.length === 0) {
      toast.error("Content or products required", {
        description: "Post must have either content or linked products."
      });
      return;
    }

    setIsLoading(true);

    try {
      const result = await updatePost(postId, {
        content: content.trim(),
        product_ids: productIds,
        image_url: initialImageUrl,
        mentioned_business_ids: []
      });

      if (result.success) {
        toast.success("Post updated successfully");
        onSave?.(content.trim(), productIds);
      } else {
        toast.error("Failed to update post", {
          description: result.error || "Please try again."
        });
      }
    } catch (error) {
      console.error("Error updating post:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setContent(initialContent);
    setCharCount(initialContent.length);
    setProductIds(initialProductIds);
    onCancel?.();
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel();
    } else if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSave();
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Content Editor */}
      <div className="space-y-3">
        <div className="relative">
          <Textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => handleContentChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="What's on your mind?"
            className={`min-h-[100px] resize-none ${
              isOverLimit ? "border-destructive focus:border-destructive" : ""
            }`}
            disabled={isLoading}
          />

          {/* Character count */}
          <div className={`absolute bottom-2 right-2 text-xs ${
            isOverLimit ? "text-destructive" : "text-muted-foreground"
          }`}>
            {charCount}/{maxChars}
          </div>
        </div>
      </div>

      {/* Product Selector */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium text-muted-foreground">
            Linked Products ({productIds.length})
          </span>
        </div>
        <ProductSelector
          selectedProductIds={productIds}
          onProductsChange={setProductIds}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-end gap-2 pt-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          disabled={isLoading}
          className="text-muted-foreground hover:text-foreground"
        >
          <X className="h-4 w-4 mr-1" />
          Cancel
        </Button>

        <motion.div
          whileHover={{ scale: hasChanges && !isLoading ? 1.02 : 1 }}
          whileTap={{ scale: hasChanges && !isLoading ? 0.98 : 1 }}
        >
          <Button
            size="sm"
            onClick={handleSave}
            disabled={!hasChanges || isOverLimit || isLoading}
            className="relative overflow-hidden"
            style={{
              background: hasChanges && !isLoading
                ? 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
                : undefined,
              boxShadow: hasChanges && !isLoading
                ? '0 4px 20px rgba(59, 130, 246, 0.3)'
                : '0 4px 20px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)'
            }}
          >
            <AnimatePresence mode="wait">
              {isLoading ? (
                <motion.div
                  key="saving"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 10 }}
                  className="flex items-center justify-center"
                >
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  Saving...
                </motion.div>
              ) : (
                <motion.div
                  key="save"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 10 }}
                  className="flex items-center justify-center"
                >
                  <Check className="h-4 w-4 mr-1" />
                  Save
                </motion.div>
              )}
            </AnimatePresence>
          </Button>
        </motion.div>
      </div>
    </div>
  );
}
