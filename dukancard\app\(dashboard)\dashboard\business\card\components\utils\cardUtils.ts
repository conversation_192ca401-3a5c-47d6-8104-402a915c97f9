export const formatWhatsAppUrl = (number?: string): string | undefined => {
  if (!number) return undefined;
  const digits = number.replace(/\D/g, "");
  if (digits.length < 10) return undefined;
  const formattedNumber = digits.startsWith("91") ? digits : `91${digits}`;
  return `https://wa.me/${formattedNumber}`;
};

export const formatTelUrl = (number?: string): string | undefined => {
  if (!number) return undefined;
  const digits = number.replace(/\D/g, "");
  if (digits.length < 10) return undefined;
  return `tel:+91${digits}`;
};

export const formatPrice = (price: number | null | undefined): string => {
  if (price === null || price === undefined) return "N/A";
  return `₹${price.toLocaleString("en-IN")}`;
};

export const formatTimeTo12Hour = (time24: string): string => {
  if (!time24 || time24.length < 5) return time24;

  const [hourStr, minuteStr] = time24.split(":");
  const hour = parseInt(hourStr, 10);

  if (isNaN(hour)) return time24;

  const period = hour >= 12 ? "PM" : "AM";
  const hour12 = hour % 12 || 12;
  return `${hour12}:${minuteStr} ${period}`;
};

export const formatDayGroup = (days: string[]): string => {
  const dayAbbreviations: Record<string, string> = {
    monday: "Mon",
    tuesday: "Tue",
    wednesday: "Wed",
    thursday: "Thu",
    friday: "Fri",
    saturday: "Sat",
    sunday: "Sun",
  };

  const dayOrder = [
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
    "sunday",
  ];
  const sortedDays = [...days].sort(
    (a, b) => dayOrder.indexOf(a) - dayOrder.indexOf(b)
  );

  const shortDays = sortedDays.map((day) => dayAbbreviations[day] || day);

  const weekdays = ["monday", "tuesday", "wednesday", "thursday", "friday"];
  if (
    sortedDays.length === 5 &&
    weekdays.every((day) => sortedDays.includes(day))
  ) {
    return "Mon-Fri";
  }

  if (
    sortedDays.length === 2 &&
    sortedDays.includes("saturday") &&
    sortedDays.includes("sunday")
  ) {
    return "Sat-Sun";
  }

  if (sortedDays.length === 7) {
    return "All days";
  }

  if (isConsecutive(sortedDays, dayOrder)) {
    return `${shortDays[0]}-${shortDays[shortDays.length - 1]}`;
  }

  return shortDays.join(", ");
};

export const isConsecutive = (days: string[], dayOrder: string[]): boolean => {
  if (days.length <= 1) return true;

  const indices = days
    .map((day) => dayOrder.indexOf(day))
    .sort((a, b) => a - b);

  for (let i = 1; i < indices.length; i++) {
    if (indices[i] !== indices[i - 1] + 1) {
      return false;
    }
  }

  return true;
};

// Generate card style with theme colors
export const generateCardStyle = (
  finalThemeColor: string
): React.CSSProperties => {
  return {
    "--theme-color": finalThemeColor,
    "--theme-color-80": `${finalThemeColor}CC`,
    "--theme-color-50": `${finalThemeColor}80`,
    "--theme-color-30": `${finalThemeColor}4D`,
    "--theme-color-20": `${finalThemeColor}33`,
    "--theme-color-10": `${finalThemeColor}1A`,
    "--theme-color-5": `${finalThemeColor}0D`,
    "--theme-accent-end": "#E5C76E", // Less yellow accent
  } as React.CSSProperties;
};