"use server";

import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { revalidatePath } from "next/cache";
import { getScalableUserPath } from "@/lib/utils/storage-paths";

// Delete a product/service
export async function deleteProductService(
  itemId: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user)
    return { success: false, error: "User not authenticated." };
  if (!itemId) return { success: false, error: "Item ID is required." };

  // Get the product to find all associated images and the product name
  const { data: product } = await supabase
    .from("products_services")
    .select("images, name")
    .eq("id", itemId)
    .eq("business_id", user.id)
    .single();

  if (!product) {
    return { success: false, error: "Product not found." };
  }

  // Delete all associated images from storage using admin client
  const bucketName = "business";
  const adminSupabase = createAdminClient();

  try {
    // First, try to delete images based on URLs in the product record
    if (product.images && Array.isArray(product.images) && product.images.length > 0) {
      for (const imageUrl of product.images) {
        if (imageUrl) {
          try {
            // Extract the storage path from the URL
            const url = new URL(imageUrl);
            const pathParts = url.pathname.split('/');

            // The path will be in format like /storage/v1/object/public/business/users/xx/xx/userId/products/productId/image_0.webp
            // We need to extract the part after 'business/'
            const businessIndex = pathParts.findIndex(part => part === 'business');

            if (businessIndex !== -1 && businessIndex < pathParts.length - 1) {
              // Extract the path after 'business/'
              const storagePath = pathParts.slice(businessIndex + 1).join('/').split('?')[0];

              // Delete the file from storage using admin client
              await adminSupabase.storage
                .from(bucketName)
                .remove([storagePath]);

              console.log(`Attempted to delete image from URL: ${storagePath}`);
            }
          } catch (error) {
            console.error(`Error processing image URL for deletion: ${imageUrl}`, error);
          }
        }
      }
    }

    // Then, list all files in the user's products directory to catch any that might have been missed
    // Use the scalable user path utility for consistency
    const userPath = getScalableUserPath(user.id);
    const { data: fileList, error: listError } = await adminSupabase.storage
      .from(bucketName)
      .list(`${userPath}/products`);

    if (listError) {
      console.error("Error listing product files:", listError);
    } else if (fileList) {
      // Find all folders and files related to this product
      const filesToDelete: string[] = [];

      // Find the product folder (now just the productId)
      for (const item of fileList) {
        if (item.name === itemId) {
          // This is the product folder - list all files inside it
          const { data: productFiles, error: productFilesError } = await adminSupabase.storage
            .from(bucketName)
            .list(`${userPath}/products/${item.name}`);

          if (productFilesError) {
            console.error(`Error listing files in product folder ${item.name}:`, productFilesError);
          } else if (productFiles) {
            // Add all files in the product folder to the delete list
            for (const file of productFiles) {
              filesToDelete.push(`${userPath}/products/${item.name}/${file.name}`);
            }
          }
        }
      }

      // Delete all files
      if (filesToDelete.length > 0) {
        // Delete in batches of 100 to avoid API limits
        for (let i = 0; i < filesToDelete.length; i += 100) {
          const batch = filesToDelete.slice(i, i + 100);
          const { error: deleteError } = await adminSupabase.storage
            .from(bucketName)
            .remove(batch);

          if (deleteError && deleteError.message !== "The resource was not found") {
            console.warn(`Could not delete some product files (batch ${i}): ${deleteError.message}`);
          } else {
            console.log(`Successfully deleted batch ${i} of product files`);
          }
        }
      }
    }
  } catch (error) {
    console.error("Error handling product file deletion:", error);
  }

  // Delete from DB
  const { error: deleteError } = await supabase
    .from("products_services")
    .delete()
    .eq("id", itemId)
    .eq("business_id", user.id);
  if (deleteError) {
    console.error("Delete Product Error:", deleteError);
    return {
      success: false,
      error: `Failed to delete product/service: ${deleteError.message}`,
    };
  }

  revalidatePath("/dashboard/business/products");

  return { success: true };
}