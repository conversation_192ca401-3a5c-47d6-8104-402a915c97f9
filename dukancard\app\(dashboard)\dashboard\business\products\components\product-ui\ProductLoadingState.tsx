"use client";

import { Skeleton } from "@/components/ui/skeleton";

interface ProductLoadingStateProps {
  view: "table" | "grid";
}

export default function ProductLoadingState({ view }: ProductLoadingStateProps) {
  if (view === "table") {
    return (
      <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 overflow-hidden shadow-md">
        <div className="p-3 sm:p-4">
          <Skeleton className="h-10 w-full rounded-lg" />
          <div className="space-y-2 mt-2">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full rounded-lg" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
      {[...Array(8)].map((_, i) => (
        <div key={i} className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 overflow-hidden shadow-md p-2 md:p-3">
          <div className="relative w-full overflow-hidden rounded-xl border">
            {/* Image skeleton */}
            <Skeleton className="w-full aspect-square rounded-t-xl" />

            {/* Content skeleton */}
            <div className="px-3 pt-2 pb-3 space-y-2">
              {/* Title skeleton */}
              <Skeleton className="h-5 w-3/4" />

              {/* Description skeleton */}
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-2/3" />

              {/* Price and badge skeleton */}
              <div className="flex justify-between items-center gap-2 pt-1">
                <div className="flex items-baseline space-x-2">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-3 w-10" />
                </div>
                <Skeleton className="h-5 w-14 rounded-full" />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
