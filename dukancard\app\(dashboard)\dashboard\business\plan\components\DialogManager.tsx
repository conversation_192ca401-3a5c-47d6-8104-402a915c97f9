"use client";


import SimplifiedPlanActionDialog from "./SimplifiedPlanActionDialog";
import FirstTimePaidPlanDialog from "./FirstTimePaidPlanDialog";

import { PricingPlan } from "@/lib/PricingPlans";
import { SubscriptionStatus } from "../page";


interface DialogManagerProps {
  dialogPlan: PricingPlan | null;
  isPlanDialogOpen: boolean;
  isFirstTimePaidPlanDialogOpen: boolean;
  dialogLoading: boolean;
  billingCycle: "monthly" | "yearly";
  subscriptionStatus: SubscriptionStatus;
  trialEndDate: string | null;
  razorpaySubscriptionId?: string | null;

  // Setters
  setIsPlanDialogOpen: (_open: boolean) => void;
  setIsFirstTimePaidPlanDialogOpen: (_open: boolean) => void;
  setDialogLoading: (_loading: boolean) => void;

  // Functions
  handleDialogSubscribe: () => Promise<void>;
  handleActivateTrial: () => Promise<void>;
  resetProcessing: () => void;
}

export default function DialogManager({
  dialogPlan,
  isPlanDialogOpen,
  isFirstTimePaidPlanDialogOpen,
  dialogLoading,
  billingCycle,
  subscriptionStatus,
  trialEndDate,
  razorpaySubscriptionId,
  setIsPlanDialogOpen,
  setIsFirstTimePaidPlanDialogOpen,
  setDialogLoading,
  handleDialogSubscribe,
  handleActivateTrial,
  resetProcessing,
}: DialogManagerProps) {

  // CRITICAL FIX: Add debug logging for dialog state management
  console.log('[DIALOG_MANAGER] Dialog states:', {
    dialogPlan: dialogPlan?.id,
    isPlanDialogOpen,
    isFirstTimePaidPlanDialogOpen,
    dialogLoading
  });

  return (
    <>
      {/* Plan Action Dialog */}
      {dialogPlan && (
        <SimplifiedPlanActionDialog
          isOpen={isPlanDialogOpen}
          onClose={() => {
            console.log('[DIALOG_MANAGER] Closing SimplifiedPlanActionDialog');
            setIsPlanDialogOpen(false);
            setDialogLoading(false); // Reset loading state when dialog is closed
            resetProcessing(); // Also reset the processing state to ensure no lingering toast notifications
          }}
          plan={dialogPlan}
          trialEndDate={
            subscriptionStatus === "authenticated" ? null : trialEndDate
          }
          _onSubscribe={handleDialogSubscribe}
          isLoading={dialogLoading} // Pass loading state to dialog
          razorpaySubscriptionId={razorpaySubscriptionId} // Pass to detect subscription switch
        />
      )}

      {/* First Time Paid Plan Dialog */}
      {dialogPlan && (
        <FirstTimePaidPlanDialog
          isOpen={isFirstTimePaidPlanDialogOpen}
          onClose={() => {
            setIsFirstTimePaidPlanDialogOpen(false);
            setDialogLoading(false); // Reset loading state when dialog is closed
            resetProcessing(); // Also reset the processing state to ensure no lingering toast notifications
          }}
          plan={dialogPlan}
          billingCycle={billingCycle} // Pass the selected billing cycle
          onActivateTrial={handleActivateTrial}
          isLoading={dialogLoading} // Pass loading state to dialog
        />
      )}


    </>
  );
}
