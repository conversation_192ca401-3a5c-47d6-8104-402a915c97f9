"use client";

import { ReactNode, useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface PolicySectionProps {
  id: string;
  title: string;
  icon?: ReactNode;
  children: ReactNode;
  className?: string;
  delay?: number;
}

export default function PolicySection({
  id,
  title,
  icon,
  children,
  className,
  delay = 0,
}: PolicySectionProps) {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });

  // Animation variants
  const sectionVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        delay: delay * 0.1,
        ease: "easeOut",
      },
    },
  };

  const iconVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.4,
        delay: delay * 0.1 + 0.2,
        type: "spring",
        stiffness: 200,
      },
    },
  };

  const titleVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.5,
        delay: delay * 0.1 + 0.3,
      },
    },
  };

  const contentVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        delay: delay * 0.1 + 0.4,
      },
    },
  };

  return (
    <motion.div
      id={id}
      ref={sectionRef}
      className={cn("mb-8 scroll-mt-24", className)}
      variants={sectionVariants}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
    >
      <Card className="p-6 md:p-8 border border-border shadow-sm hover:shadow-md transition-shadow duration-300">
        <div className="flex items-center mb-4">
          {icon && (
            <motion.div
              variants={iconVariants}
              className="mr-3 text-[var(--brand-gold)] flex-shrink-0"
            >
              {icon}
            </motion.div>
          )}
          <motion.h2
            variants={titleVariants}
            className="text-2xl font-semibold"
          >
            {title}
          </motion.h2>
        </div>

        <motion.div
          variants={contentVariants}
          className="prose prose-neutral dark:prose-invert max-w-none"
        >
          {children}
        </motion.div>
      </Card>
    </motion.div>
  );
}
