"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, Mail, Phone, MapPin, CheckCircle } from "lucide-react";
import { motion } from "framer-motion";

interface ProfileRequirementDialogProps {
  hasCompleteAddress?: boolean;
}

export default function ProfileRequirementDialog({
  hasCompleteAddress = false,
}: ProfileRequirementDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [missingFields, setMissingFields] = useState<string[]>([]);
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    // Check URL parameters for missing fields
    const missingParam = searchParams.get("missing");
    const messageParam = searchParams.get("message");
    
    if (missingParam || messageParam) {
      const fields = missingParam ? missingParam.split(",") : [];
      
      // Also check current state to determine what's actually missing
      const actuallyMissing: string[] = [];
      if (!hasCompleteAddress) actuallyMissing.push("address");
      
      // Use the more comprehensive list
      const finalMissing = actuallyMissing.length > 0 ? actuallyMissing : fields;
      
      setMissingFields(finalMissing);
      setIsOpen(true);
      
      // Clean up URL parameters
      const newUrl = window.location.pathname;
      router.replace(newUrl, { scroll: false });
    }
  }, [searchParams, hasCompleteAddress, router]);

  const getFieldInfo = (field: string) => {
    switch (field) {
      case "email":
        return {
          icon: <Mail className="w-5 h-5" />,
          label: "Email Address",
          description: "Required for account notifications and password reset",
          color: "text-blue-600 dark:text-blue-400",
          bgColor: "bg-blue-100 dark:bg-blue-900/30",
        };
      case "phone":
        return {
          icon: <Phone className="w-5 h-5" />,
          label: "Mobile Number",
          description: "Required for account access and verification",
          color: "text-green-600 dark:text-green-400",
          bgColor: "bg-green-100 dark:bg-green-900/30",
        };
      case "address":
        return {
          icon: <MapPin className="w-5 h-5" />,
          label: "Address Information",
          description: "Required for location-based services",
          color: "text-purple-600 dark:text-purple-400",
          bgColor: "bg-purple-100 dark:bg-purple-900/30",
        };
      default:
        return {
          icon: <AlertCircle className="w-5 h-5" />,
          label: field,
          description: "Required information",
          color: "text-gray-600 dark:text-gray-400",
          bgColor: "bg-gray-100 dark:bg-gray-900/30",
        };
    }
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  if (missingFields.length === 0) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg font-semibold">
            <div className="p-2 rounded-full bg-amber-100 dark:bg-amber-900/30">
              <AlertCircle className="w-5 h-5 text-amber-600 dark:text-amber-400" />
            </div>
            Complete Your Profile
          </DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            Please add the following required information to continue using the dashboard.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-3 my-4">
          {missingFields.map((field, index) => {
            const fieldInfo = getFieldInfo(field);
            return (
              <motion.div
                key={field}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`flex items-start gap-3 p-3 rounded-lg border ${fieldInfo.bgColor} border-opacity-50`}
              >
                <div className={`p-1.5 rounded-lg ${fieldInfo.bgColor} ${fieldInfo.color}`}>
                  {fieldInfo.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-foreground">
                    {fieldInfo.label}
                  </h4>
                  <p className="text-xs text-muted-foreground mt-1">
                    {fieldInfo.description}
                  </p>
                </div>
              </motion.div>
            );
          })}
        </div>

        <div className="flex flex-col gap-2">
          <Button onClick={handleClose} className="w-full">
            <CheckCircle className="w-4 h-4 mr-2" />
            Got it, let me update my profile
          </Button>
          <p className="text-xs text-center text-muted-foreground">
            You can update these details in the forms below
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
