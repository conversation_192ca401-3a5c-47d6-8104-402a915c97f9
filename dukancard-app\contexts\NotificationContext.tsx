import React, { createContext, useContext, useState, useEffect, useRef, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { activityService, ActivityData } from '@/lib/services/activityService';
import { realtimeService } from '@/lib/services/realtimeService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState } from 'react-native';

interface NotificationContextType {
  notifications: ActivityData[];
  unreadCount: number;
  loading: boolean;
  refreshing: boolean;
  hasMore: boolean;
  isModalVisible: boolean;
  showModal: () => void;
  hideModal: () => void;
  refreshNotifications: () => Promise<void>;
  loadMoreNotifications: () => Promise<void>;
  markAsRead: (activityId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

const CACHE_KEY = 'dukancard_notifications_cache';
const CACHE_EXPIRY_KEY = 'dukancard_notifications_cache_expiry';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { user, profileStatus } = useAuth();
  const userType = profileStatus.roleStatus?.role;
  const [notifications, setNotifications] = useState<ActivityData[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [page, setPage] = useState(1);
  
  const subscriptionRef = useRef<any>(null);
  const isMountedRef = useRef(true);

  // Load cached notifications on mount
  useEffect(() => {
    if (user?.id && userType === 'business') {
      loadCachedNotifications();
    }
  }, [user?.id, userType]);

  // Set up real-time subscription when app is active (for business users only)
  useEffect(() => {
    if (!user?.id || userType !== 'business') return;

    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        setupRealtimeSubscription();
      } else {
        cleanupSubscription();
      }
    };

    // Set up initial subscription if app is active
    if (AppState.currentState === 'active') {
      setupRealtimeSubscription();
    }

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
      cleanupSubscription();
    };
  }, [user?.id, userType]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      cleanupSubscription();
    };
  }, []);

  const loadCachedNotifications = async () => {
    try {
      const [cachedData, cacheExpiry] = await Promise.all([
        AsyncStorage.getItem(CACHE_KEY),
        AsyncStorage.getItem(CACHE_EXPIRY_KEY)
      ]);

      if (cachedData && cacheExpiry) {
        const expiryTime = parseInt(cacheExpiry, 10);
        const now = Date.now();

        if (now < expiryTime) {
          const parsed = JSON.parse(cachedData);
          setNotifications(parsed.notifications || []);
          setUnreadCount(parsed.unreadCount || 0);
        } else {
          // Cache expired, fetch fresh data
          await refreshNotifications();
        }
      } else {
        // No cache, fetch fresh data
        await refreshNotifications();
      }
    } catch (error) {
      console.error('Error loading cached notifications:', error);
      await refreshNotifications();
    }
  };

  const cacheNotifications = async (notificationsData: ActivityData[], unreadCountData: number) => {
    try {
      const cacheData = {
        notifications: notificationsData,
        unreadCount: unreadCountData,
        timestamp: Date.now()
      };
      
      const expiryTime = Date.now() + CACHE_DURATION;
      
      await Promise.all([
        AsyncStorage.setItem(CACHE_KEY, JSON.stringify(cacheData)),
        AsyncStorage.setItem(CACHE_EXPIRY_KEY, expiryTime.toString())
      ]);
    } catch (error) {
      console.error('Error caching notifications:', error);
    }
  };

  const setupRealtimeSubscription = () => {
    if (!user?.id || subscriptionRef.current) return;
    
    subscriptionRef.current = realtimeService.subscribeToTable(
      'business_activities',
      (event) => {
        if (!isMountedRef.current) return;

        if (event.eventType === 'INSERT' && event.new) {
          // Increment unread count
          setUnreadCount(prev => prev + 1);
          
          // Refresh notifications to get the new one with proper user profile data
          refreshNotifications();
        }
      },
      {
        event: 'INSERT',
        filter: `business_profile_id=eq.${user.id}`,
      }
    );
  };

  const cleanupSubscription = () => {
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe();
      subscriptionRef.current = null;
    }
  };

  const refreshNotifications = async () => {
    if (!user?.id || userType !== 'business') return;

    try {
      setRefreshing(true);
      setPage(1);

      const response = await activityService.getBusinessActivities(
        user.id,
        1,
        20,
        'all'
      );

      if (response.success && response.data) {
        const newNotifications = response.data || [];
        setNotifications(newNotifications);
        setHasMore(newNotifications.length === 20);

        // Get unread count
        const unreadResponse = await activityService.getUnreadActivitiesCount(user.id);
        const newUnreadCount = unreadResponse.count || 0;
        setUnreadCount(newUnreadCount);

        // Cache the data
        await cacheNotifications(newNotifications, newUnreadCount);
      }
    } catch (error) {
      console.error('Error refreshing notifications:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const loadMoreNotifications = async () => {
    if (!user?.id || userType !== 'business' || loading || !hasMore) return;

    try {
      setLoading(true);
      const nextPage = page + 1;

      const response = await activityService.getBusinessActivities(
        user.id,
        nextPage,
        20,
        'all'
      );

      if (response.success && response.data) {
        const newNotifications = response.data || [];
        setNotifications(prev => [...prev, ...newNotifications]);
        setHasMore(newNotifications.length === 20);
        setPage(nextPage);
      } else {
        // No more data available or error occurred
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error loading more notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (activityId: string) => {
    if (!user?.id) return;

    try {
      await activityService.markActivitiesAsRead(user.id, [activityId]);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === activityId 
            ? { ...notification, is_read: true }
            : notification
        )
      );
      
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    if (!user?.id) return;

    try {
      await activityService.markActivitiesAsRead(user.id, 'all');
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, is_read: true }))
      );
      
      setUnreadCount(0);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const showModal = async () => {
    setIsModalVisible(true);
    // Don't mark as read immediately - let user view all unread notifications first
  };

  const hideModal = () => {
    setIsModalVisible(false);
  };

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    loading,
    refreshing,
    hasMore,
    isModalVisible,
    showModal,
    hideModal,
    refreshNotifications,
    loadMoreNotifications,
    markAsRead,
    markAllAsRead,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
