"use client";

import { useEffect, useState, useRef, useCallback } from "react";
import { motion, useAnimation } from "framer-motion";
import { Heart, Users, Star, Eye } from "lucide-react";
import { formatIndianNumberShort } from "@/lib/utils";

interface EnhancedMetricsDisplayProps {
  likes: number;
  subscribers: number;
  rating: number;
  views?: number;
  minimal?: boolean;
}

export default function EnhancedMetricsDisplay({
  likes,
  subscribers,
  rating,
  views = 125000,
  minimal = false,
}: EnhancedMetricsDisplayProps) {
  // State for animated counters
  const [animatedLikes, setAnimatedLikes] = useState(0);
  const [animatedSubscribers, setAnimatedSubscribers] = useState(0);
  const [animatedViews, setAnimatedViews] = useState(0);
  const [animatedRating, setAnimatedRating] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Intersection Observer setup
  const containerRef = useRef<HTMLDivElement>(null);
  const controls = useAnimation();

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Format large numbers with Indian number system (lakhs, crores, etc.)
  const formatNumber = (num: number) => {
    return formatIndianNumberShort(num);
  };

  // Function to animate counters - wrapped in useCallback to prevent recreation on each render
  const animateCounters = useCallback(() => {
    const duration = 2000; // 2 seconds
    const framesPerSecond = 60;
    const totalFrames = (duration / 1000) * framesPerSecond;

    let frame = 0;
    const timer = setInterval(() => {
      const progress = frame / totalFrames;
      const easeOutProgress = 1 - Math.pow(1 - progress, 3); // Cubic ease out

      setAnimatedLikes(Math.floor(likes * easeOutProgress));
      setAnimatedSubscribers(Math.floor(subscribers * easeOutProgress));
      setAnimatedViews(Math.floor(views * easeOutProgress));
      setAnimatedRating(parseFloat((rating * easeOutProgress).toFixed(1)));

      frame++;
      if (frame > totalFrames) {
        clearInterval(timer);
        setAnimatedLikes(likes);
        setAnimatedSubscribers(subscribers);
        setAnimatedViews(views);
        setAnimatedRating(rating);
      }
    }, 1000 / framesPerSecond);

    return timer;
  }, [likes, subscribers, views, rating]);

  // Setup intersection observer to detect when component is fully in view
  useEffect(() => {
    if (!containerRef.current || !isClient) return;

    const currentRef = containerRef.current; // Store ref in a variable for cleanup

    const observer = new IntersectionObserver(
      ([entry]) => {
        // Trigger animation when element is at least 50% in view
        if (entry.isIntersecting && entry.intersectionRatio >= 0.5) {
          controls.start("visible");

          if (!hasAnimated) {
            animateCounters();
            setHasAnimated(true);
          }
        }
      },
      { threshold: 0.5 } // 0.5 means 50% of the element must be in view
    );

    observer.observe(currentRef);

    return () => {
      observer.unobserve(currentRef);
    };
  }, [controls, hasAnimated, animateCounters, isClient]);

  if (!isClient) {
    return <div className="h-24" />; // Placeholder height to prevent layout shift
  }

  return (
    <div
      ref={containerRef}
      className="relative grid grid-cols-2 sm:grid-cols-4 gap-3 w-full max-w-4xl mx-auto py-6 px-4"
    >
      {/* Background glow effect */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <motion.div
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[80%] h-[80%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-[50px]"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          }}
        />
      </div>

      <EnhancedMetricCard
        icon={<Heart className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]" />}
        label="Likes"
        value={formatNumber(animatedLikes)}
        controls={controls}
        delay={0}
        minimal={minimal}
      />

      <EnhancedMetricCard
        icon={<Users className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]" />}
        label="Subscribers"
        value={formatNumber(animatedSubscribers)}
        controls={controls}
        delay={0.1}
        minimal={minimal}
      />

      <EnhancedMetricCard
        icon={<Star className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]" />}
        label="Rating"
        value={animatedRating.toFixed(1)}
        controls={controls}
        delay={0.2}
        minimal={minimal}
      />

      <EnhancedMetricCard
        icon={<Eye className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]" />}
        label="Views"
        value={formatNumber(animatedViews)}
        controls={controls}
        delay={0.3}
        minimal={minimal}
      />
    </div>
  );
}

interface EnhancedMetricCardProps {
  icon: React.ReactNode;
  label: string;
  value: string;
  controls: ReturnType<typeof useAnimation>;
  delay: number;
  minimal?: boolean;
}

function EnhancedMetricCard({
  icon,
  label,
  value,
  controls,
  delay,
  minimal: _minimal = false
}: EnhancedMetricCardProps) {
  return (
    <motion.div
      className="relative rounded-lg p-2 sm:p-3 md:p-4 bg-white/90 dark:bg-black/40 backdrop-blur-sm border border-[var(--brand-gold)]/20 flex flex-col items-center justify-center text-center shadow-sm overflow-hidden"
      variants={{
        hidden: { opacity: 0, y: 10 },
        visible: {
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.3,
            delay: delay,
          },
        },
      }}
      initial="hidden"
      animate={controls}
      whileHover={{
        scale: 1.05,
        boxShadow:
          "0 0 15px rgba(var(--brand-gold-rgb), 0.2), 0 0 5px rgba(var(--brand-gold-rgb), 0.1)",
        transition: { duration: 0.2 },
      }}
    >
      {/* Shimmer effect */}
      <motion.div
        className="absolute inset-0 w-full h-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="absolute top-0 left-0 w-[200%] h-full"
          style={{
            background: "linear-gradient(90deg, transparent 0%, rgba(var(--brand-gold-rgb), 0.1) 50%, transparent 100%)",
          }}
          initial={{ x: "-100%" }}
          animate={{ x: "100%" }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: "loop",
            repeatDelay: 5,
            ease: "easeInOut"
          }}
        />
      </motion.div>

      <motion.div
        className="mb-1 sm:mb-2 text-[var(--brand-gold)] opacity-90"
        style={{
          filter:
            "drop-shadow(0 0 2px rgba(var(--brand-gold-rgb), 0.3)) drop-shadow(0 0 1px rgba(var(--brand-gold-rgb), 0.2))",
        }}
        animate={{
          scale: [1, 1.1, 1],
          filter: [
            "drop-shadow(0 0 2px rgba(var(--brand-gold-rgb), 0.3))",
            "drop-shadow(0 0 4px rgba(var(--brand-gold-rgb), 0.5))",
            "drop-shadow(0 0 2px rgba(var(--brand-gold-rgb), 0.3))"
          ]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
          delay: delay + 1
        }}
      >
        {icon}
      </motion.div>

      <div className="text-base sm:text-xl md:text-2xl font-bold text-[var(--brand-gold)] mb-1 glow-text dark:glow-text-stronger">
        {value}
      </div>

      <div className="text-[10px] sm:text-xs text-neutral-600 dark:text-neutral-400">
        {label}
      </div>
    </motion.div>
  );
}
