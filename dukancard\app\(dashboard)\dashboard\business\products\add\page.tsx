import { Metadata } from "next";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import AddProductClient from "./AddProductClient";
import { getPlanLimit } from "@/lib/PricingPlans";

// Add metadata
export const metadata: Metadata = {
  title: "Add New Product",
  robots: "noindex, nofollow",
};

export default async function AddProductPage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/login?message=Authentication required");
  }

  // Fetch business profile to check if it exists
  const { error: profileError } = await supabase
    .from("business_profiles")
    .select("id")
    .eq("id", user.id)
    .single();

  if (profileError) {
    console.error("Error fetching business profile:", profileError.message);
    return redirect("/dashboard/business?error=Failed to load business profile");
  }

  // Get the user's current plan from payment_subscriptions
  const { data: subscriptionData, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("plan_id")
    .eq("business_profile_id", user.id)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
  }

  // Default to free plan if no subscription found
  const planId = subscriptionData?.plan_id || "free";
  const planLimit = getPlanLimit(planId);

  // Count existing available products
  const { count: availableCount, error: availableCountError } = await supabase
    .from("products_services")
    .select("id", { count: "exact", head: true })
    .eq("business_id", user.id)
    .eq("is_available", true);

  if (availableCountError) {
    console.error("Error counting available products:", availableCountError.message);
  }

  // Count total products (for display purposes)
  const { count: totalCount, error: countError } = await supabase
    .from("products_services")
    .select("id", { count: "exact", head: true })
    .eq("business_id", user.id);

  if (countError) {
    console.error("Error counting products:", countError.message);
    return redirect("/dashboard/business/products?error=Failed to count products");
  }

  return (
    <AddProductClient
      planLimit={planLimit}
      currentCount={totalCount || 0}
      currentAvailableCount={availableCount || 0}
    />
  );
}
