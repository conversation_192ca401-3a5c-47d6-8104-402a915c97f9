import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert<PERSON>riangle, Heart } from 'lucide-react';
import LikesPageClient from './components/LikesPageClient';
import { Suspense } from 'react';
import { LikeListSkeleton } from '@/app/components/shared/likes';
import { requireCompleteProfile } from '@/lib/actions/customerProfiles/addressValidation';

// Import the fetchCustomerLikes function
import { fetchCustomerLikes } from './actions';

export const metadata: Metadata = {
  title: "My Likes - Dukancard",
  robots: "noindex, nofollow",
};

export default async function CustomerLikesPage({
  searchParams
}: {
  searchParams: Promise<{ search?: string; page?: string }>
}) {
  // Properly await searchParams to fix the error
  const { search, page: pageParam } = await searchParams;
  const supabase = await createClient();
  const page = pageParam ? parseInt(pageParam) : 1;
  const searchTerm = search || "";

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/login?message=Please log in to view your likes.');
  }

  // Check if customer has complete address
  await requireCompleteProfile(user.id);

  try {
    // Fetch likes with pagination and search
    const likesResult = await fetchCustomerLikes(user.id, page, 10, searchTerm);

    // Wrap in a div to ensure proper layout with the skeleton
    return (
      <div className="relative space-y-6 max-w-6xl mx-auto">
        <Suspense fallback={
          <div className="relative z-10">
            <Card className="border shadow-md bg-white dark:bg-black border-neutral-200 dark:border-neutral-800">
              <CardHeader className="pb-4 border-b border-neutral-100 dark:border-neutral-800">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                  <div className="p-2 rounded-lg bg-rose-100 dark:bg-rose-900/30 text-rose-500 dark:text-rose-400 self-start">
                    <Heart className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                      Liked Businesses
                    </h3>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                      Businesses you&apos;ve liked
                    </p>
                  </div>
                </div>

                {/* Search skeleton */}
                <div className="mt-4">
                  <Skeleton className="h-10 w-full rounded-md" />
                </div>
              </CardHeader>

              <CardContent className="pt-4">
                <LikeListSkeleton />
              </CardContent>
            </Card>
          </div>
        }>
          <LikesPageClient
            initialLikes={likesResult.items}
            totalCount={likesResult.totalCount}
            currentPage={likesResult.currentPage}
            searchTerm={searchTerm}
          />
        </Suspense>
      </div>
    );
  } catch (_error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Could not load likes data. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }
}
