# Installation
> `npm install --save @types/yup`

# Summary
This package contains type definitions for yup (https://github.com/jquense/yup).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yup.

### Additional Details
 * Last updated: Mon, 16 May 2022 17:01:47 GMT
 * Dependencies: none
 * Global values: none

# Credits
These definitions were written by [<PERSON><PERSON><PERSON>](https://github.com/dhardtke), [<PERSON><PERSON><PERSON>](https://github.com/vtserman), [Moreton Bay Regional Council](https://github.com/MoretonBayRC), [<PERSON><PERSON> Sep<PERSON>a](https://github.com/sseppola), [<PERSON><PERSON>](https://github.com/YashdalfTheGray), [<PERSON>](https://github.com/vincentjames501), [<PERSON>](https://github.com/robert<PERSON>en), [<PERSON><PERSON>](https://github.com/sat0yu), [<PERSON>](https://github.com/deskoh), [<PERSON>](https://github.com/mauricedb), [<PERSON><PERSON>](https://github.com/elias-garcia), [<PERSON> <PERSON>](https://github.com/iansan5653), [Jay Fong](https://github.com/fjc0k), and [Lukas Elmer](https://github.com/lukaselmer).
