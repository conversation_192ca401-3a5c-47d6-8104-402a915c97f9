"use client";

import { motion } from "framer-motion";
import { CreditCard, ExternalLink } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function CardEditorLinkSection() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.2 }}
      className="rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 transition-all duration-300 hover:shadow-lg"
    >
      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
          <CreditCard className="w-4 sm:w-5 h-4 sm:h-5" />
        </div>
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
            Edit Digital Card Profile
          </h3>
          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
            Update your business details, contact information, social links,
            and appearance on your public digital card.
          </p>
        </div>
      </div>

      <div className="mt-4 sm:mt-6 flex justify-end">
        <div className="relative group">
          {/* Border glow effect - matches button size */}
          <div
            className="absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300"
            style={{
              boxShadow: `inset 0 0 20px rgba(147, 51, 234, 0.2), 0 0 20px rgba(147, 51, 234, 0.3)`
            }}
          />

          {/* Strong decorative colored glow elements - positioned relative to button */}
          <div className="absolute -top-1 -right-1 w-6 h-6 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none" />
          <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none" />

          <Button
            asChild
            variant="outline"
            size="sm"
            className={`
              relative overflow-hidden rounded-xl p-3
              bg-white dark:bg-black
              border border-purple-200/50 dark:border-purple-700/50
              shadow-purple-500/40 shadow-lg
              hover:shadow-xl hover:shadow-purple-500/40
              transition-all duration-300
              text-purple-500 dark:text-purple-400
              hover:bg-purple-500/5 dark:hover:bg-purple-500/10
              text-xs sm:text-sm h-auto
            `}
          >
            <Link href="/dashboard/business/card" className="flex items-center">
              <CreditCard className="w-4 h-4 mr-2" />
              Go to Card Editor
              <ExternalLink className="w-4 h-4 ml-2" />
            </Link>
          </Button>
        </div>
      </div>
    </motion.div>
  );
}
