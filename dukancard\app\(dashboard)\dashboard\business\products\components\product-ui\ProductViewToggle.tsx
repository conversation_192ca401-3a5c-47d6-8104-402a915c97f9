"use client";

import { motion } from "framer-motion";
import { FileText, Package, Filter, Infinity as InfinityIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Tabs,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { itemVariants } from "../../types";
import { useProducts } from "../../context/ProductsContext";

export default function ProductViewToggle() {
  const {
    viewType,
    setViewType,
    products,
    totalCount,
    planLimit,
    getFilterStatusText
  } = useProducts();

  return (
    <motion.div
      variants={itemVariants}
      className="flex flex-row items-center justify-between py-2 sm:py-3 md:py-4 relative z-10 w-full"
    >
      <div className="flex items-center gap-2 sm:gap-3">
        <span className="text-xs sm:text-sm font-medium text-neutral-600 dark:text-neutral-400">
          {products.length === 0
            ? "No items"
            : planLimit === Infinity
              ? `Showing ${products.length} of ${totalCount} items (Unlimited available)`
              : `Showing ${products.length} of ${totalCount} items`
          }
        </span>
        {getFilterStatusText() && (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800 text-xs">
            <Filter className="w-3 h-3 mr-1" />
            Filtered
          </Badge>
        )}
        {planLimit === Infinity && (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800 text-xs">
            <InfinityIcon className="w-3 h-3 mr-1" />
            Unlimited
          </Badge>
        )}
      </div>

      <Tabs value={viewType} onValueChange={(value) => setViewType(value as "table" | "grid")} className="w-auto">
        <TabsList className="grid w-[140px] sm:w-[160px] md:w-[180px] grid-cols-2 bg-neutral-50 dark:bg-neutral-800/70 border border-neutral-200 dark:border-neutral-700 rounded-lg p-1 gap-1">
          <TabsTrigger
            value="table"
            className="text-xs sm:text-sm data-[state=active]:bg-white dark:data-[state=active]:bg-neutral-900 data-[state=active]:shadow-sm data-[state=active]:border-primary/20 data-[state=active]:text-primary hover:bg-white/80 dark:hover:bg-neutral-900/80 hover:text-primary/80 rounded-md transition-all duration-200 px-2 py-1.5 flex items-center justify-center"
          >
            <FileText className="w-3.5 h-3.5 mr-1.5" />
            <span>Table</span>
          </TabsTrigger>
          <TabsTrigger
            value="grid"
            className="text-xs sm:text-sm data-[state=active]:bg-white dark:data-[state=active]:bg-neutral-900 data-[state=active]:shadow-sm data-[state=active]:border-primary/20 data-[state=active]:text-primary hover:bg-white/80 dark:hover:bg-neutral-900/80 hover:text-primary/80 rounded-md transition-all duration-200 px-2 py-1.5 flex items-center justify-center"
          >
            <Package className="w-3.5 h-3.5 mr-1.5" />
            <span>Grid</span>
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </motion.div>
  );
}
