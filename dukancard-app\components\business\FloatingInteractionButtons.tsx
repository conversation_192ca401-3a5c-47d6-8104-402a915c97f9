import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, Animated } from 'react-native';
import { Heart, Users, Star, Loader2 } from 'lucide-react-native';
import { BusinessInteractionStatus } from '../../lib/services/businessInteractions';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface FloatingInteractionButtonsProps {
  interactionStatus: BusinessInteractionStatus | null;
  isDark: boolean;
  isOwner: boolean;
  likeLoading: boolean;
  subscribeLoading: boolean;
  onLikePress: () => void;
  onSubscribePress: () => void;
  onReviewPress: () => void;
}

export default function FloatingInteractionButtons({
  interactionStatus,
  isDark,
  isOwner,
  likeLoading,
  subscribeLoading,
  onLikePress,
  onSubscribePress,
  onReviewPress,
}: FloatingInteractionButtonsProps) {
  const styles = createPublicCardViewStyles(isDark);

  // Animation values for floating buttons
  const likeScale = new Animated.Value(1);
  const subscribeScale = new Animated.Value(1);
  const reviewScale = new Animated.Value(1);

  // Animation values for spinners
  const likeSpinValue = new Animated.Value(0);
  const subscribeSpinValue = new Animated.Value(0);

  // Pulse animation for active buttons
  useEffect(() => {
    if (interactionStatus?.isLiked) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(likeScale, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(likeScale, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      likeScale.setValue(1);
    }
  }, [interactionStatus?.isLiked]);

  useEffect(() => {
    if (interactionStatus?.isSubscribed) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(subscribeScale, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(subscribeScale, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      subscribeScale.setValue(1);
    }
  }, [interactionStatus?.isSubscribed]);

  useEffect(() => {
    if (interactionStatus?.userRating) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(reviewScale, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(reviewScale, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      reviewScale.setValue(1);
    }
  }, [interactionStatus?.userRating]);

  // Spinner animations
  useEffect(() => {
    if (likeLoading) {
      Animated.loop(
        Animated.timing(likeSpinValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      ).start();
    } else {
      likeSpinValue.setValue(0);
    }
  }, [likeLoading]);

  useEffect(() => {
    if (subscribeLoading) {
      Animated.loop(
        Animated.timing(subscribeSpinValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      ).start();
    } else {
      subscribeSpinValue.setValue(0);
    }
  }, [subscribeLoading]);

  if (isOwner) {
    return null;
  }

  return (
    <View style={styles.floatingInteractionButtons}>
      {/* Like Button */}
      <View style={styles.floatingButtonContainer}>
        <Animated.View style={{ transform: [{ scale: likeScale }] }}>
          <TouchableOpacity
            style={[
              styles.floatingButton,
              styles.likeFloatingButton,
              interactionStatus?.isLiked && styles.activeFloatingButton
            ]}
            onPress={onLikePress}
            activeOpacity={0.7}
            disabled={likeLoading}
          >
            {/* Inner circle for better visual depth */}
            <View style={{
              width: 36,
              height: 36,
              borderRadius: 18,
              backgroundColor: interactionStatus?.isLiked ? '#E74C3C' : 'transparent',
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: interactionStatus?.isLiked ? 0 : 1,
              borderColor: '#E74C3C',
            }}>
              {likeLoading ? (
                <Animated.View
                  style={{
                    transform: [{
                      rotate: likeSpinValue.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0deg', '360deg'],
                      }),
                    }],
                  }}
                >
                  <Loader2 size={16} color={interactionStatus?.isLiked ? "#FFF" : "#E74C3C"} />
                </Animated.View>
              ) : (
                <Heart
                  size={16}
                  color={interactionStatus?.isLiked ? "#FFF" : "#E74C3C"}
                  fill={interactionStatus?.isLiked ? "#FFF" : "transparent"}
                />
              )}
            </View>
          </TouchableOpacity>
        </Animated.View>
        <Text style={styles.buttonLabel}>
          {interactionStatus?.isLiked ? 'Liked' : 'Like'}
        </Text>
      </View>

      {/* Subscribe Button */}
      <View style={styles.floatingButtonContainer}>
        <Animated.View style={{ transform: [{ scale: subscribeScale }] }}>
          <TouchableOpacity
            style={[
              styles.floatingButton,
              styles.subscribeFloatingButton,
              interactionStatus?.isSubscribed && styles.activeFloatingButton
            ]}
            onPress={onSubscribePress}
            activeOpacity={0.7}
            disabled={subscribeLoading}
          >
            {/* Inner circle for better visual depth */}
            <View style={{
              width: 36,
              height: 36,
              borderRadius: 18,
              backgroundColor: interactionStatus?.isSubscribed ? '#3498DB' : 'transparent',
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: interactionStatus?.isSubscribed ? 0 : 1,
              borderColor: '#3498DB',
            }}>
              {subscribeLoading ? (
                <Animated.View
                  style={{
                    transform: [{
                      rotate: subscribeSpinValue.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0deg', '360deg'],
                      }),
                    }],
                  }}
                >
                  <Loader2 size={16} color={interactionStatus?.isSubscribed ? "#FFF" : "#3498DB"} />
                </Animated.View>
              ) : (
                <Users
                  size={16}
                  color={interactionStatus?.isSubscribed ? "#FFF" : "#3498DB"}
                  fill={interactionStatus?.isSubscribed ? "#FFF" : "transparent"}
                />
              )}
            </View>
          </TouchableOpacity>
        </Animated.View>
        <Text style={styles.buttonLabel}>
          {interactionStatus?.isSubscribed ? 'Following' : 'Follow'}
        </Text>
      </View>

      {/* Review Button */}
      <View style={styles.floatingButtonContainer}>
        <Animated.View style={{ transform: [{ scale: reviewScale }] }}>
          <TouchableOpacity
            style={[
              styles.floatingButton,
              styles.reviewFloatingButton,
              interactionStatus?.userRating ? styles.activeFloatingButton : null
            ]}
            onPress={onReviewPress}
            activeOpacity={0.7}
          >
            {/* Inner circle for better visual depth */}
            <View style={{
              width: 36,
              height: 36,
              borderRadius: 18,
              backgroundColor: interactionStatus?.userRating ? '#F39C12' : 'transparent',
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: interactionStatus?.userRating ? 0 : 1,
              borderColor: '#F39C12',
            }}>
              <Star
                size={16}
                color={interactionStatus?.userRating ? "#FFF" : "#F39C12"}
                fill={interactionStatus?.userRating ? "#FFF" : "transparent"}
              />
            </View>
          </TouchableOpacity>
        </Animated.View>
        <Text style={styles.buttonLabel}>
          {interactionStatus?.userRating ? 'Reviewed' : 'Review'}
        </Text>
      </View>
    </View>
  );
}
