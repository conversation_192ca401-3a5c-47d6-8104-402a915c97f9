import React from 'react';
import { View, Text } from 'react-native';
import { DashboardLayout } from '@/components/shared/layout/DashboardLayout';
import { createSharedDiscoverStyles } from '@/styles/shared/_discover-styles';
import { useColorScheme } from '@/hooks/useColorScheme';

interface DiscoverScreenProps {
  userName: string;
  showNotifications?: boolean;
}

export default function DiscoverScreen({
  userName,
  showNotifications = true
}: DiscoverScreenProps) {
  const colorScheme = useColorScheme();
  const styles = createSharedDiscoverStyles(colorScheme);

  return (
    <DashboardLayout
      userName={userName}
      showNotifications={showNotifications}
    >
      <View style={styles.container}>
        <View style={styles.placeholder}>
          <Text style={styles.icon}>🔍</Text>
          <Text style={styles.title}>Discover</Text>
          <Text style={styles.subtitle}>Coming Soon</Text>
          <Text style={styles.description}>
            We're working on an amazing discovery experience to help you find the best businesses and products near you.
          </Text>
          <Text style={styles.note}>
            Stay tuned for advanced search, filters, categories, and location-based discovery features!
          </Text>
        </View>
      </View>
    </DashboardLayout>
  );
}
