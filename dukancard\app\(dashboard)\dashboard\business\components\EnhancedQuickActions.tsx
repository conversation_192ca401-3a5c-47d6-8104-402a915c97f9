"use client";

import { Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON>ard, Package, Heart, Bell } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

interface EnhancedQuickActionsProps {
  userPlan: string | null;
}

export default function EnhancedQuickActions({ userPlan: _userPlan }: EnhancedQuickActionsProps) {
  return (
    <div
      className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-5 transition-all duration-300 hover:shadow-lg h-full relative overflow-hidden"
    >

      <div className="relative z-10">
        <div className="flex items-center justify-between gap-2 mb-4 pb-3 border-b border-neutral-100 dark:border-neutral-800">
          <div className="flex items-center gap-2">
            <div className="p-1.5 sm:p-2 rounded-lg bg-primary/10 text-primary">
              <CreditCard className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
            </div>
            <h3 className="text-sm sm:text-base font-semibold text-neutral-800 dark:text-neutral-100 truncate">
              Quick Actions
            </h3>
          </div>
          <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse" />
        </div>

        <div className="flex flex-col gap-2 sm:gap-3 overflow-hidden">
          <div className="relative group">
            {/* Inner glow effect only */}
            <div className="absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none" />

            <Button
              asChild
              variant="outline"
              className="w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden"
            >
              <Link href="/dashboard/business/card">
                <CreditCard className="w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300" />
                Edit Business Card
              </Link>
            </Button>
          </div>

          <div className="relative group">
            {/* Inner glow effect only */}
            <div className="absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none" />

            <Button
              asChild
              variant="outline"
              className="w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden"
            >
              <Link href="/dashboard/business/products">
                <Package className="w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300" />
                Manage Products
              </Link>
            </Button>
          </div>



          <div className="relative group">
            {/* Inner glow effect only */}
            <div className="absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none" />

            <Button
              asChild
              variant="outline"
              className="w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden"
            >
              <Link href="/dashboard/business/analytics">
                <BarChart3 className="w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300" />
                View Analytics
              </Link>
            </Button>
          </div>

          <div className="relative group">
            {/* Inner glow effect only */}
            <div className="absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none" />

            <Button
              asChild
              variant="outline"
              className="w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden"
            >
              <Link href="/dashboard/business/likes">
                <Heart className="w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300" />
                My Likes
              </Link>
            </Button>
          </div>

          <div className="relative group">
            {/* Inner glow effect only */}
            <div className="absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none" />

            <Button
              asChild
              variant="outline"
              className="w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden"
            >
              <Link href="/dashboard/business/subscriptions">
                <Bell className="w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300" />
                My Subscriptions
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
