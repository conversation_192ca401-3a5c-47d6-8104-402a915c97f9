"use client";

import { motion } from "framer-motion";
import { Heart, Star, UserPlus } from "lucide-react";
import { formatIndianNumberShort } from "@/lib/utils";
import EnhancedMetricCard from "./EnhancedMetricCard";

interface BusinessProfile {
  total_likes: number;
  total_subscriptions: number;
  average_rating: number;
}

interface EngagementMetricsSectionProps {
  profile: BusinessProfile;
  initialProfile: BusinessProfile;
}

export default function EnhancedEngagementMetricsSection({
  profile,
  initialProfile,
}: EngagementMetricsSectionProps) {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      }
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="space-y-4">
      {/* Section Header */}
      <motion.div
        variants={headerVariants}
      >
        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="p-2 rounded-lg bg-rose-100 dark:bg-rose-900/30 text-rose-600 dark:text-rose-400 self-start">
          <Heart className="w-4 sm:w-5 h-4 sm:h-5" />
        </div>
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
            Engagement Metrics
          </h3>
          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
            How users are interacting with your business card
          </p>
        </div>
        </div>
      </motion.div>

      {/* Metrics Cards */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
        {/* Total Likes */}
        <EnhancedMetricCard
          title="Total Likes"
          value={formatIndianNumberShort(profile.total_likes)}
          icon={Heart}
          description="People who liked your card"
          color="rose"
          isUpdated={profile.total_likes !== initialProfile.total_likes}
        />

        {/* Total Subscribers */}
        <EnhancedMetricCard
          title="Total Subscribers"
          value={formatIndianNumberShort(profile.total_subscriptions)}
          icon={UserPlus}
          description="People subscribed to updates"
          color="blue"
          isUpdated={profile.total_subscriptions !== initialProfile.total_subscriptions}
        />

        {/* Average Rating */}
        <EnhancedMetricCard
          title="Average Rating"
          value={profile.average_rating?.toFixed(1) || "0.0"}
          suffix="/5.0"
          icon={Star}
          description="Average customer rating"
          color="amber"
          isUpdated={profile.average_rating !== initialProfile.average_rating}
        />
      </div>
      </div>
    </motion.div>
  );
}
