"use client";

import SubscriptionManager from "./subscription-manager/SubscriptionManager";
import EnhancedGlowButton from "./EnhancedGlowButton";
import { PricingPlan } from "@/lib/PricingPlans";
import { SubscriptionStatus } from "../page";

interface SubscriptionTabContentProps {
  userId: string;
  currentSubscriptionId: string | null;
  subscriptionStatus: SubscriptionStatus;
  currentPlanDetails?: PricingPlan;
  currentPlanCycle: "monthly" | "yearly";
  nextBillingDate: string | null;
  subscriptionStartDate?: string | null;
  subscriptionExpiryTime?: string | null;
  subscriptionChargeTime?: string | null;
  lastPaymentMethod?: string | null;
  cancellationRequestedAt: string | null;
  cancelledAt: string | null;
  trialEndDate: string | null;
  isEligibleForRefund: boolean;
  setActiveTab: (_tab: "plans" | "subscription") => void;
}

export default function SubscriptionTabContent({
  userId,
  currentSubscriptionId,
  subscriptionStatus,
  currentPlanDetails,
  currentPlanCycle,
  nextBillingDate,
  subscriptionStartDate,
  subscriptionExpiryTime,
  subscriptionChargeTime,
  lastPaymentMethod,
  cancellationRequestedAt,
  cancelledAt,
  trialEndDate,
  isEligibleForRefund,
  setActiveTab,
}: SubscriptionTabContentProps) {
  
  // Get plan amount
  const getPlanAmount = () => {
    if (!currentPlanDetails) return 0;

    // Extract the numeric value from the price string
    const priceString = currentPlanCycle === "monthly"
      ? currentPlanDetails.price
      : currentPlanDetails.yearlyPrice || currentPlanDetails.price;

    // Remove non-numeric characters and parse as integer
    return parseInt(priceString.replace(/[^\d]/g, ''));
  };

  if (currentSubscriptionId) {
    return (
      <SubscriptionManager
        userId={userId}
        subscriptionId={currentSubscriptionId}
        status={subscriptionStatus}
        planName={currentPlanDetails?.name || "Unknown Plan"}
        planId={currentPlanDetails?.id || "free"} // Pass planId to check if user is on free plan
        planCycle={currentPlanCycle}
        amount={getPlanAmount()}
        currency="INR"
        nextBillingDate={nextBillingDate}
        // Use Supabase data instead of Razorpay data
        lastPaymentDate={subscriptionStartDate} // Use subscription start date as last payment date
        lastPaymentStatus="SUCCESS"
        lastPaymentMethod={lastPaymentMethod} // Use the payment method from database
        createdAt={subscriptionStartDate} // Use subscription start date as created date
        expiresAt={subscriptionExpiryTime}
        pausedAt={subscriptionStatus === "paused" ? subscriptionStartDate : null}
        cancellationRequestedAt={cancellationRequestedAt}
        cancelledAt={cancelledAt}
        isEligibleForRefund={isEligibleForRefund}
        // Additional date fields
        subscriptionStartDate={subscriptionStartDate}
        subscriptionChargeTime={subscriptionChargeTime}
        trialEndDate={trialEndDate}
      />
    );
  }

  return (
    <div className="text-center py-12 px-4 border rounded-lg bg-muted/20">
      <div className="flex flex-col items-center">
        <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
        </div>
        <h2 className="text-2xl font-bold mb-2">No Active Subscription</h2>
        <p className="text-muted-foreground mb-6 max-w-md">
          You don&apos;t have an active subscription yet. Choose a plan that fits your business needs to unlock premium features.
        </p>
        <div className="flex justify-center">
          <EnhancedGlowButton
            onClick={() => setActiveTab("plans")}
            size="lg"
            className="px-8 py-2"
            showArrow={true}
            roundedFull={true}
          >
            Browse Plans
          </EnhancedGlowButton>
        </div>
      </div>
    </div>
  );
}
