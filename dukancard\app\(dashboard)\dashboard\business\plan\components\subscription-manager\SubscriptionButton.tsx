"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { CreditCard, RefreshCcw, Loader2 } from "lucide-react";
import { SubscriptionState, SubscriptionManagerProps } from "./types";
import { useSubscriptionDetails } from "./hooks";
import { useSubscriptionActions } from "./SubscriptionActions";

interface SubscriptionButtonProps {
  subscriptionState: SubscriptionState;
  subscriptionProps: SubscriptionManagerProps;
}

export const SubscriptionButton = ({
  subscriptionState,
  subscriptionProps,
}: SubscriptionButtonProps) => {
  const {
    isLoading,
    setSubscriptionDetails,
    setIsWithinRefundWindow,
  } = subscriptionState;

  const {
    subscriptionStatus,
    currentPlanDetails,
    selectedPlan,
    planCycle,
    selectedCycle,
  } = subscriptionProps;

  // Check if the selected plan is the current plan
  const isCurrentPlan =
    currentPlanDetails?.id === selectedPlan?.id && planCycle === selectedCycle;

  // Fetch subscription details hook
  const fetchSubscriptionDetails = useSubscriptionDetails(
    setSubscriptionDetails,
    setIsWithinRefundWindow
  );

  // Get subscription actions
  const actions = useSubscriptionActions({
    subscriptionState,
    subscriptionProps,
  });

  // Determine the button text and action based on subscription status
  let buttonText = "Subscribe";
  let buttonAction: () => Promise<void> | void;
  let buttonIcon = <CreditCard className="w-4 h-4 mr-2" />;

  if (subscriptionStatus === "active") {
    if (isCurrentPlan) {
      buttonText = "Manage Subscription";
      buttonAction = actions.handleCancelSubscription;
      buttonIcon = <RefreshCcw className="w-4 h-4 mr-2" />;
    } else {
      buttonText = "Change Plan";
      // For plan changes, we should not use this button - it should be handled by the main plan selection component
      buttonAction = () => {
        console.warn("Plan change should be handled by the main plan selection component, not this button");
      };
      buttonIcon = <RefreshCcw className="w-4 h-4 mr-2" />;
    }
  } else if (subscriptionStatus === "trial") {
    buttonText = "Subscribe Now";
    // For trial users wanting to subscribe, we should not use this button - it should be handled by the main plan selection component
    buttonAction = () => {
      console.warn("Subscription creation should be handled by the main plan selection component, not this button");
    };
  } else {
    // Default case - for inactive users, subscription creation should be handled by the main plan selection component
    buttonAction = () => {
      console.warn("Subscription creation should be handled by the main plan selection component, not this button");
    };
  }

  return (
    <Button
      onClick={() => {
        // For all subscriptions, proceed directly
        // The payment method limitations dialog will only be shown if needed
        // in the server action for existing subscriptions with UPI/eMandate

        // Fetch subscription details if showing cancel dialog
        if (isCurrentPlan) {
          fetchSubscriptionDetails();
        }

        // Proceed with the appropriate action
        buttonAction();
      }}
      disabled={isLoading}
      className="w-full"
      size="lg"
    >
      {isLoading ? (
        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
      ) : (
        buttonIcon
      )}
      {isLoading ? "Processing..." : buttonText}
    </Button>
  );
};
