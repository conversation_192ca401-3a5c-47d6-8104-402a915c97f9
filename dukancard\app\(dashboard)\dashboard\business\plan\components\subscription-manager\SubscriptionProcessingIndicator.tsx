"use client";

import React, { useEffect } from "react";
import { useSubscriptionProcessing, ProcessingStatus } from "../../context/SubscriptionProcessingContext";
import { toast } from "sonner";
import {
  Loader2,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Clock,
  CreditCard,
  RefreshCw
} from "lucide-react";

export default function SubscriptionProcessingIndicator() {
  const {
    status,
    message,
    resetProcessing
  } = useSubscriptionProcessing();

  // Show toast notifications based on status changes
  useEffect(() => {
    // If status is idle, dismiss all toasts and return
    if (status === "idle") {
      toast.dismiss();
      return;
    }

    // Get toast configuration based on status
    const getToastConfig = (status: ProcessingStatus) => {
      switch (status) {
        case "processing":
          return {
            type: "loading" as const,
            icon: <Loader2 className="h-5 w-5 animate-spin" />,
            title: "Processing",
            description: message,
            duration: 0 // Don't auto-dismiss
          };
        case "success":
          return {
            type: "success" as const,
            icon: <CheckCircle2 className="h-5 w-5" />,
            title: "Success",
            description: message,
            duration: 5000
          };
        case "error":
          return {
            type: "error" as const,
            icon: <XCircle className="h-5 w-5" />,
            title: "Error",
            description: message,
            duration: 5000
          };
        case "razorpay_server_error":
          return {
            type: "error" as const,
            icon: <AlertCircle className="h-5 w-5" />,
            title: "Server Error",
            description: message,
            duration: 8000
          };
        case "waiting_for_webhook":
          return {
            type: "loading" as const,
            icon: <Clock className="h-5 w-5 text-yellow-500" />,
            title: "Waiting",
            description: message,
            duration: 0 // Don't auto-dismiss
          };
        case "payment_authorized":
        case "payment_captured":
          return {
            type: "success" as const,
            icon: <CreditCard className="h-5 w-5" />,
            title: "Payment",
            description: message,
            duration: 5000
          };
        case "future_payment_authorized":
          return {
            type: "success" as const,
            icon: <Clock className="h-5 w-5" />,
            title: "Subscription Authorized",
            description: message,
            duration: 8000
          };
        case "subscription_created":
        case "subscription_authenticated":
        case "subscription_active":
        case "subscription_activated":
          return {
            type: "success" as const,
            icon: <CheckCircle2 className="h-5 w-5" />,
            title: "Subscription",
            description: message,
            duration: 5000
          };
        case "subscription_resumed":
          return {
            type: "success" as const,
            icon: <CheckCircle2 className="h-5 w-5 text-green-500" />,
            title: "Subscription Resumed",
            description: message,
            duration: 8000
          };
        case "subscription_pending":
          return {
            type: "info" as const,
            icon: <Clock className="h-5 w-5" />,
            title: "Subscription",
            description: message,
            duration: 5000
          };
        case "subscription_halted":
          return {
            type: "info" as const,
            icon: <AlertCircle className="h-5 w-5" />,
            title: "Subscription Halted",
            description: message,
            duration: 5000
          };
        case "subscription_cancelled":
          return {
            type: "info" as const,
            icon: <XCircle className="h-5 w-5 text-red-500" />,
            title: "Subscription Cancelled",
            description: message,
            duration: 8000
          };
        case "subscription_completed":
          return {
            type: "info" as const,
            icon: <CheckCircle2 className="h-5 w-5 text-green-500" />,
            title: "Subscription Completed",
            description: message,
            duration: 5000
          };
        case "subscription_expired":
          return {
            type: "info" as const,
            icon: <Clock className="h-5 w-5 text-amber-500" />,
            title: "Subscription Expired",
            description: message,
            duration: 5000
          };
        case "subscription_paused":
          return {
            type: "info" as const,
            icon: <AlertCircle className="h-5 w-5 text-amber-500" />,
            title: "Subscription Paused",
            description: message,
            duration: 8000
          };
        case "refund_processing":
          return {
            type: "loading" as const,
            icon: <RefreshCw className="h-5 w-5 animate-spin" />,
            title: "Refund",
            description: message,
            duration: 0 // Don't auto-dismiss
          };
        case "refund_processed":
          return {
            type: "success" as const,
            icon: <CheckCircle2 className="h-5 w-5" />,
            title: "Refund",
            description: message,
            duration: 5000
          };
        default:
          return {
            type: "info" as const,
            icon: <AlertCircle className="h-5 w-5" />,
            title: "Information",
            description: message,
            duration: 5000
          };
      }
    };

    // Get toast configuration
    const config = getToastConfig(status);

    // Show toast based on type
    const toastId = `subscription-${status}-${Date.now()}`;

    switch (config.type) {
      case "success":
        toast.success(config.title, {
          id: toastId,
          description: config.description,
          duration: config.duration,
          icon: config.icon
        });
        break;
      case "error":
        toast.error(config.title, {
          id: toastId,
          description: config.description,
          duration: config.duration,
          icon: config.icon
        });
        break;
      case "loading":
        toast.loading(config.title, {
          id: toastId,
          description: config.description,
          duration: config.duration,
          icon: config.icon
        });
        break;
      case "info":
      default:
        toast.info(config.title, {
          id: toastId,
          description: config.description,
          duration: config.duration,
          icon: config.icon
        });
        break;
    }

    // Auto-dismiss error notifications after 5 seconds
    if (status === "error" || status === "razorpay_server_error") {
      const timer = setTimeout(() => {
        resetProcessing();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [status, message, resetProcessing]);

  // This component no longer renders anything visible
  // It just manages the toast notifications based on subscription processing status
  return null;
}
