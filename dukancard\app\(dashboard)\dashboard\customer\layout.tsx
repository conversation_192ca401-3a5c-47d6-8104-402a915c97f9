// This is now a Server Component
import React from "react"; // Removed useState, useEffect
import { createClient } from "@/utils/supabase/server"; // Use server client
import { Metadata } from "next";
import CustomerDashboardClientLayout from "./CustomerDashboardClientLayout"; // Import the new client layout

// Add metadata to prevent indexing for all customer dashboard pages
export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Customer Dashboard", // Generic title for dashboard sections
    robots: "noindex, nofollow", // Prevent indexing
  };
}

export default async function CustomerDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  let initialUserName: string | null = null;
  let initialUserAvatarUrl: string | null = null;

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    // Removed assignment to initialUserEmail

    // Fetch name and avatar from customer_profiles table
    const { data: profile, error: profileError } = await supabase
      .from("customer_profiles")
      .select("name, avatar_url")
      .eq("id", user.id)
      .single();

    if (profileError) {
      console.error("Error fetching customer profile:", profileError.message);
      initialUserName = "User"; // Fallback on error
    } else {
      initialUserName = profile?.name || "User"; // Use fetched name or fallback
      initialUserAvatarUrl = profile?.avatar_url; // Use fetched avatar URL or null
    }
  } else {
    // Handle case where user is not found (though middleware should prevent this)
    initialUserName = "User";
    // Removed assignment to initialUserEmail
  }

  return (
       <CustomerDashboardClientLayout
         initialUserName={initialUserName} // Pass the fetched name
         // Removed initialUserEmail prop as it's no longer used by the client layout
         initialUserAvatarUrl={initialUserAvatarUrl} // Pass the avatar URL (currently null)
       >
      {children}
    </CustomerDashboardClientLayout>
  );
}
