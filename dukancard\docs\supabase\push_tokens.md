# Push Tokens Table

## Overview
The `push_tokens` table stores push notification tokens for users to enable real-time notifications when they receive business activities like likes, subscriptions, and ratings.

## Table Structure

```sql
CREATE TABLE push_tokens (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    token TEXT NOT NULL,
    user_type TEXT NOT NULL CHECK (user_type IN ('customer', 'business')),
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android', 'web')),
    device_info JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Indexes

```sql
-- Efficient querying by user and active status
CREATE INDEX idx_push_tokens_user_active 
ON push_tokens (user_id, is_active) 
WHERE is_active = TRUE;

-- Efficient querying by user type
CREATE INDEX idx_push_tokens_user_type 
ON push_tokens (user_type, is_active) 
WHERE is_active = TRUE;

-- Unique constraint to prevent duplicate tokens per user
CREATE UNIQUE INDEX idx_push_tokens_user_token 
ON push_tokens (user_id, token);
```

## Row Level Security (RLS)

```sql
-- Enable RLS
ALTER TABLE push_tokens ENABLE ROW LEVEL SECURITY;

-- Users can manage their own push tokens
CREATE POLICY "Users can manage their own push tokens" ON push_tokens
    FOR ALL USING (auth.uid() = user_id);

-- Service role can manage all push tokens
CREATE POLICY "Service role can manage all push tokens" ON push_tokens
    FOR ALL USING (auth.role() = 'service_role');
```

## Fields Description

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `user_id` | UUID | Foreign key to auth.users.id |
| `token` | TEXT | The actual push notification token from Expo |
| `user_type` | TEXT | Either 'customer' or 'business' |
| `platform` | TEXT | Device platform: 'ios', 'android', or 'web' |
| `device_info` | JSONB | Additional device information (optional) |
| `is_active` | BOOLEAN | Whether the token is active and should receive notifications |
| `created_at` | TIMESTAMP | When the token was first registered |
| `updated_at` | TIMESTAMP | When the token was last updated |

## Usage Examples

### Register a Push Token
```sql
INSERT INTO push_tokens (user_id, token, user_type, platform, device_info)
VALUES (
    'user-uuid',
    'ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]',
    'business',
    'android',
    '{"model": "Pixel 6", "version": "13"}'::jsonb
)
ON CONFLICT (user_id, token) 
DO UPDATE SET 
    is_active = TRUE,
    updated_at = NOW();
```

### Get Active Tokens for a Business User
```sql
SELECT token, platform 
FROM push_tokens 
WHERE user_id = 'business-user-uuid' 
  AND user_type = 'business' 
  AND is_active = TRUE;
```

### Deactivate Old Tokens
```sql
UPDATE push_tokens 
SET is_active = FALSE 
WHERE updated_at < NOW() - INTERVAL '30 days';
```

## Integration with Push Notification System

This table is used by:
- **PushNotificationService** (React Native) - to store and retrieve user tokens
- **Push notification queue processor** - to send notifications to active tokens
- **Frequency management system** - to track which users should receive notifications

## Best Practices

1. **Token Cleanup**: Regularly clean up inactive tokens (older than 30 days)
2. **Upsert Pattern**: Use ON CONFLICT to handle token updates gracefully
3. **Platform Specific**: Store platform info for platform-specific notification handling
4. **Active Status**: Use is_active flag to temporarily disable notifications without deleting tokens
5. **Device Info**: Store device information for debugging and analytics

## Related Tables

- `business_activities` - Activities that trigger push notifications
- `push_notification_queue` - Queue for processing notifications
- `notification_analytics` - Analytics for notification performance
