"use client";

import { motion } from "framer-motion";
import EnhancedMetricsDisplay from "./EnhancedMetricsDisplay";

interface EnhancedMetricsContainerProps {
  likes: number;
  subscribers: number;
  rating: number;
  views?: number;
  minimal?: boolean;
}

export default function EnhancedMetricsContainer({
  likes,
  subscribers,
  rating,
  views,
  minimal = false,
}: EnhancedMetricsContainerProps) {
  return (
    <motion.section
      className="relative w-full py-4 overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden pointer-events-none">
        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 bg-[url('/decorative/subtle-pattern.svg')] bg-repeat opacity-5 dark:opacity-10"></div>
      </div>
      
      {/* Enhanced metrics display */}
      <div className="relative w-full px-2 sm:px-4">
        <EnhancedMetricsDisplay
          likes={likes}
          subscribers={subscribers}
          rating={rating}
          views={views}
          minimal={minimal}
        />
      </div>
    </motion.section>
  );
}
