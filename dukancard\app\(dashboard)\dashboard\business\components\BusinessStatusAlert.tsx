"use client";

import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertCircle, ArrowRight } from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";

/**
 * BusinessStatusAlert Component
 * 
 * This component displays an alert when a business profile is set to offline status.
 * It informs the user that their business card is not visible to customers and
 * provides a direct link to the card settings page to change the status.
 */
export default function BusinessStatusAlert() {
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="mb-6"
    >
      <Alert 
        variant="destructive"
        className="bg-red-50 border-red-200 dark:bg-red-950/50 dark:border-red-800/50"
      >
        <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
        <AlertTitle className="text-red-800 dark:text-red-300">
          Your Business Card is Offline
        </AlertTitle>
        <AlertDescription className="text-red-700 dark:text-red-400">
          <p className="mb-3">
            Your business card is currently set to offline status and won&apos;t appear in search results or discovery pages.
          </p>
          <Button 
            asChild 
            size="sm" 
            variant="outline"
            className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900/30"
          >
            <Link href="/dashboard/business/card" className="flex items-center gap-1">
              <span>Go Online Now</span>
              <ArrowRight className="h-3.5 w-3.5" />
            </Link>
          </Button>
        </AlertDescription>
      </Alert>
    </motion.div>
  );
}
