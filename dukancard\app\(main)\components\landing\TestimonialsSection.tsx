"use client";

import { useState, useEffect } from "react";
import SectionBackground from "../SectionBackground";
import TestimonialCard from "./TestimonialCard";
import TestimonialCarousel from "./TestimonialCarousel";

// Enhanced testimonial data with ratings
const testimonials = [
  {
    name: "<PERSON><PERSON>",
    business: "Kumar Electronics",
    quote:
      "Dukancard has transformed my business. My customers can now easily find my store and contact me directly. The digital card looks professional and represents my brand perfectly.",
    rating: 5
  },
  {
    name: "<PERSON><PERSON>",
    business: "Sharma Boutique",
    quote:
      "I've seen a 40% increase in customer engagement since using Dukancard. The analytics feature helps me understand what my customers are interested in.",
    rating: 5
  },
  {
    name: "<PERSON><PERSON>",
    business: "Patel Grocery",
    quote:
      "Setting up my digital card was incredibly easy. Now I can showcase my products and services online without the hassle of building a website.",
    rating: 4
  },
  {
    name: "<PERSON><PERSON>",
    business: "Verma Jewellers",
    quote:
      "The subscription feature has helped me build a loyal customer base. My customers get updates about new products and promotions directly.",
    rating: 5
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    business: "Singh Auto Parts",
    quote:
      "The QR code feature has been a game-changer for my business. Customers can scan it from my physical store and instantly access my full product catalog online.",
    rating: 5
  },
  {
    name: "Meera <PERSON>i",
    business: "<PERSON>i Tailoring",
    quote:
      "I love how easy it is to update my business information and showcase my latest designs. My customers appreciate being able to browse my work before visiting my shop.",
    rating: 4
  },
];

export default function TestimonialsSection() {
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile devices
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);



  return (
    <section className="py-10 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto relative">
      {/* Simple background */}
      <div className="absolute inset-0 -z-10">
        <SectionBackground variant="purple" intensity="low" />
      </div>

      <div className="text-center mb-12">
        <div className="relative inline-block">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Trusted by{" "}
            <span className="text-[var(--brand-gold)] relative">
              Businesses
            </span>{" "}
            Everywhere
          </h2>
        </div>
        <p className="text-lg text-neutral-600 dark:text-neutral-300 max-w-2xl mx-auto">
          See what our customers are saying about their Dukancard experience.
        </p>
      </div>

      {/* Responsive testimonial display */}
      {isMobile ? (
        // Mobile: Auto-scrolling carousel
        <div className="w-full max-w-md mx-auto">
          <TestimonialCarousel testimonials={testimonials} />
        </div>
      ) : (
        // Desktop: Grid layout with cards
        <div className="relative">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
            {testimonials.slice(0, 4).map((testimonial, index) => (
              <TestimonialCard
                key={index}
                name={testimonial.name}
                business={testimonial.business}
                quote={testimonial.quote}
                _index={index}
                rating={testimonial.rating}
              />
            ))}
          </div>
        </div>
      )}
    </section>
  );
}
