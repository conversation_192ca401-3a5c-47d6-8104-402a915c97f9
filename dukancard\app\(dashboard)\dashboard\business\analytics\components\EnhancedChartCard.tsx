"use client";

import { ReactNode } from "react";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface EnhancedChartCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  children: ReactNode;
  className?: string;
}

export default function EnhancedChartCard({
  title,
  description,
  icon: Icon,
  children,
  className,
}: EnhancedChartCardProps) {
  return (
    <motion.div
      className="p-1"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <div
        className={cn(
          "rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 shadow-md overflow-hidden group transition-all duration-300 hover:shadow-lg",
          className
        )}
      >
      <div className="p-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="flex items-center gap-2">
          <motion.div
            className="p-1.5 rounded-lg bg-primary/10 text-primary"
            whileHover={{ scale: 1.1 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <Icon className="w-4 h-4" />
          </motion.div>
          <div>
            <h3 className="text-sm font-semibold text-neutral-800 dark:text-neutral-100">
              {title}
            </h3>
            <p className="text-xs text-neutral-500 dark:text-neutral-400">
              {description}
            </p>
          </div>
        </div>
      </div>

      <div className="p-4">
        {children}
      </div>

      {/* Animated border effect using Framer Motion */}
      <div className="relative overflow-hidden" style={{ width: "100%" }}>
        <motion.div
          className="h-0.5 bg-gradient-to-r from-primary/30 via-primary to-primary/30"
          initial={{ x: "-100%" }}
          animate={{ x: "100%" }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>
      </div>
    </motion.div>
  );
}
