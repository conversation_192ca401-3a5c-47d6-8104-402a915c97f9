import { DashboardLayout } from '@/components/shared/layout/DashboardLayout';
import React, { useEffect, useState, useCallback } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, RefreshControl, Image, ActivityIndicator } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { useAuth } from '@/contexts/AuthContext';
import { useBusinessProfile } from '@/contexts/UserDataContext';
import { useRouter } from 'expo-router';
import { User, Settings, ChevronRight, BarChart3, Package, Star, Users, Heart, Palette, LogOut, CreditCard, ImageIcon, Crown } from 'lucide-react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { ProfileSkeleton } from '@/components/ui/SkeletonLoader';
import { ErrorState } from '@/components/ui/ErrorState';
import { handleNetworkError, logError } from '@/lib/utils/errorHandling';
import { createBusinessProfileStyles } from '@/styles/dashboard/business/business-profile-styles';
import { ThemeToggleButton } from '@/components/ui/ThemeToggleButton';

import ComingSoonModal from '@/components/ui/ComingSoonModal';
import { BusinessProfileStats } from '@/components/business/BusinessProfileStats';

export default function BusinessProfileScreen() {
  const { user, profileStatus, signOut } = useAuth();
  const { businessProfile, getBusinessProfile, refreshBusinessProfile } = useBusinessProfile();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const [refreshing, setRefreshing] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Coming soon modal state
  const [comingSoonModal, setComingSoonModal] = useState({
    visible: false,
    featureName: '',
    description: '',
  });

  // Use profile data from context
  const businessName = businessProfile?.business_name || 'Business';

  // Fetch business profile if not available
  useEffect(() => {
    if (!businessProfile) {
      setIsLoading(true);
      getBusinessProfile()
        .then(() => setIsLoading(false))
        .catch((err) => {
          setError('Failed to load profile');
          setIsLoading(false);
        });
    }
  }, [businessProfile, getBusinessProfile]);



  // Handle pull-to-refresh using profile context
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refreshBusinessProfile(); // Force refresh from context
    } catch (error) {
      console.error('Error during refresh:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refreshBusinessProfile]);

  // Helper function to show coming soon modal
  const showComingSoonModal = (featureName: string, description?: string) => {
    setComingSoonModal({
      visible: true,
      featureName,
      description: description || `${featureName} is available on our website with full functionality and advanced management tools.`,
    });
  };

  // Helper function to close coming soon modal
  const closeComingSoonModal = () => {
    setComingSoonModal({
      visible: false,
      featureName: '',
      description: '',
    });
  };



  const isDark = colorScheme === 'dark';
  const styles = createBusinessProfileStyles(colorScheme);

  return (
    <DashboardLayout
      userName={businessName}
      showNotifications={true}
    >
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#D4AF37']}
            tintColor="#D4AF37"
          />
        }
      >
        {/* Profile Content - Matching Skeleton Layout */}
        {isLoading && !businessProfile ? (
          <ProfileSkeleton />
        ) : error ? (
          <ErrorState
            type="generic"
            title="Unable to load data"
            message={error}
            onRetry={() => refreshBusinessProfile()}
            showRetry={true}
            style={styles.errorContainer}
          />
        ) : (
          <View style={[styles.profileContent, { backgroundColor: isDark ? '#000000' : '#FFFFFF' }]}>
            {/* Profile Header - Avatar, Name */}
            <View style={styles.profileHeader}>
              <View style={[styles.largeAvatar, { backgroundColor: isDark ? '#333' : '#f5f5f5' }]}>
                {businessProfile?.logo_url ? (
                  <Image
                    source={{ uri: businessProfile.logo_url }}
                    style={styles.avatarImage}
                    resizeMode="cover"
                  />
                ) : (
                  <User size={40} color={isDark ? '#D4AF37' : '#D4AF37'} />
                )}
              </View>
              <Text style={[styles.profileName, { color: isDark ? '#fff' : '#000' }]}>{businessName}</Text>
            </View>

            {/* Business Stats - Matching Customer Profile Design */}
            {businessProfile && user && (
              <BusinessProfileStats
                initialProfile={{
                  id: businessProfile.id,
                  business_name: businessProfile.business_name || businessName || '',
                  total_likes: businessProfile.total_likes || 0,
                  total_subscriptions: businessProfile.total_subscriptions || 0,
                  average_rating: businessProfile.average_rating || 0,
                }}
                userId={user.id}
              />
            )}

            {/* Business Menu Options */}
            <View style={styles.profileMenu}>
              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() => showComingSoonModal('Manage Card', 'Card management features including design customization, contact information updates, and branding options are available on our website.')}
              >
                <CreditCard size={20} color={isDark ? '#D4AF37' : '#D4AF37'} />
                <Text style={[styles.menuItemText, { color: isDark ? '#fff' : '#000' }]}>Manage Card</Text>
                <ChevronRight size={20} color={isDark ? '#999' : '#666'} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() => showComingSoonModal('Manage Products/Services', 'Full product and service management including bulk operations, categories, inventory tracking, and advanced pricing options are available on our website.')}
              >
                <Package size={20} color={isDark ? '#D4AF37' : '#D4AF37'} />
                <Text style={[styles.menuItemText, { color: isDark ? '#fff' : '#000' }]}>Manage Products/Services</Text>
                <ChevronRight size={20} color={isDark ? '#999' : '#666'} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() => showComingSoonModal('Gallery', 'Gallery management with drag-and-drop reordering, bulk uploads, image optimization, and advanced organization features are available on our website.')}
              >
                <ImageIcon size={20} color={isDark ? '#D4AF37' : '#D4AF37'} />
                <Text style={[styles.menuItemText, { color: isDark ? '#fff' : '#000' }]}>Gallery</Text>
                <ChevronRight size={20} color={isDark ? '#999' : '#666'} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() => showComingSoonModal('Analytics', 'Comprehensive analytics including visitor insights, engagement metrics, conversion tracking, and detailed reports are available on our website.')}
              >
                <BarChart3 size={20} color={isDark ? '#D4AF37' : '#D4AF37'} />
                <Text style={[styles.menuItemText, { color: isDark ? '#fff' : '#000' }]}>Analytics</Text>
                <ChevronRight size={20} color={isDark ? '#999' : '#666'} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() => showComingSoonModal('Manage Plan', 'Plan management including subscription upgrades, billing history, payment methods, and plan comparisons are available on our website.')}
              >
                <Crown size={20} color={isDark ? '#D4AF37' : '#D4AF37'} />
                <Text style={[styles.menuItemText, { color: isDark ? '#fff' : '#000' }]}>Manage Plan</Text>
                <ChevronRight size={20} color={isDark ? '#999' : '#666'} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() => router.push('/business/settings')}
              >
                <Settings size={20} color={isDark ? '#D4AF37' : '#D4AF37'} />
                <Text style={[styles.menuItemText, { color: isDark ? '#fff' : '#000' }]}>Settings</Text>
                <ChevronRight size={20} color={isDark ? '#999' : '#666'} />
              </TouchableOpacity>
            </View>

            {/* Theme Toggle Section */}
            <View style={[styles.themeSection, { backgroundColor: isDark ? '#1a1a1a' : '#f8f9fa' }]}>
              <View style={styles.themeSectionHeader}>
                <Palette size={20} color={isDark ? '#D4AF37' : '#D4AF37'} />
                <Text style={[styles.themeSectionTitle, { color: isDark ? '#fff' : '#000' }]}>
                  App Theme
                </Text>
              </View>
              <ThemeToggleButton variant="profile" showLabel />
            </View>

            {/* Logout Button */}
            <TouchableOpacity
              style={[styles.logoutButton, { backgroundColor: isDark ? '#2a1a1a' : '#fef2f2' }]}
              onPress={async () => {
                if (isLoggingOut) return; // Prevent multiple clicks

                setIsLoggingOut(true);
                try {
                  const { error } = await signOut();
                  if (error) {
                    console.error('Logout error:', error);
                    setIsLoggingOut(false); // Reset loading state on error
                  }
                  // On success, the auth context will handle navigation
                } catch (error) {
                  console.error('Logout error:', error);
                  setIsLoggingOut(false); // Reset loading state on error
                }
              }}
              disabled={isLoggingOut}
            >
              {isLoggingOut ? (
                <ActivityIndicator size="small" color="#ef4444" />
              ) : (
                <LogOut size={20} color="#ef4444" />
              )}
              <Text style={[styles.logoutButtonText, { color: '#ef4444' }]}>
                {isLoggingOut ? 'Logging out...' : 'Logout'}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* Coming Soon Modal */}
      <ComingSoonModal
        visible={comingSoonModal.visible}
        onClose={closeComingSoonModal}
        featureName={comingSoonModal.featureName}
        description={comingSoonModal.description}
      />
    </DashboardLayout>
  );
}


