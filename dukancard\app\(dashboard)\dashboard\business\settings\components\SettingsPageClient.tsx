"use client";

import { motion } from "framer-motion";
import { Settings } from "lucide-react";
import LinkEmailSection from "./LinkEmailSection";
import LinkPhoneSection from "./LinkPhoneSection";
import PasswordUpdateSection from "./PasswordUpdateSection";
import CardEditorLinkSection from "./CardEditorLinkSection";
import AccountDeletionSection from "./AccountDeletionSection";

interface SettingsPageClientProps {
  currentEmail: string | undefined;
  currentPhone: string | null | undefined;
  registrationType: 'google' | 'email' | 'phone';
}

export default function SettingsPageClient({
  currentEmail,
  currentPhone,
  registrationType,
}: SettingsPageClientProps) {
  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-6"
    >
      <motion.div variants={itemVariants} className="mb-6">
        {/* Main card container */}
        <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-4 sm:p-5 md:p-6 mb-4 transition-all duration-300 hover:shadow-lg relative overflow-hidden">

          {/* Content with relative positioning */}
          <div className="relative z-10 space-y-6">
            {/* Section Header */}
            <div className="flex items-center justify-between gap-2 mb-4 pb-3 border-b border-neutral-100 dark:border-neutral-800">
              <div className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-primary/10 text-primary">
                  <Settings className="w-4 sm:w-5 h-4 sm:h-5" />
                </div>
                <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Account Settings
                </h3>
              </div>
            </div>

            {/* Settings Sections */}

            {/* Link Email - Show for all users with different behaviors */}
            <LinkEmailSection
              currentEmail={currentEmail}
              currentPhone={currentPhone}
              registrationType={registrationType}
            />

            {/* Link Phone - Show for all users with different behaviors */}
            <LinkPhoneSection
              currentEmail={currentEmail}
              currentPhone={currentPhone}
              registrationType={registrationType}
            />

            {/* Password Management - Not for Google users */}
            {registrationType !== 'google' && (
              <PasswordUpdateSection
                registrationType={registrationType}
              />
            )}

            <CardEditorLinkSection />

            <AccountDeletionSection />
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
