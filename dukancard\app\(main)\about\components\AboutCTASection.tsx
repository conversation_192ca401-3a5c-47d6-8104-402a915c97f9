"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ArrowRight, Star } from "lucide-react";
import Link from "next/link";

export default function AboutCTASection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });

  return (
    <section
      ref={sectionRef}
      className="py-12 sm:py-16 md:py-20 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.6 }}
        className="bg-gradient-to-br from-primary/10 dark:from-[var(--brand-gold)]/20 to-background dark:to-black border border-primary/20 dark:border-[var(--brand-gold)]/30 rounded-2xl p-6 sm:p-8 md:p-12 relative overflow-hidden"
      >
        {/* Decorative elements - adjusted for mobile */}
        <motion.div
          className="absolute -right-20 -top-20 w-40 sm:w-64 h-40 sm:h-64 bg-primary/10 dark:bg-[var(--brand-gold)]/20 rounded-full blur-3xl"
          animate={{
            opacity: [0.5, 0.7, 0.5],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        />

        <motion.div
          className="absolute -left-20 -bottom-20 w-40 sm:w-64 h-40 sm:h-64 bg-primary/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl"
          animate={{
            opacity: [0.3, 0.5, 0.3],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            repeatType: "reverse",
            delay: 1,
          }}
        />

        {/* Animated stars - hidden on mobile */}
        <motion.div
          className="hidden sm:block absolute top-8 right-8 text-[var(--brand-gold)]/20 dark:text-[var(--brand-gold)]/30"
          animate={{
            rotate: [0, 360],
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 3, repeat: Infinity, repeatType: "reverse" }
          }}
        >
          <Star size={24} />
        </motion.div>

        <motion.div
          className="hidden sm:block absolute bottom-8 left-8 text-blue-500/20 dark:text-blue-500/30"
          animate={{
            rotate: [0, -360],
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 3, repeat: Infinity, repeatType: "reverse", delay: 1 }
          }}
        >
          <Star size={24} />
        </motion.div>

        {/* Content */}
        <div className="relative z-10 max-w-3xl mx-auto text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-2xl sm:text-3xl md:text-4xl font-bold text-foreground mb-4 sm:mb-6"
          >
            Ready to Join the{" "}
            <span className="text-[var(--brand-gold)]">Digital Revolution</span>?
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-base sm:text-lg text-muted-foreground mb-6 sm:mb-8"
          >
            Create your digital business card today and start connecting with
            customers in a whole new way.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex flex-col sm:flex-row gap-4 justify-center w-full"
          >
            <Link href="/pricing" className="w-full sm:w-auto">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.98 }}
                className="w-full"
              >
                <Button className="cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 px-4 sm:px-6 md:px-8 py-4 sm:py-5 md:py-6 rounded-full font-medium text-base sm:text-lg flex gap-2 items-center justify-center w-full sm:w-auto">
                  Get Started <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
                </Button>
              </motion.div>
            </Link>

            <Link href="/contact" className="w-full sm:w-auto">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.98 }}
                className="w-full"
              >
                <Button
                  variant="outline"
                  className="cursor-pointer border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 hover:text-[var(--brand-gold)] dark:hover:bg-[var(--brand-gold)]/10 px-4 sm:px-6 md:px-8 py-4 sm:py-5 md:py-6 rounded-full font-medium text-base sm:text-lg w-full sm:w-auto"
                >
                  Contact Us
                </Button>
              </motion.div>
            </Link>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
}
