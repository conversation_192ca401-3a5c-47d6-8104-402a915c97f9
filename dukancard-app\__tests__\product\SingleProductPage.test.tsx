import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import SingleProductPage from '@/app/product/[productId]';
import { getProduct } from '@/lib/actions/products';

// Mock dependencies
jest.mock('@/lib/actions/products');
jest.mock('@/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  canGoBack: jest.fn(() => true),
};

const mockProduct = {
  id: 'product-123',
  name: 'Test Product',
  description: 'Test product description',
  base_price: 1000,
  discounted_price: 800,
  image_url: 'https://example.com/product.jpg',
  business_id: 'business-123',
  slug: 'test-product',
  product_type: 'physical' as const,
  is_available: true,
  images: ['https://example.com/product.jpg'],
  featured_image_index: 0,
  created_at: new Date(),
  updated_at: new Date(),
};

const mockBusiness = {
  id: 'business-123',
  business_name: 'Test Business',
  business_slug: 'test-business',
  phone: '+91 9876543210',
  whatsapp_number: '+91 9876543210',
  address_line: 'Test Address',
  city: 'Test City',
  state: 'Test State',
  pincode: '123456',
  logo_url: 'https://example.com/logo.jpg',
};

describe('SingleProductPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // The useRouter mock is already set up in jest.setup.js
    (getProduct as jest.Mock).mockResolvedValue({
      success: true,
      data: mockProduct,
    });

  });

  describe('Navigation Entry Points', () => {
    it('should handle navigation from feed posts', async () => {
      (useLocalSearchParams as jest.Mock).mockReturnValue({
        productId: 'product-123',
      });

      render(<SingleProductPage />);

      await waitFor(() => {
        expect(getProduct).toHaveBeenCalledWith('product-123');
      });

      await waitFor(() => {
        expect(screen.getByText('Test Product')).toBeTruthy();
      });
    });

    it('should handle navigation from public card screen', async () => {
      (useLocalSearchParams as jest.Mock).mockReturnValue({
        productId: 'product-456',
      });

      render(<SingleProductPage />);

      await waitFor(() => {
        expect(getProduct).toHaveBeenCalledWith('product-456');
      });
    });

    it('should handle invalid product ID gracefully', () => {
      (useLocalSearchParams as jest.Mock).mockReturnValue({
        productId: null,
      });

      render(<SingleProductPage />);

      // Should return null for invalid productId - component should not render anything
      expect(screen.queryByTestId('product-container')).toBeNull();
    });
  });

  describe('Back Navigation', () => {
    beforeEach(() => {
      (useLocalSearchParams as jest.Mock).mockReturnValue({
        productId: 'product-123',
      });
    });

    it('should navigate back when back button is pressed', async () => {
      render(<SingleProductPage />);

      await waitFor(() => {
        expect(screen.getByText('Test Product')).toBeTruthy();
      });

      const backButton = screen.getByTestId('back-button');
      fireEvent.press(backButton);

      // The mock router from jest.setup.js should be called
      expect(useRouter().back).toHaveBeenCalled();
    });
  });

  describe('Data Display', () => {
    beforeEach(() => {
      (useLocalSearchParams as jest.Mock).mockReturnValue({
        productId: 'product-123',
      });
    });

    it('should display product information correctly', async () => {
      render(<SingleProductPage />);

      await waitFor(() => {
        expect(screen.getByText('Test Product')).toBeTruthy();
        expect(screen.getByText('Test product description')).toBeTruthy();
      });
    });

    it('should display pricing information with discount', async () => {
      render(<SingleProductPage />);

      await waitFor(() => {
        expect(screen.getByText('₹800')).toBeTruthy(); // Discounted price
        expect(screen.getByText('₹1,000')).toBeTruthy(); // Original price
      });
    });

    it('should display business information', async () => {
      render(<SingleProductPage />);

      await waitFor(() => {
        expect(screen.getByText('Test Business')).toBeTruthy();
      });
    });

    it('should handle business profile navigation', async () => {
      render(<SingleProductPage />);

      await waitFor(() => {
        expect(screen.getByText('Test Business')).toBeTruthy();
      });

      const businessProfile = screen.getByTestId('business-profile');
      fireEvent.press(businessProfile);

      expect(useRouter().push).toHaveBeenCalledWith('/business/test-business');
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      (useLocalSearchParams as jest.Mock).mockReturnValue({
        productId: 'product-123',
      });
    });

    it('should handle product fetch error', async () => {
      (getProduct as jest.Mock).mockResolvedValue({
        success: false,
        message: 'Product not found',
      });

      render(<SingleProductPage />);

      await waitFor(() => {
        expect(screen.getByText('Product not found')).toBeTruthy();
      });
    });

    it('should handle business fetch error', async () => {
      // Mock the internal getBusinessProfileById function to fail
      const mockGetProduct = getProduct as jest.Mock;
      mockGetProduct.mockResolvedValue({
        success: true,
        data: { ...mockProduct, business_id: 'invalid-business' },
      });

      render(<SingleProductPage />);

      await waitFor(() => {
        expect(screen.getByText('Failed to fetch business profile')).toBeTruthy();
      });
    });

    it('should show loading state initially', () => {
      render(<SingleProductPage />);

      expect(screen.getByTestId('loading-indicator')).toBeTruthy();
    });
  });

  describe('Contact Actions', () => {
    beforeEach(() => {
      (useLocalSearchParams as jest.Mock).mockReturnValue({
        productId: 'product-123',
      });
    });

    it('should handle phone call action', async () => {
      const { Linking } = await import('react-native');
      const mockLinking = Linking as jest.Mocked<typeof Linking>;
      mockLinking.openURL = jest.fn();

      render(<SingleProductPage />);

      await waitFor(() => {
        expect(screen.getByText('Test Business')).toBeTruthy();
      });

      const phoneButton = screen.getByTestId('phone-button');
      fireEvent.press(phoneButton);

      expect(mockLinking.openURL).toHaveBeenCalledWith('tel:+91 9876543210');
    });

    it('should handle WhatsApp action', async () => {
      const { Linking } = await import('react-native');
      const mockLinking = Linking as jest.Mocked<typeof Linking>;
      mockLinking.openURL = jest.fn();

      render(<SingleProductPage />);

      await waitFor(() => {
        expect(screen.getByText('Test Business')).toBeTruthy();
      });

      const whatsappButton = screen.getByTestId('whatsapp-button');
      fireEvent.press(whatsappButton);

      expect(mockLinking.openURL).toHaveBeenCalledWith(
        'whatsapp://send?phone=+91 9876543210&text=Hi, I am interested in Test Product'
      );
    });
  });
});
