import React from "react";
import { Metadata } from "next";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { pricingPlans, PricingPlan } from "@/lib/PricingPlans";
import BusinessDashboardClient from "../components/BusinessDashboardClient";
import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";

// Helper function to find plan details by ID and cycle
const getPlanDetails = (planId: string | null, planCycle: string | null): PricingPlan | undefined => {
  if (!planId) return undefined;
  // Use the provided plan cycle or default to monthly
  const cycle = (planCycle === "yearly") ? "yearly" : "monthly";
  const allPlans = pricingPlans(cycle);
  return allPlans.find((plan) => plan.id === planId);
};

// Define types for the business profile and subscription data
type BusinessProfileInfo = {
  business_name: string | null;
  trial_end_date: string | null;
};

type PaymentSubscriptionInfo = {
  subscription_status: string | null;
  plan_id: string | null;
  plan_cycle: string | null;
};

// Helper function to check subscription status using centralized logic
function checkSubscriptionStatus(
  profile: BusinessProfileInfo | null,
  subscription: PaymentSubscriptionInfo | null
): "active" | "trial" | "inactive" {
  if (!profile) return "inactive";

  const now = new Date();
  const trialEndDate = profile.trial_end_date
    ? new Date(profile.trial_end_date)
    : null;

  // If we have a subscription status from the database, use centralized logic
  if (subscription?.subscription_status) {
    const status = subscription.subscription_status;
    const planId = subscription.plan_id || 'free';

    // Use centralized logic - only paid subscriptions count as active
    // Trial and free plan users are NOT considered to have active subscription
    if (status === 'active' || status === 'authenticated') {
      // Only paid plans count as active subscription
      if (planId !== 'free') {
        return "active";
      }
    }

    if (status === 'trial') {
      return "trial";
    }
  }

  // Fallback: Check if user is in trial period based on trial_end_date
  if (trialEndDate && trialEndDate > now) {
    return "trial";
  }

  return "inactive"; // Trial ended or never existed, and no active subscription
}

// Add metadata
export const metadata: Metadata = {
  title: "Business Dashboard Overview",
  robots: "noindex, nofollow",
};

export default async function BusinessOverviewPage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/login?message=Authentication required");
  }

  // Fetch the business profile including interaction details and status
  const { data: profileData, error: profileError } = await supabase
    .from("business_profiles")
    .select(
      "business_name, business_slug, trial_end_date, total_likes, total_subscriptions, average_rating, logo_url, title, status"
    )
    .eq("id", user.id)
    .single();

  // Fetch subscription data from payment_subscriptions
  const { data: subscription, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("subscription_status, plan_id, plan_cycle")
    .eq("business_profile_id", user.id)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
  }

  if (profileError || !profileData) {
    console.error(
      "Error fetching business profile or profile not found:",
      profileError?.message
    );
    return redirect("/login?message=Profile fetch error");
  }

  // No need to generate visit data as we've removed the performance metrics component

  // Get subscription status and plan details
  const subscriptionStatus = checkSubscriptionStatus(profileData, subscription);

  // Get the plan ID from the subscription data, even if the subscription is inactive or halted
  // This ensures that users who have paused their subscription still see their actual plan
  const planId = subscription?.plan_id || "free";
  const planDetails = getPlanDetails(planId, subscription?.plan_cycle || null);

  // Create the properly formatted initialProfile object
  const initialProfile = {
    business_name: profileData.business_name || "",
    business_slug: profileData.business_slug || "",
    plan_id: planId, // Use the planId we determined above
    plan_cycle: subscription?.plan_cycle || null,
    has_active_subscription:
      // Use centralized logic: only paid subscriptions count as active
      // Trial and free plan users have has_active_subscription = false
      (subscription?.subscription_status === "active" || subscription?.subscription_status === "authenticated") &&
      planId !== 'free',
    trial_end_date: profileData.trial_end_date,
    total_likes: profileData.total_likes || 0,
    total_subscriptions: profileData.total_subscriptions || 0,
    average_rating: profileData.average_rating || 0,
    logo_url: profileData.logo_url,
    title: profileData.title,
    status: profileData.status || "offline" // Default to offline if status is not set
  };

  return (
    <Suspense fallback={<Skeleton className="h-[600px] w-full" />}>
      <BusinessDashboardClient
        initialProfile={initialProfile}
        userId={user.id}
        subscriptionStatus={subscriptionStatus}
        planDetails={planDetails}
        subscription={subscription}
      />
    </Suspense>
  );
}
