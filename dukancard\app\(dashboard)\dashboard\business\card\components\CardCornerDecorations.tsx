import Image from "next/image";

export default function CardCornerDecorations() {
  return (
    <>
      {/* Subtle corner decorations */}
      <div className="absolute top-0 left-0 w-12 h-12 pointer-events-none z-20 opacity-20">
        <div style={{ transform: "rotate(0deg)" }}>
          <Image
            src="/decorative/card-border.svg"
            alt=""
            width={48}
            height={48}
            className="w-full h-full"
          />
        </div>
      </div>
      <div className="absolute top-0 right-0 w-12 h-12 pointer-events-none z-20 opacity-20">
        <div style={{ transform: "rotate(90deg)" }}>
          <Image
            src="/decorative/card-border.svg"
            alt=""
            width={48}
            height={48}
            className="w-full h-full"
          />
        </div>
      </div>
      <div className="absolute bottom-0 right-0 w-12 h-12 pointer-events-none z-20 opacity-20">
        <div style={{ transform: "rotate(180deg)" }}>
          <Image
            src="/decorative/card-border.svg"
            alt=""
            width={48}
            height={48}
            className="w-full h-full"
          />
        </div>
      </div>
      <div className="absolute bottom-0 left-0 w-12 h-12 pointer-events-none z-20 opacity-20">
        <div style={{ transform: "rotate(270deg)" }}>
          <Image
            src="/decorative/card-border.svg"
            alt=""
            width={48}
            height={48}
            className="w-full h-full"
          />
        </div>
      </div>
    </>
  );
}
