import { Calendar } from "lucide-react";
import { useTheme } from "next-themes";
import NextImage from "next/image";
import { getThemeSpecificHeaderImage, getBrandingText, hasCustomBrandingAccess, shouldShowDukancardBranding } from "@/lib/utils/customBranding";

interface CardHeaderProps {
  userPlan?: "free" | "basic" | "growth" | "pro" | "enterprise" | "trial";
  establishedYear?: number | null;
  customBranding?: {
    hide_dukancard_branding?: boolean;
    custom_header_text?: string;
    custom_header_image_url?: string; // Legacy field
    custom_header_image_light_url?: string; // Light theme
    custom_header_image_dark_url?: string; // Dark theme
  };
}

export default function CardHeader({ userPlan, establishedYear, customBranding }: CardHeaderProps) {
  const { resolvedTheme } = useTheme();

  // Get theme-specific header image with fallback logic
  const themeSpecificHeaderImage = getThemeSpecificHeaderImage(
    userPlan,
    customBranding,
    resolvedTheme
  );

  // Get custom branding text
  const customBrandingText = getBrandingText(userPlan, customBranding);

  // Check if user has Pro/Enterprise access
  const hasProEnterpriseAccess = hasCustomBrandingAccess(userPlan);

  // Determine what to show in the header based on plan and toggle state
  const shouldShowCustomHeaderImage = hasProEnterpriseAccess &&
    customBranding?.hide_dukancard_branding &&
    themeSpecificHeaderImage;

  const shouldShowCustomHeaderText = hasProEnterpriseAccess &&
    customBranding?.hide_dukancard_branding &&
    customBrandingText &&
    !shouldShowCustomHeaderImage; // Only show text if no image (image has priority)

  // Use the centralized function for consistent branding logic
  const shouldShowDukancardBrandingResult = shouldShowDukancardBranding(userPlan, customBranding);

  return (
    <div className="flex justify-between items-start">
      <div className="flex items-center">
        {/* Priority: Theme-specific header image > Custom header text > Dukancard branding */}
        {shouldShowCustomHeaderImage ? (
          <div className="max-w-[120px] max-h-[32px] overflow-hidden">
            <NextImage
              src={themeSpecificHeaderImage}
              alt="Custom header"
              width={120}
              height={32}
              className="h-8 w-auto object-contain"
              style={{ maxWidth: '120px', maxHeight: '32px' }}
              onError={(e) => {
                // Gracefully handle broken images by hiding the element
                e.currentTarget.style.display = 'none';
              }}
            />
          </div>
        ) : shouldShowCustomHeaderText ? (
          <span className="font-medium text-sm text-[var(--theme-color)]">
            {customBrandingText}
          </span>
        ) : (
          /* Show Dukancard branding if toggle is OFF or no custom content */
          shouldShowDukancardBrandingResult && (
            <span className="font-bold text-md text-[var(--theme-color)]">
              Dukan
              <span className="text-neutral-900 dark:text-white">card</span>
            </span>
          )
        )}
      </div>

      {/* Established Year badge */}
      {establishedYear && (
        <div className="flex items-center justify-center text-xs py-0.5 px-2 rounded-full bg-[var(--theme-color-10)] dark:bg-[var(--theme-color-20)]">
          <Calendar className="w-3 h-3 mr-1 text-[var(--theme-color)]" />
          <span className="font-semibold">Est. {establishedYear}</span>
        </div>
      )}
    </div>
  );
}