import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Maximize, Trash2, GripVertical } from "lucide-react";
import { GalleryImage } from "../types";

interface SortableImageItemProps {
  image: GalleryImage;
  onViewImage: (_url: string) => void;
  onDeleteImage: (_image: GalleryImage) => void;
}

export default function SortableImageItem({
  image,
  onViewImage,
  onDeleteImage,
}: SortableImageItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: image.id,
    transition: {
      duration: 150, // Smooth animation duration
      easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.3 : 1,
    zIndex: isDragging ? 1000 : 'auto',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="relative group"
    >
      <div className="aspect-square relative overflow-hidden rounded-lg border shadow-sm hover:shadow-md transition-all duration-300">
        <Image
          src={image.url}
          alt="Gallery image"
          fill
          className="object-cover transition-transform duration-500 group-hover:scale-110"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />

        {/* Drag Handle - Higher z-index and better positioning */}
        <div
          {...attributes}
          {...listeners}
          className="absolute top-2 left-2 p-2 bg-black/70 rounded-md cursor-grab active:cursor-grabbing opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-30 hover:bg-black/80"
          style={{ touchAction: 'none' }}
        >
          <GripVertical className="h-4 w-4 text-white" />
        </div>

        {/* Overlay with actions - Lower z-index and pointer-events handling */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 pointer-events-none">
          <div className="absolute bottom-3 right-3 flex space-x-2 pointer-events-auto">
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 bg-white/20 backdrop-blur-sm text-white shadow-md hover:bg-white/30"
              onClick={(e) => {
                e.stopPropagation();
                onViewImage(image.url);
              }}
            >
              <Maximize className="h-4 w-4" />
            </Button>
            <Button
              variant="destructive"
              size="icon"
              className="h-8 w-8 bg-red-500/80 hover:bg-red-600/90 text-white shadow-md"
              onClick={(e) => {
                e.stopPropagation();
                onDeleteImage(image);
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
