import React from 'react';
import { TouchableOpacity, View, Text, StyleSheet } from 'react-native';
import { Bell } from 'lucide-react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useNotifications } from '@/contexts/NotificationContext';
import { useAuth } from '@/contexts/AuthContext';

interface NotificationIconProps {
  size?: number;
  color?: string;
  showBadge?: boolean;
  onPress?: () => void;
}

export const NotificationIcon: React.FC<NotificationIconProps> = ({
  size = 24,
  color,
  showBadge = true,
  onPress
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { profileStatus } = useAuth();

  // Get user role from profileStatus
  const userRole = profileStatus.roleStatus?.role;

  // Only show for business users
  if (userRole !== 'business') {
    return null;
  }

  const { unreadCount, showModal } = useNotifications();

  const iconColor = color || (isDark ? '#f3f4f6' : '#374151');

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      showModal();
    }
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <Bell size={size} color={iconColor} />
        
        {/* Unread count badge */}
        {showBadge && unreadCount > 0 && (
          <View style={[
            styles.badge,
            {
              backgroundColor: '#ef4444',
              borderColor: isDark ? '#111827' : '#ffffff'
            }
          ]}>
            <Text style={styles.badgeText}>
              {unreadCount > 99 ? '99+' : unreadCount.toString()}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 8,
  },
  iconContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -6,
    right: -6,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '600',
    lineHeight: 12,
  },
});
