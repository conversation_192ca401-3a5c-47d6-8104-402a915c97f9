"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

import ReviewCard from "@/app/components/shared/reviews/ReviewCard";
import ReviewCardSkeleton from "@/app/components/shared/reviews/ReviewCardSkeleton";
import ReviewSortDropdown, { ReviewSortOption } from "@/app/components/shared/reviews/ReviewSortDropdown";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface ReviewData {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string | null;
  business_profile_id: string;
  user_id: string;
  profiles: {
    id: string;
    full_name: string | null;
    avatar_url: string | null;
  } | null;
  reviewer_type?: 'business' | 'customer';
  reviewer_name?: string;
  reviewer_avatar?: string | null;
  reviewer_slug?: string | null;
}

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  perPage: number;
}

interface BusinessReviewListClientProps {
  businessProfileId: string;
}

export default function BusinessReviewListClient({ businessProfileId }: BusinessReviewListClientProps) {
  const [reviews, setReviews] = useState<ReviewData[]>([]);
  const [sortBy, setSortBy] = useState<ReviewSortOption>("newest");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    perPage: 10
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  // Fetch reviews from the API
  const fetchReviews = useCallback(async (page: number, sort: ReviewSortOption) => {
    setIsLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        businessProfileId: businessProfileId,
        sort: sort,
      });

      const response = await fetch(`/api/business/reviews?${queryParams.toString()}`);

      if (!response.ok) {
        const errorText = await response.text();

        try {
          // Try to parse the error as JSON
          const errorJson = JSON.parse(errorText);
          throw new Error(errorJson.error || 'Failed to fetch reviews');
        } catch (_parseError) {
          // If parsing fails, use the raw error text
          throw new Error(`Failed to fetch reviews: ${response.status} ${response.statusText}`);
        }
      }

      const data = await response.json();

      // Check if data.reviews is defined and is an array
      if (data.reviews && Array.isArray(data.reviews)) {
        setReviews(data.reviews);
        setPagination(data.pagination || {
          currentPage: 1,
          totalPages: 1,
          totalCount: data.reviews.length,
          perPage: 10
        });
      } else {
        setReviews([]);
        setPagination({
          currentPage: 1,
          totalPages: 1,
          totalCount: 0,
          perPage: 10
        });
        throw new Error('Invalid response format: reviews data is missing or not an array');
      }
    } catch (_err) {
      setError('Failed to load reviews. Please try again.');
      setReviews([]);
    } finally {
      setIsLoading(false);
    }
  }, [businessProfileId]);

  // Fetch reviews when page or sort changes
  useEffect(() => {
    fetchReviews(pagination.currentPage, sortBy);
  }, [pagination.currentPage, sortBy, fetchReviews]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle sort change
  const handleSortChange = (newSortBy: ReviewSortOption) => {
    setSortBy(newSortBy);
    // Reset to first page when sorting changes
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };



  // Generate pagination items
  const renderPaginationItems = () => {
    const { currentPage, totalPages } = pagination;
    const items = [];

    // Always show first page
    items.push(
      <PaginationItem key="first">
        <PaginationLink
          href="#"
          onClick={(e) => {
            e.preventDefault();
            handlePageChange(1);
          }}
          isActive={currentPage === 1}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );

    // Show ellipsis if needed
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Show current page and surrounding pages
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      if (i === 1 || i === totalPages) continue; // Skip first and last page as they're always shown
      items.push(
        <PaginationItem key={i}>
          <PaginationLink
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(i);
            }}
            isActive={currentPage === i}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    // Show ellipsis if needed
    if (currentPage < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Always show last page if there's more than one page
    if (totalPages > 1) {
      items.push(
        <PaginationItem key="last">
          <PaginationLink
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(totalPages);
            }}
            isActive={currentPage === totalPages}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  return (
    <div className="space-y-4">
      {/* Sort Controls */}
      <div className="flex justify-end mb-6">
        {/* Sort dropdown */}
        <ReviewSortDropdown
          sortBy={sortBy}
          onSortChange={handleSortChange}
          className="sm:w-auto"
        />
      </div>



      {/* Error message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Loading state */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <ReviewCardSkeleton key={index} index={index} />
          ))}
        </div>
      ) : reviews.length > 0 ? (
        <>
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 gap-4"
          >
            {reviews.map((review) => {
              // Create the business_profiles object based on reviewer type
              const businessProfiles = {
                id: review.business_profile_id,
                business_name: review.reviewer_name || "Anonymous User",
                business_slug: review.reviewer_type === 'business' ? (review.reviewer_slug || null) : null,
                logo_url: review.reviewer_avatar || null
              };

              return (
                <ReviewCard
                  key={review.id}
                  review={{
                    ...review,
                    updated_at: review.updated_at || review.created_at,
                    business_profiles: businessProfiles
                  }}
                  // Don't allow editing/deleting in "Reviews Received" tab
                  onDeleteSuccess={null}
                  isReviewsReceivedTab={true}
                />
              );
            })}
          </motion.div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <Pagination className="mt-8">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (pagination.currentPage > 1) {
                        handlePageChange(pagination.currentPage - 1);
                      }
                    }}
                    className={pagination.currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>

                {renderPaginationItems()}

                <PaginationItem>
                  <PaginationNext
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (pagination.currentPage < pagination.totalPages) {
                        handlePageChange(pagination.currentPage + 1);
                      }
                    }}
                    className={pagination.currentPage === pagination.totalPages ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </>
      ) : (
        <Alert variant="default" className="bg-white dark:bg-black border-neutral-200 dark:border-neutral-800">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No reviews found</AlertTitle>
          <AlertDescription>
            Your business hasn&apos;t received any reviews yet.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
