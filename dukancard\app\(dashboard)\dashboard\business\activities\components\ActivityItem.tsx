"use client";

import { motion } from "framer-motion";
import { formatDistanceToNow } from "date-fns";
import { Heart, Star, Users, Building2 } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { BusinessActivity } from "@/lib/actions/activities";
import { useEffect, useRef } from "react";

interface ActivityItemProps {
  activity: BusinessActivity;
  onView?: (activityId: string) => void;
}

export default function ActivityItem({ activity, onView }: ActivityItemProps) {
  const itemRef = useRef<HTMLDivElement>(null);

  // Use intersection observer to track when item comes into view
  useEffect(() => {
    if (!onView || !itemRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            onView(activity.id);
            // Stop observing once viewed
            observer.unobserve(entry.target);
          }
        });
      },
      {
        threshold: 0.5, // Trigger when 50% of the item is visible
        rootMargin: '0px 0px -50px 0px' // Trigger a bit before fully visible
      }
    );

    observer.observe(itemRef.current);

    return () => {
      observer.disconnect();
    };
  }, [activity.id, onView]);

  // Ensure user_profile exists
  const userProfile = activity.user_profile || {};

  // Determine if the user is a business or customer
  const isBusiness = userProfile.is_business || false;

  // Get the appropriate name and avatar
  const displayName = isBusiness
    ? userProfile.business_name
    : userProfile.name;

  const avatarUrl = isBusiness
    ? userProfile.logo_url
    : userProfile.avatar_url;

  // Get initials for avatar fallback
  const getInitials = (name?: string | null) => {
    if (!name) return "?";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  const initials = getInitials(displayName);

  // Format the time
  const timeAgo = activity.created_at
    ? formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })
    : "recently";

  // Get activity icon and text
  const getActivityDetails = () => {
    switch (activity.activity_type) {
      case "like":
        return {
          icon: <Heart className="w-4 h-4 text-rose-500" />,
          text: "liked your business",
          color: "bg-rose-50 text-rose-700 dark:bg-rose-900/20 dark:text-rose-300",
        };
      case "subscribe":
        return {
          icon: <Users className="w-4 h-4 text-blue-500" />,
          text: "subscribed to your business",
          color: "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300",
        };
      case "rating":
        return {
          icon: <Star className="w-4 h-4 text-amber-500" />,
          text: `rated your business ${activity.rating_value}/5`,
          color: "bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300",
        };
      default:
        return {
          icon: <Star className="w-4 h-4" />,
          text: "interacted with your business",
          color: "bg-neutral-50 text-neutral-700 dark:bg-neutral-900/20 dark:text-neutral-300",
        };
    }
  };

  const { icon, text, color } = getActivityDetails();

  // Get profile link
  const profileLink = isBusiness && activity.user_profile?.business_slug
    ? `/${activity.user_profile.business_slug}`
    : "#";

  // Item animation variants
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      ref={itemRef}
      variants={itemVariants}
      className={cn(
        "relative rounded-lg border p-4 transition-all duration-300 overflow-hidden",
        activity.is_read
          ? "border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black"
          : "border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-900/10"
      )}
    >
      {/* Unread indicator */}
      {!activity.is_read && (
        <div className="absolute top-4 right-4 w-2 h-2 rounded-full bg-blue-500" />
      )}

      <div className="flex items-start gap-4">
        {/* Avatar - Only link for business users */}
        {isBusiness ? (
          <Link href={profileLink} className="shrink-0" target="_blank" rel="noopener noreferrer">
            <Avatar className="h-10 w-10 rounded-lg border border-amber-200 dark:border-amber-800">
              {avatarUrl ? (
                <AvatarImage src={avatarUrl} alt={displayName || "User"} />
              ) : null}
              <AvatarFallback className="rounded-lg text-xs bg-amber-50 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300">
                {initials}
              </AvatarFallback>
            </Avatar>
          </Link>
        ) : (
          <div className="shrink-0">
            <Avatar className="h-10 w-10 rounded-lg border border-neutral-200 dark:border-neutral-700">
              {avatarUrl ? (
                <AvatarImage src={avatarUrl} alt={displayName || "User"} />
              ) : null}
              <AvatarFallback className="rounded-lg text-xs bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300">
                {initials}
              </AvatarFallback>
            </Avatar>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex flex-col space-y-1 mb-1">
            <div className="flex items-center gap-1 flex-wrap">
              {/* Only link for business users */}
              {isBusiness ? (
                <Link
                  href={profileLink}
                  className="font-medium hover:underline truncate max-w-[200px]"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {displayName || "Anonymous User"}
                </Link>
              ) : (
                <span className="font-medium truncate max-w-[200px]">
                  {displayName || "Anonymous User"}
                </span>
              )}

              {/* Business badge */}
              {isBusiness && (
                <Badge variant="outline" className="bg-amber-50 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300 border-amber-200 dark:border-amber-800 flex items-center gap-1 py-0 h-5">
                  <Building2 className="w-3 h-3" />
                  <span className="text-xs">Business</span>
                </Badge>
              )}
            </div>

            <span className="text-neutral-500 dark:text-neutral-400 text-sm">
              {text}
            </span>
          </div>

          <div className="flex flex-wrap items-center justify-between mt-2 gap-2">
            <div className="flex items-center flex-wrap gap-2">
              <Badge variant="secondary" className={cn("flex items-center gap-1 whitespace-nowrap", color)}>
                {icon}
                <span className="text-xs capitalize">{activity.activity_type}</span>
              </Badge>

              <span className="text-xs text-neutral-500 dark:text-neutral-400 whitespace-nowrap">
                {timeAgo}
              </span>
            </div>

            {/* Removed individual mark as read button as we now auto-mark activities as read */}
          </div>
        </div>
      </div>
    </motion.div>
  );
}
