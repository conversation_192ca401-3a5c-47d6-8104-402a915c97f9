"use client";

import { useRef } from "react";
import { motion } from "framer-motion";
import { exampleCardData } from "./exampleCardData";
import CardBackground from "./CardBackground";
import EnhancedFeatureElements from "./EnhancedFeatureElements";
import EnhancedMetricsContainer from "./EnhancedMetricsContainer";
import EnhancedCardShowcase from "./EnhancedCardShowcase";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

interface DigitalCardFeatureProps {
  minimal?: boolean;
  hideTitle?: boolean;
  isAuthenticated?: boolean;
  userType?: "business" | "customer" | null;
  businessCardData?: BusinessCardData;
  userPlan?: "free" | "basic" | "growth" | "pro" | "enterprise" | "trial";
}

export default function DigitalCardFeature({
  minimal = false,
  hideTitle = false,
  isAuthenticated = false,
  userType = null,
  businessCardData,
  userPlan
}: DigitalCardFeatureProps) {
  // Ref for card
  const cardRef = useRef(null);

  // Determine which card data to use
  const cardData = (isAuthenticated && userType === "business" && businessCardData)
    ? businessCardData
    : exampleCardData;

  // Determine if this is a demo card
  const isDemo = !(isAuthenticated && userType === "business" && businessCardData);

  return (
    <div className={minimal ? "relative" : "relative w-full overflow-hidden py-16 md:py-20"}>
      {/* Background effects */}
      <CardBackground />

      <div className={minimal ? "" : "container mx-auto px-4 max-w-7xl"}>
        {/* Section title - only shown in full mode */}
        {!minimal && (
          <motion.div
            className="relative mb-8 md:mb-10 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7 }}
          >
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-center">
              Transform Your Business{" "}
              <span className="text-[var(--brand-gold)]">
                Digital Presence
              </span>
            </h2>

            {/* Subtle gradient line under heading */}
            <motion.div
              className="mt-3 mx-auto w-24 h-0.5 bg-gradient-to-r from-transparent via-[var(--brand-gold)]/50 to-transparent"
              initial={{ width: 0, opacity: 0 }}
              whileInView={{ width: "120px", opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 1, delay: 0.3 }}
            />
          </motion.div>
        )}

        {/* Minimal mode title */}
        {minimal && !hideTitle && (
          <div className="mb-6">
            <h2 className="text-2xl md:text-3xl font-bold mb-2">
              Get Your Business <span className="text-[var(--brand-gold)]">Online Instantly</span>
            </h2>
            <p className="text-neutral-600 dark:text-neutral-400 mb-4">
              Launch your digital storefront in <span className="font-semibold">just 30 seconds</span> — <span className="text-[var(--brand-gold)] font-semibold">completely FREE</span>
            </p>
          </div>
        )}

        {/* Card showcase with enhanced visuals */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: minimal ? 0.2 : 0.4 }}
          className="relative w-full max-w-4xl mx-auto flex justify-center"
        >
          <div ref={cardRef} className="relative w-full max-w-md">
            {/* Enhanced card component with dynamic demo mode */}
            <EnhancedCardShowcase
              cardData={cardData}
              isDemo={isDemo}
              isAuthenticated={isAuthenticated}
              userPlan={userPlan}
            />

            {/* Enhanced feature elements - visible in both modes */}
            <div className="absolute inset-0 pointer-events-none hidden md:block">
              <EnhancedFeatureElements minimal={minimal} />
            </div>
          </div>
        </motion.div>

        {/* Enhanced metrics container - now visible in both modes */}
        <motion.div
          className="mt-8 md:mt-10 w-full max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.4 }}
        >
          <EnhancedMetricsContainer
            likes={isDemo ? 50251 : (businessCardData?.total_likes ?? 0)}
            subscribers={isDemo ? 32053 : (businessCardData?.total_subscriptions ?? 0)}
            rating={isDemo ? 4.8 : (businessCardData?.average_rating ?? 0)}
            views={isDemo ? 1300000 : (businessCardData?.total_visits ?? 0)}
            minimal={minimal}
          />
        </motion.div>
      </div>
    </div>
  );
}
