"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, CreditCard } from "lucide-react";

interface SubscriptionTabsToggleProps {
  activeTab: string;
  onChange: (_tab: string) => void;
}

export default function SubscriptionTabsToggle({
  activeTab,
  onChange,
}: SubscriptionTabsToggleProps) {
  return (
    <div className="flex justify-center">
      <div className="relative inline-flex">
        {/* Background glow effect */}
        <div className="absolute -inset-1 bg-gradient-to-r from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/30 rounded-full blur-md" />

        <div className="space-x-2 bg-white/80 dark:bg-black/50 backdrop-blur-sm p-1.5 rounded-full border border-neutral-200/50 dark:border-neutral-800/50 inline-flex shadow-md relative z-10">
          <Button
            onClick={() => onChange("overview")}
            variant={activeTab === "overview" ? "default" : "ghost"}
            size="sm"
            className={`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
              activeTab === "overview"
                ? "bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md"
                : "text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"
            }`}
          >
            <span className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              <span className={activeTab === "overview" ? "font-medium" : "font-normal"}>
                Overview
              </span>
            </span>
          </Button>
          <Button
            onClick={() => onChange("payments")}
            variant={activeTab === "payments" ? "default" : "ghost"}
            size="sm"
            className={`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
              activeTab === "payments"
                ? "bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md"
                : "text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"
            }`}
          >
            <span className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              <span className={activeTab === "payments" ? "font-medium" : "font-normal"}>
                Payments
              </span>
            </span>
          </Button>
        </div>
      </div>
    </div>
  );
}
