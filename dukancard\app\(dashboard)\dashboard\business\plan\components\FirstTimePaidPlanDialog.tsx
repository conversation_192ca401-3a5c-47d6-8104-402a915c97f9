"use client";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Di<PERSON>Footer,
  <PERSON><PERSON>Title,
  DialogHeader,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  Gift,
  Calendar,
  Sparkles,
  CheckCircle,
} from "lucide-react";
import { PricingPlan } from "@/lib/PricingPlans";
import { cn } from "@/lib/utils";
import { useSubscriptionProcessing } from "../context/SubscriptionProcessingContext";
import { Badge } from "@/components/ui/badge";
import DialogBackground from "./DialogBackground";
import { format, addMonths } from "date-fns";

interface FirstTimePaidPlanDialogProps {
  isOpen: boolean;
  onClose: () => void;
  plan: PricingPlan;
  onActivateTrial: () => Promise<void>;
  isLoading?: boolean;
  billingCycle?: "monthly" | "yearly";
}

export default function FirstTimePaidPlanDialog({
  isOpen,
  onClose,
  plan,
  onActivateTrial,
  isLoading: externalLoading,
  billingCycle = "monthly", // Default to monthly if not provided
}: FirstTimePaidPlanDialogProps) {
  // Get the resetProcessing function from the subscription processing context
  const { resetProcessing } = useSubscriptionProcessing();

  // State for client-side rendering check
  const [isClient, setIsClient] = useState(false);

  // Use internal loading state only if external loading is not provided
  const [internalLoading, setInternalLoading] = useState(false);

  // Calculate trial end date (1 month from now)
  const trialEndDate = addMonths(new Date(), 1);
  const formattedTrialEndDate = format(trialEndDate, "dd MMM yyyy");

  // Combined loading state
  const isLoading = externalLoading !== undefined ? externalLoading : internalLoading;

  // Animation variants
  const dialogVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
  };

  const buttonVariants = {
    hover: { scale: 1.03 },
    tap: { scale: 0.98 },
  };

  // Set isClient to true on mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleActivateTrial = async () => {
    try {
      // Only set internal loading if we're not using external loading
      if (externalLoading === undefined) {
        setInternalLoading(true);
      }

      await onActivateTrial();

      // Only reset internal loading if we're not using external loading
      if (externalLoading === undefined) {
        setInternalLoading(false);
      }
    } catch (error) {
      console.error("Error activating trial:", error);
      // Only reset internal loading if we're not using external loading
      if (externalLoading === undefined) {
        setInternalLoading(false);
      }
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          // Reset internal loading state when dialog is closed
          if (externalLoading === undefined) {
            setInternalLoading(false);
          }
          // Reset processing state to ensure no lingering toast notifications
          resetProcessing();
          onClose();
        }
      }}
    >
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={dialogVariants}
          className="relative overflow-hidden"
        >
          {/* Background gradient effect */}
          {isClient && <DialogBackground variant="gold" intensity="medium" />}

          {/* Header with plan name and icon */}
          <DialogHeader className="p-6 pb-0 z-10 relative">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-full bg-[var(--brand-gold)]/15 text-[var(--brand-gold)]">
                <Gift className="w-5 h-5" />
              </div>
              <Badge
                variant="outline"
                className="border-[var(--brand-gold)]/50 text-[var(--brand-gold)] px-2 py-0.5"
              >
                Special Offer
              </Badge>
            </div>
            <DialogTitle className="text-2xl font-bold">
              1 Month Free Trial
            </DialogTitle>
          </DialogHeader>

          {/* Content section */}
          <div className="p-6 space-y-4">
            {/* Plan details with animation */}
            <motion.div variants={itemVariants} className="space-y-2">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-full bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]">
                  <Sparkles className="w-5 h-5" />
                </div>
                <div>
                  <h3 className="font-semibold">{plan.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {plan.price}{plan.period}
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-blue-950/30 dark:to-purple-950/30 p-4 rounded-lg border border-blue-200/50 dark:border-blue-800/30 space-y-3">
                <div className="flex items-start gap-2">
                  <Gift className="w-5 h-5 text-[var(--brand-gold)] mt-0.5 flex-shrink-0" />
                  <p className="text-sm">
                    As a first-time paid subscriber, you&apos;ll get a <span className="font-semibold text-[var(--brand-gold)]">1-month free trial</span> of the {plan.name}. No payment required now!
                  </p>
                </div>

                {billingCycle === "yearly" && (
                  <div className="flex items-start gap-2 bg-amber-50 dark:bg-amber-950/30 border border-amber-200/50 dark:border-amber-800/30 p-3 rounded-lg text-sm text-amber-800 dark:text-amber-300">
                    <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                    <p>
                      Your trial will be for the monthly plan. After your trial ends, you can switch to the yearly plan if you prefer.
                    </p>
                  </div>
                )}

                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <span>
                    Trial ends on <span className="font-medium">{formattedTrialEndDate}</span>
                  </span>
                </div>
              </div>
            </motion.div>

            {/* What happens next */}
            <motion.div variants={itemVariants} className="bg-muted/30 p-4 rounded-lg border border-neutral-200 dark:border-neutral-800">
              <p className="text-sm text-muted-foreground">
                After your trial ends, you&apos;ll need to add a payment method to continue using the {plan.name}.
                {billingCycle === "yearly" ?
                  " You can choose to continue with the monthly plan or switch to the yearly plan at that time." :
                  " We'll remind you before your trial expires."}
              </p>
            </motion.div>
          </div>

          {/* Footer with buttons */}
          <motion.div variants={itemVariants}>
            <DialogFooter className="flex flex-col-reverse sm:flex-row gap-3 p-6 pt-4 border-t border-neutral-200 dark:border-neutral-800 bg-neutral-50 dark:bg-neutral-950">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="w-full sm:w-auto py-6 rounded-xl transition-all duration-200"
                >
                  Cancel
                </Button>
              </motion.div>

              <motion.div
                whileHover="hover"
                whileTap="tap"
                variants={buttonVariants}
                className="w-full sm:w-auto"
              >
                <Button
                  onClick={handleActivateTrial}
                  className={cn(
                    "w-full relative overflow-hidden py-6",
                    "bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90",
                    "text-black dark:text-neutral-900 font-medium transition-all duration-200",
                    "shadow-[0_0_15px_rgba(var(--brand-gold-rgb),0.5)] hover:shadow-[0_0_20px_rgba(var(--brand-gold-rgb),0.7)]",
                    "rounded-xl"
                  )}
                  disabled={isLoading}
                >
                  {/* Shimmer effect */}
                  {isClient && !isLoading && (
                    <motion.div
                      className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none"
                      initial={{ x: "-100%" }}
                      animate={{ x: "100%" }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    />
                  )}

                  {/* Background glow effect */}
                  {isClient && !isLoading && (
                    <div className="absolute inset-0 w-full h-full opacity-75 pointer-events-none">
                      <div className="absolute inset-0 bg-[var(--brand-gold)]/20 blur-md rounded-xl" />
                    </div>
                  )}

                  {isLoading ? (
                    <div className="flex items-center justify-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Activating...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <Gift className="w-4 h-4" />
                      <span>Activate Free Trial</span>
                    </div>
                  )}
                </Button>
              </motion.div>
            </DialogFooter>
          </motion.div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
