"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2, Image as ImageIcon, Check } from "lucide-react";
import Image from "next/image";
import { StorageImage, getBusinessImageLibrary } from "../actions/image-library";
import { cn } from "@/lib/utils";

interface ImageLibraryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectImage: (_imageUrl: string) => void;
}

export default function ImageLibraryDialog({
  isOpen,
  onClose,
  onSelectImage,
}: ImageLibraryDialogProps) {
  const [images, setImages] = useState<StorageImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSelecting, setIsSelecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadImages();
    } else {
      // Reset state when dialog closes
      setSelectedImageUrl(null);
    }
  }, [isOpen]);

  const loadImages = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log("Loading image library...");
      const result = await getBusinessImageLibrary(50);

      if (result.error) {
        console.error("Error from getBusinessImageLibrary:", result.error);
        setError(result.error);
      } else {
        console.log(`Loaded ${result.images.length} images from library`);
        setImages(result.images);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      console.error("Error loading image library:", errorMessage, err);
      setError(`Failed to load images: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectImage = (imageUrl: string) => {
    setSelectedImageUrl(imageUrl);
  };

  const handleConfirmSelection = () => {
    if (selectedImageUrl) {
      try {
        setIsSelecting(true);
        onSelectImage(selectedImageUrl);
        onClose();
      } catch (error) {
        console.error("Error selecting image:", error);
        setError("Failed to select image. Please try again.");
      } finally {
        setIsSelecting(false);
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold flex items-center gap-2">
            <ImageIcon className="h-5 w-5 text-primary" />
            Image Library
          </DialogTitle>
          <DialogDescription>
            Select an image from your previously uploaded images
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-sm font-medium">Your Image Library</h3>
            <Button variant="outline" size="sm" onClick={loadImages} disabled={isLoading}>
              {isLoading ? <Loader2 className="h-3.5 w-3.5 mr-1 animate-spin" /> : null}
              Refresh
            </Button>
          </div>

          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="h-8 w-8 text-primary animate-spin mb-4" />
              <p className="text-sm text-neutral-500">Loading your images...</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-12">
              <p className="text-sm text-red-500 mb-2">{error}</p>
              <Button variant="outline" size="sm" onClick={loadImages}>
                Try Again
              </Button>
            </div>
          ) : images.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <ImageIcon className="h-12 w-12 text-neutral-300 dark:text-neutral-600 mb-4" />
              <h3 className="text-lg font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                No images found
              </h3>
              <p className="text-sm text-neutral-500 dark:text-neutral-400 max-w-md">
                You haven&apos;t uploaded any images yet. Upload images to your products to see them here.
              </p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {images.map((image) => (
                  <div
                    key={image.id}
                    className={cn(
                      "relative aspect-square rounded-md overflow-hidden border-2 cursor-pointer group",
                      selectedImageUrl === image.url
                        ? "border-primary ring-2 ring-primary/20"
                        : "border-neutral-200 dark:border-neutral-800 hover:border-primary/50"
                    )}
                    onClick={() => handleSelectImage(image.url)}
                  >
                    <Image
                      src={image.url}
                      alt={image.name}
                      fill
                      className="object-cover transition-all group-hover:scale-105"
                    />

                    {/* Image name tooltip */}
                    <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 truncate opacity-0 group-hover:opacity-100 transition-opacity">
                      {image.name}
                    </div>

                    {/* Selection indicator */}
                    {selectedImageUrl === image.url && (
                      <div className="absolute inset-0 bg-primary/10 flex items-center justify-center">
                        <div className="bg-primary text-white p-1 rounded-full">
                          <Check className="h-5 w-5" />
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Load more button - for future pagination implementation */}
              {images.length >= 50 && (
                <div className="flex justify-center mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => console.log("Load more images")}
                    disabled={true} // Disabled until we implement pagination
                  >
                    Load More Images
                  </Button>
                </div>
              )}

              <div className="flex justify-between space-x-2 pt-4 border-t border-neutral-200 dark:border-neutral-800">
                <div className="text-xs text-neutral-500">
                  {images.length} images found
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={onClose} disabled={isSelecting}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleConfirmSelection}
                    disabled={!selectedImageUrl || isSelecting}
                  >
                    {isSelecting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      "Use Selected Image"
                    )}
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
