"use client";

import { motion } from "framer-motion";
import { Mail, Phone, MapPin, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import Link from "next/link";
import { siteConfig } from "@/lib/site-config";

interface PolicyCTAProps {
  relatedLinks?: Array<{
    title: string;
    href: string;
  }>;
}

export default function PolicyCTASection({ relatedLinks = [] }: PolicyCTAProps) {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  const iconVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: { duration: 0.3, type: "spring", stiffness: 200 },
    },
  };

  return (
    <motion.section
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.3 }}
      className="py-12 md:py-16"
    >
      <div className="container mx-auto px-4 max-w-4xl">
        <motion.h2
          variants={itemVariants}
          className="text-2xl md:text-3xl font-bold text-center mb-8"
        >
          Have Questions About Our Policies?
        </motion.h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
          {/* Email Card */}
          <motion.div variants={itemVariants}>
            <Card className="p-6 h-full flex flex-col items-center text-center hover:shadow-md transition-shadow duration-300">
              <motion.div
                variants={iconVariants}
                className="mb-4 p-3 rounded-full bg-[var(--brand-gold)]/10"
              >
                <Mail className="h-6 w-6 text-[var(--brand-gold)]" />
              </motion.div>
              <h3 className="font-medium mb-2">Email Us</h3>
              <p className="text-muted-foreground mb-4 text-sm">
                Send us your questions
              </p>
              <a
                href={`mailto:${siteConfig.contact.email}`}
                className="text-[var(--brand-gold)] hover:underline mt-auto"
              >
                {siteConfig.contact.email}
              </a>
            </Card>
          </motion.div>

          {/* Phone Card */}
          <motion.div variants={itemVariants}>
            <Card className="p-6 h-full flex flex-col items-center text-center hover:shadow-md transition-shadow duration-300">
              <motion.div
                variants={iconVariants}
                className="mb-4 p-3 rounded-full bg-[var(--brand-gold)]/10"
              >
                <Phone className="h-6 w-6 text-[var(--brand-gold)]" />
              </motion.div>
              <h3 className="font-medium mb-2">Call Us</h3>
              <p className="text-muted-foreground mb-4 text-sm">
                Speak to our support team
              </p>
              <a
                href={`tel:${siteConfig.contact.phone}`}
                className="text-[var(--brand-gold)] hover:underline mt-auto"
              >
                {siteConfig.contact.phone}
              </a>
            </Card>
          </motion.div>

          {/* Address Card */}
          <motion.div variants={itemVariants}>
            <Card className="p-6 h-full flex flex-col items-center text-center hover:shadow-md transition-shadow duration-300">
              <motion.div
                variants={iconVariants}
                className="mb-4 p-3 rounded-full bg-[var(--brand-gold)]/10"
              >
                <MapPin className="h-6 w-6 text-[var(--brand-gold)]" />
              </motion.div>
              <h3 className="font-medium mb-2">Visit Us</h3>
              <p className="text-muted-foreground mb-4 text-sm">
                Our office location
              </p>
              <span className="text-sm mt-auto">
                {siteConfig.contact.address.full}
              </span>
            </Card>
          </motion.div>
        </div>

        {/* Related Links */}
        {relatedLinks.length > 0 && (
          <motion.div variants={itemVariants} className="mt-12">
            <h3 className="text-xl font-semibold text-center mb-6">
              Related Policies
            </h3>
            <div className="flex flex-wrap justify-center gap-4">
              {relatedLinks.map((link, index) => (
                <Button
                  key={index}
                  variant="outline"
                  asChild
                  className="group"
                >
                  <Link href={link.href}>
                    {link.title}
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                  </Link>
                </Button>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </motion.section>
  );
}
