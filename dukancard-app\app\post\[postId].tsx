import React from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import SinglePostScreen from '@/components/post/SinglePostScreen';

export default function SinglePostPage() {
  const { postId } = useLocalSearchParams<{ postId: string }>();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Theme colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const statusBarStyle = isDark ? 'light-content' : 'dark-content';

  // Validate postId parameter
  if (!postId || typeof postId !== 'string') {
    // Handle invalid postId - could redirect to error screen
    router.replace('/');
    return null;
  }

  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      // Fallback navigation
      router.replace('/');
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Post',
          headerShown: true,
          headerStyle: {
            backgroundColor,
          },
          headerShadowVisible: false,
          headerTintColor: textColor,
          headerTitleStyle: {
            color: textColor,
            fontWeight: '600',
          },
          headerLeft: () => (
            <TouchableOpacity
              onPress={handleBack}
              style={styles.backButton}
              activeOpacity={0.7}
            >
              <ArrowLeft size={24} color={textColor} />
            </TouchableOpacity>
          ),
        }}
      />
      <StatusBar barStyle={statusBarStyle} backgroundColor={backgroundColor} />
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <SinglePostScreen postId={postId} />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
});
