'use client';

import { useState } from 'react';
import LikeCardClient from './LikeCardClient'; // Import the card component

// Re-define interfaces here as they are needed by the client component props
interface BusinessProfileData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
}

interface LikeWithProfile {
  id: string;
  business_profiles: BusinessProfileData | null;
}

interface LikeListClientProps {
  initialLikes: LikeWithProfile[];
}

export default function LikeListClient({ initialLikes }: LikeListClientProps) {
  const [likes, setLikes] = useState<LikeWithProfile[]>(initialLikes);

  // Handle successful unlike
  const handleUnlikeSuccess = (likeId: string) => {
    setLikes((prevLikes) => prevLikes.filter((like) => like.id !== likeId));
  };

  // If there are no likes, show a message
  if (likes.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-neutral-500 dark:text-neutral-400">
          You haven&apos;t liked any businesses yet.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {likes.map((like) => {
        const profile = like.business_profiles;
        if (!profile) return null; // Should not happen if data is clean, but good practice

        return (
          <LikeCardClient
            key={like.id}
            likeId={like.id}
            profile={profile}
            onUnlikeSuccess={handleUnlikeSuccess}
          />
        );
      })}
    </div>
  );
}
