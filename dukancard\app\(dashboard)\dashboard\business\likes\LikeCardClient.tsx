'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Heart, Loader2 } from 'lucide-react';
import { unlikeBusiness } from '@/lib/actions/interactions'; // Import server action
import { toast } from 'sonner';

interface BusinessProfileData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
}

interface LikeCardClientProps {
  likeId: string;
  profile: BusinessProfileData;
  onUnlikeSuccess: (_likeId: string) => void; // Callback to remove item from list
}

export default function LikeCardClient({
  likeId,
  profile,
  onUnlikeSuccess,
}: LikeCardClientProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Handle unlike action
  const handleUnlike = async () => {
    setIsLoading(true);
    try {
      const result = await unlikeBusiness(profile.id);
      if (result.success) {
        toast.success('Business unliked successfully');
        onUnlikeSuccess(likeId);
      } else {
        toast.error(result.error || 'Failed to unlike business');
      }
    } catch (error) {
      console.error('Error unliking business:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Get business initials for avatar fallback
  const getInitials = (name: string | null) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format location
  const formatLocation = (city: string | null, state: string | null) => {
    if (city && state) return `${city}, ${state}`;
    if (city) return city;
    if (state) return state;
    return 'Location not specified';
  };

  return (
    <div className="bg-white dark:bg-neutral-900 rounded-lg border border-neutral-200 dark:border-neutral-800 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200">
      <div className="p-4">
        <div className="flex items-center gap-3 mb-3">
          <Avatar className="h-10 w-10 border border-[var(--brand-gold)]/30">
            {profile.logo_url ? (
              <AvatarImage src={profile.logo_url} alt={profile.business_name || 'Business'} />
            ) : null}
            <AvatarFallback className="bg-primary/10 text-primary border border-[var(--brand-gold)]/30">
              {getInitials(profile.business_name)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-neutral-900 dark:text-neutral-100 truncate">
              {profile.business_name || 'Unnamed Business'}
            </h3>
            <p className="text-xs text-neutral-500 dark:text-neutral-400 truncate">
              {formatLocation(profile.city, profile.state)}
            </p>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <Button
            asChild
            variant="outline"
            size="sm"
            className="text-xs h-8"
          >
            <Link href={`/${profile.business_slug}`}>
              Visit Card
            </Link>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="text-xs h-8 text-rose-500 hover:text-rose-600 hover:bg-rose-50 dark:hover:bg-rose-900/20"
            onClick={handleUnlike}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-3.5 w-3.5 mr-1.5 animate-spin" />
            ) : (
              <Heart className="h-3.5 w-3.5 mr-1.5 fill-current" />
            )}
            Unlike
          </Button>
        </div>
      </div>
    </div>
  );
}
