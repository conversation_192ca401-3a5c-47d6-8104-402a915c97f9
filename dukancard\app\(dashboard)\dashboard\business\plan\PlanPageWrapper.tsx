"use client";

import React from "react";
import { SubscriptionProcessingProvider } from "./context/SubscriptionProcessingContext";
import { ToastProvider } from "@/components/ui/toast";
import RealtimePlanPageClient from "./components/RealtimePlanPageClient";

// Import the types we need
import { SubscriptionStatus } from "./page";
import { PricingPlan } from "@/lib/PricingPlans";

interface PlanPageWrapperProps {
  userId: string;
  currentPlanDetails?: PricingPlan;
  subscriptionStatus: SubscriptionStatus;
  trialEndDate: string | null;
  subscriptionEndDate: string | null;
  monthlyPlans: PricingPlan[];
  yearlyPlans: PricingPlan[];
  currentSubscriptionId: string | null;
  nextBillingDate: string | null;
  cancellationRequestedAt: string | null;
  cancelledAt: string | null;
  planCycle: "monthly" | "yearly";
  authenticatedSubscriptionStartDate?: string | null;
  // New props for subscription dates from Supabase
  subscriptionStartDate?: string | null;
  subscriptionExpiryTime?: string | null;
  subscriptionChargeTime?: string | null;
  isEligibleForRefund?: boolean;
  // Payment method
  lastPaymentMethod?: string | null;
  // Razorpay subscription data to determine switch vs fresh subscription
  razorpaySubscriptionId?: string | null;
}

export default function PlanPageWrapper(
  props: PlanPageWrapperProps
) {
  // Use the realtime plan page client which adds real-time subscription updates
  return (
    <ToastProvider>
      <SubscriptionProcessingProvider>
        <RealtimePlanPageClient {...props} />
      </SubscriptionProcessingProvider>
    </ToastProvider>
  );
}
