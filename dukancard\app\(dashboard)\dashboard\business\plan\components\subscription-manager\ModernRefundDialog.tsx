"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  RefreshCw,
  Shield,
  // Unused import removed: AlertCircle,
  CheckCircle,
} from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import DialogBackground from "@/app/(dashboard)/dashboard/business/plan/components/DialogBackground";
import EnhancedGlowButton from "../EnhancedGlowButton";
// Unused import removed: import { cn } from "@/lib/utils";

interface ModernRefundDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onRequestRefund: (_speed: "normal" | "optimum") => Promise<void>;
  isLoading: boolean;
  isEligibleForRefund: boolean;
}

export default function ModernRefundDialog({
  isOpen,
  onClose,
  onRequestRefund,
  isLoading,
  isEligibleForRefund,
}: ModernRefundDialogProps) {
  // State for client-side rendering check
  const [isClient, setIsClient] = useState(false);
  const [refundSpeed, setRefundSpeed] = useState<"normal" | "optimum">("normal");

  // Animation variants
  const dialogVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
  };

  // Unused variable removed
  const _buttonVariants = {
    hover: { scale: 1.03 },
    tap: { scale: 0.98 },
  };

  // Set isClient to true on mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Handle request refund
  const handleRequestRefund = async () => {
    await onRequestRefund(refundSpeed);
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          onClose();
        }
      }}
    >
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={dialogVariants}
          className="relative overflow-hidden"
        >
          {/* Background gradient effect */}
          {isClient && <DialogBackground variant="gold" intensity="medium" />}

          {/* Header with icon */}
          <DialogHeader className="p-6 pb-2 z-10 relative">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-full bg-purple-500/15 text-purple-500">
                <RefreshCw className="w-5 h-5" />
              </div>
              <Badge
                variant="outline"
                className="border-purple-500/50 text-purple-500 px-2 py-0.5"
              >
                {isEligibleForRefund ? "Payment Issue" : "Refund Request"}
              </Badge>
            </div>
            <DialogTitle className="text-2xl font-bold">
              Request Refund
            </DialogTitle>
          </DialogHeader>

          {/* Content section */}
          <div className="p-6 pt-2 space-y-4 z-10 relative">
            {/* Description with animation */}
            <motion.p
              variants={itemVariants}
              className="text-muted-foreground"
            >
              {isEligibleForRefund
                ? "You are eligible for a refund due to a payment issue or subscription problem. Your subscription will be cancelled immediately if the refund is approved."
                : "Request a refund for your subscription. Note that refunds are only processed for payment issues or subscription problems."}
            </motion.p>

            {/* Alert with refund information */}
            <motion.div variants={itemVariants}>
              <Alert className="bg-white/50 dark:bg-black/50 border border-neutral-200 dark:border-neutral-800">
                <Shield className="h-4 w-4 text-[var(--brand-gold)]" />
                <AlertTitle className="font-medium">Refund Information</AlertTitle>
                <AlertDescription className="text-sm">
                  <p className="mb-2">
                    {isEligibleForRefund
                      ? "Your full refund will be processed back to your original payment method and your subscription will be cancelled immediately."
                      : "Your refund request will be reviewed. If approved, it will be processed back to your original payment method."}
                    {" "}Processing time depends on your bank and the refund speed selected.
                  </p>
                </AlertDescription>
              </Alert>
            </motion.div>

            {/* Refund speed selection */}
            <motion.div variants={itemVariants} className="space-y-3">
              <h4 className="text-sm font-medium">Select Refund Speed</h4>
              <RadioGroup
                value={refundSpeed}
                onValueChange={(value) => setRefundSpeed(value as "normal" | "optimum")}
                className="space-y-2"
              >
                <div className="flex items-center space-x-2 p-3 rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80 transition-colors">
                  <RadioGroupItem value="normal" id="normal" />
                  <Label htmlFor="normal" className="flex flex-col cursor-pointer">
                    <span className="font-medium">Normal</span>
                    <span className="text-xs text-muted-foreground">Processed within 5-7 business days</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2 p-3 rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white/50 dark:bg-black/50 hover:bg-neutral-50 dark:hover:bg-neutral-900/80 transition-colors">
                  <RadioGroupItem value="optimum" id="optimum" />
                  <Label htmlFor="optimum" className="flex flex-col cursor-pointer">
                    <span className="flex items-center font-medium">
                      Instant
                      <span className="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300">Recommended</span>
                    </span>
                    <span className="text-xs text-muted-foreground">Processed immediately when possible, otherwise normal speed</span>
                  </Label>
                </div>
              </RadioGroup>
            </motion.div>
          </div>

          {/* Footer with buttons */}
          <DialogFooter className="p-6 pt-2 flex flex-col sm:flex-row gap-2 sm:justify-between">
            <motion.div
              variants={itemVariants}
              className="w-full sm:w-auto"
            >
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="w-full sm:w-auto border-neutral-200 dark:border-neutral-800"
              >
                Cancel
              </Button>
            </motion.div>
            <motion.div
              variants={itemVariants}
              className="w-full sm:w-auto"
            >
              <EnhancedGlowButton
                onClick={handleRequestRefund}
                disabled={isLoading}
                className="w-full sm:w-auto"
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Request Refund
                  </span>
                )}
              </EnhancedGlowButton>
            </motion.div>
          </DialogFooter>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
