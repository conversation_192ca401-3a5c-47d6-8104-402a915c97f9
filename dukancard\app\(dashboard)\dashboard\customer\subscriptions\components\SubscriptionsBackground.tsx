"use client";

import { useEffect, useState } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

export default function SubscriptionsBackground() {
  const [isClient, setIsClient] = useState(false);
  const isMobile = useIsMobile();

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none z-0">
      {/* Simple gradient background */}
      {isClient && (
        <div
          className="absolute inset-0 opacity-30 dark:opacity-20"
          style={{
            background: `radial-gradient(circle at 50% 50%,
              rgba(59, 130, 246, 0.3) 0%,
              rgba(59, 130, 246, 0.2) 25%,
              rgba(59, 130, 246, 0.1) 50%,
              rgba(59, 130, 246, 0.05) 75%,
              rgba(59, 130, 246, 0.02) 100%)`,
            filter: isMobile ? "blur(60px)" : "blur(80px)",
            transformOrigin: "center",
          }}
        />
      )}

      {/* Simple overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-blue-500/5 dark:from-neutral-900/5 dark:to-blue-500/10" />
    </div>
  );
}
