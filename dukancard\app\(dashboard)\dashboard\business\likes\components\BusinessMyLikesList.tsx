'use client';

import { useMemo } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Compass } from 'lucide-react';
import { LikeCard, LikeData } from '@/app/components/shared/likes';
import { BusinessMyLike } from '../actions';

interface BusinessMyLikesListProps {
  initialLikes: BusinessMyLike[];
}

export default function BusinessMyLikesList({ initialLikes }: BusinessMyLikesListProps) {
  // Transform the data to match the shared component interface
  const transformedLikes: LikeData[] = useMemo(() => {
    return initialLikes.map(like => ({
      id: like.id,
      profile: like.business_profiles ? {
        id: like.business_profiles.id,
        name: like.business_profiles.business_name,
        slug: like.business_profiles.business_slug,
        logo_url: like.business_profiles.logo_url,
        city: like.business_profiles.city,
        state: like.business_profiles.state,
        pincode: like.business_profiles.pincode,
        address_line: like.business_profiles.address_line,
        type: 'business' as const,
      } : null
    })).filter(like => like.profile !== null) as LikeData[];
  }, [initialLikes]);

  // If there are no likes, show empty state
  if (transformedLikes.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <div className="mb-4">
            <div className="w-16 h-16 mx-auto bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center">
              <span className="text-2xl">💝</span>
            </div>
          </div>
          <h3 className="text-lg font-medium text-neutral-800 dark:text-neutral-100 mb-2">
            You haven&apos;t liked any businesses yet
          </h3>
          <p className="text-neutral-500 dark:text-neutral-400 mb-6">
            Like businesses to see them here and show your support.
          </p>
          <Button asChild variant="outline" className="gap-2">
            <Link href="/businesses" target="_blank" rel="noopener noreferrer">
              <Compass className="w-4 h-4" />
              Discover Businesses
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {transformedLikes.map((like, _index) => {
        const profile = like.profile;

        if (!profile) {
          return null; // Skip items with missing profiles
        }

        return (
          <LikeCard
            key={like.id}
            likeId={like.id}
            profile={profile}
            showUnlike={true} // Show unlike for business's own likes
            variant="default"
            showVisitButton={true} // Show visit button for my likes
            showAddress={true} // Show complete address for my likes
            showRedirectIcon={false} // Don't show redirect icon in my likes
          />
        );
      })}
    </div>
  );
}
