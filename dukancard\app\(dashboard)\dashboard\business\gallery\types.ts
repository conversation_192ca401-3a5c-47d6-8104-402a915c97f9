export interface GalleryImage {
  id: string;
  url: string; // Public URL for display
  path: string; // Storage path
  created_at: string;
}

export interface UploadGalleryImageResponse {
  success: boolean;
  image?: GalleryImage;
  error?: string;
}

export interface DeleteGalleryImageResponse {
  success: boolean;
  error?: string;
  warning?: string;
}

export interface GetGalleryImagesResponse {
  images: GalleryImage[];
  error?: string;
}
