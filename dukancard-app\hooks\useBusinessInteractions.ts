import { useState, useEffect } from 'react';
import {
  getBusinessInteractionStatus,
  toggleBusinessLike,
  toggleBusinessSubscription,
  BusinessInteractionStatus
} from '../lib/services/businessInteractions';
import { supabase } from '../lib/supabase';
import { Toast } from '../lib/utils/toast';

export const useBusinessInteractions = (businessId: string) => {
  const [interactionStatus, setInteractionStatus] = useState<BusinessInteractionStatus | null>(null);
  const [isOwner, setIsOwner] = useState(false);
  const [likeLoading, setLikeLoading] = useState(false);
  const [subscribeLoading, setSubscribeLoading] = useState(false);

  // Load interaction status and check ownership
  useEffect(() => {
    const loadInteractionStatus = async () => {
      if (!businessId) return;

      try {
        // Get current user
        const { data: { user }, error: authError } = await supabase.auth.getUser();

        if (authError || !user) {
          setIsOwner(false);
          return;
        }

        // Check if current user owns this business by comparing user.id with business.id
        // In our system, business owners have user.id === business.id
        const isBusinessOwner = user.id === businessId;
        setIsOwner(isBusinessOwner);

        // Only load interaction status if user is not the owner
        if (!isBusinessOwner) {
          const statusResult = await getBusinessInteractionStatus(businessId);
          if (statusResult.success && statusResult.data) {
            setInteractionStatus(statusResult.data);
          }
        }
      } catch (error) {
        console.error('Error loading interaction status:', error);
        setIsOwner(false);
      }
    };

    loadInteractionStatus();
  }, [businessId]);

  // Handle like button press
  const handleLikePress = async () => {
    if (isOwner) {
      Toast.show('You cannot like your own business', 'error');
      return;
    }

    if (likeLoading) return;

    setLikeLoading(true);
    try {
      const result = await toggleBusinessLike(businessId);

      if (result.success) {
        Toast.show(result.message, 'success');
        // Refresh interaction status
        const statusResult = await getBusinessInteractionStatus(businessId);
        if (statusResult.success && statusResult.data) {
          setInteractionStatus(statusResult.data);
        }
      } else {
        Toast.show(result.message, 'error');
      }
    } catch (error) {
      Toast.show('Failed to update like status', 'error');
    } finally {
      setLikeLoading(false);
    }
  };

  // Handle subscribe button press
  const handleSubscribePress = async () => {
    if (isOwner) {
      Toast.show('You cannot subscribe to your own business', 'error');
      return;
    }

    if (subscribeLoading) return;

    setSubscribeLoading(true);
    try {
      const result = await toggleBusinessSubscription(businessId);

      if (result.success) {
        Toast.show(result.message, 'success');
        // Refresh interaction status
        const statusResult = await getBusinessInteractionStatus(businessId);
        if (statusResult.success && statusResult.data) {
          setInteractionStatus(statusResult.data);
        }
      } else {
        Toast.show(result.message, 'error');
      }
    } catch (error) {
      Toast.show('Failed to update subscription status', 'error');
    } finally {
      setSubscribeLoading(false);
    }
  };

  // Handle review button press
  const handleReviewPress = (onReviewModalOpen: () => void) => {
    if (isOwner) {
      Toast.show('You cannot review your own business', 'error');
      return;
    }

    onReviewModalOpen();
  };

  // Handle review submitted
  const handleReviewSubmitted = async () => {
    // Refresh interaction status to get updated review data
    const statusResult = await getBusinessInteractionStatus(businessId);
    if (statusResult.success && statusResult.data) {
      setInteractionStatus(statusResult.data);
    }
  };

  return {
    interactionStatus,
    isOwner,
    likeLoading,
    subscribeLoading,
    handleLikePress,
    handleSubscribePress,
    handleReviewPress,
    handleReviewSubmitted,
  };
};
