"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { AlertTriangle, Save, X, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { UseFormReturn } from "react-hook-form";
import { BusinessCardData } from "../schema";

interface UnsavedChangesReminderProps {
  form: UseFormReturn<BusinessCardData>;
  isPending: boolean;
  isLogoUploading: boolean;
  isCheckingSlug?: boolean;
  isPincodeLoading?: boolean;
  onSave: () => void;
  onDiscard: () => void;
}

export default function UnsavedChangesReminder({
  form,
  isPending,
  isLogoUploading,
  isCheckingSlug = false,
  isPincodeLoading = false,
  onSave,
  onDiscard,
}: UnsavedChangesReminderProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);

  // Track when form is properly initialized
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialized(true);
    }, 5000); // 5 second delay to allow form initialization to complete

    return () => clearTimeout(timer);
  }, []);

  // Track form state changes to detect user interaction
  useEffect(() => {
    if (!isInitialized) return;

    // Only mark as user interacted if form becomes dirty after initialization
    if (form.formState.isDirty && !hasUserInteracted) {
      // Add a small delay to ensure this isn't from initial form setup
      const timer = setTimeout(() => {
        if (form.formState.isDirty) {
          setHasUserInteracted(true);
        }
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [form.formState.isDirty, isInitialized, hasUserInteracted]);

  // Reset user interaction flag when form is submitted successfully
  useEffect(() => {
    if (!form.formState.isDirty) {
      setHasUserInteracted(false);
    }
  }, [form.formState.isDirty]);

  const hasUnsavedChanges = isInitialized && hasUserInteracted && form.formState.isDirty;

  return (
    <AnimatePresence>
      {hasUnsavedChanges && (
        <motion.div
          initial={{ opacity: 0, y: -100 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -100 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[calc(100%-2rem)] max-w-md"
        >
          <div className="bg-white dark:bg-neutral-900 border border-amber-200 dark:border-amber-800 rounded-xl shadow-xl backdrop-blur-sm p-4 ring-1 ring-black/5 dark:ring-white/10">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-5 w-5 text-amber-500" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                    You have unsaved changes
                  </p>
                  <p className="text-xs text-neutral-600 dark:text-neutral-400">
                    Don&apos;t forget to save your changes before leaving
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onDiscard}
                  disabled={isPending || isLogoUploading || isCheckingSlug || isPincodeLoading}
                  className="text-xs px-2 py-1 h-7"
                >
                  <X className="h-3 w-3 mr-1" />
                  Discard
                </Button>
                <Button
                  size="sm"
                  onClick={onSave}
                  disabled={
                    isPending ||
                    isLogoUploading ||
                    isCheckingSlug ||
                    isPincodeLoading ||
                    Object.keys(form.formState.errors).length > 0
                  }
                  className="text-xs px-3 py-1.5 h-8 bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] hover:from-[var(--brand-gold-light)] hover:to-[var(--brand-gold)] text-[var(--brand-gold-foreground)] font-medium shadow-sm hover:shadow-md transition-all duration-200 border-0"
                >
                  {(isPending || isLogoUploading || isCheckingSlug || isPincodeLoading) ? (
                    <>
                      <Loader2 className="h-3 w-3 mr-1.5 animate-spin" />
                      {isPending ? "Saving..." :
                       isLogoUploading ? "Uploading..." :
                       isCheckingSlug ? "Checking..." :
                       isPincodeLoading ? "Loading..." :
                       "Saving..."}
                    </>
                  ) : (
                    <>
                      <Save className="h-3 w-3 mr-1.5" />
                      Save
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
