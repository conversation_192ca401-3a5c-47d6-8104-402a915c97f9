import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@/contexts/AuthContext';
import { Home, Search, Store, User, DukancardBottomTabs } from './BottomNavigation';
import QRScannerModal from '../../qr/QRScannerModal';

interface UnifiedBottomNavigationProps {
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  showQRScanner?: boolean;
}

export default function UnifiedBottomNavigation({
  activeTab,
  onTabChange,
  showQRScanner = true
}: UnifiedBottomNavigationProps) {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const { user, profileStatus } = useAuth();
  const [qrScannerVisible, setQrScannerVisible] = useState(false);

  // Determine user type
  const isBusinessUser = profileStatus.roleStatus?.role === 'business';
  const isCustomerUser = profileStatus.roleStatus?.role === 'customer';

  // Handle navigation
  const handleHomePress = () => {
    if (isBusinessUser) {
      router.push('/(dashboard)/business');
    } else if (isCustomerUser) {
      router.push('/(dashboard)/customer');
    } else {
      router.push('/');
    }
    onTabChange?.('home');
  };

  const handleDiscoverPress = () => {
    router.push('/discover');
    onTabChange?.('discover');
  };

  const handleDukanAIPress = () => {
    console.log('Dukan AI is coming soon!');
    // TODO: Show coming soon modal or toast
  };

  const handleAccountPress = () => {
    if (isBusinessUser) {
      router.push('/(dashboard)/business/profile');
    } else if (isCustomerUser) {
      router.push('/(dashboard)/customer/profile');
    } else {
      router.push('/(auth)/login');
    }
    onTabChange?.('profile');
  };

  const handleQRScanPress = () => {
    setQrScannerVisible(true);
  };

  const handleQRScanSuccess = (businessSlug: string) => {
    router.push(`/business/${businessSlug}`);
    setQrScannerVisible(false);
  };

  const handleQRScannerClose = () => {
    setQrScannerVisible(false);
  };

  // Determine account label based on user type
  const getAccountLabel = () => {
    if (isBusinessUser) {
      return 'Dashboard';
    } else if (isCustomerUser) {
      return 'Profile';
    }
    return 'Account';
  };

  const tabs = [
    {
      key: 'home',
      icon: Home,
      label: 'Home',
      onPress: handleHomePress,
      isActive: activeTab === 'home',
    },
    {
      key: 'discover',
      icon: Search,
      label: 'Discover',
      onPress: handleDiscoverPress,
      isActive: activeTab === 'discover',
    },
    {
      key: 'dukan-ai',
      icon: Store,
      label: 'Dukan AI',
      onPress: handleDukanAIPress,
      isActive: false,
      disabled: true,
    },
    {
      key: 'profile',
      icon: User,
      label: getAccountLabel(),
      onPress: handleAccountPress,
      isActive: activeTab === 'profile',
    },
  ];

  return (
    <>
      <DukancardBottomTabs
        tabs={tabs}
        onQRScanPress={showQRScanner ? handleQRScanPress : undefined}
        colorScheme={colorScheme}
      />

      {/* QR Scanner Modal */}
      {showQRScanner && qrScannerVisible && (
        <QRScannerModal
          visible={qrScannerVisible}
          onClose={handleQRScannerClose}
          onScanSuccess={handleQRScanSuccess}
        />
      )}
    </>
  );
}
