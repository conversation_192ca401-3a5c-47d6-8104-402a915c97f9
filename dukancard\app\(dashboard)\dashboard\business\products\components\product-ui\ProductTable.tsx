"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Loader2, Edit, Trash2, ArrowUpDown, Package, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Image from "next/image";
import Link from "next/link";
import { viewTransitionVariants } from "../../types";
import { useProducts } from "../../context/ProductsContext";
import ProductEmptyState from "./ProductEmptyState";
import VariantTable from "../VariantTable";
import { getProductWithVariants } from "../../actions/getProductWithVariants";
import { ProductVariant } from "@/types/variants";
import { toast } from "sonner";

export default function ProductTable() {
  const {
    products,
    isLoading,
    currentPage,
    isPending,
    deletingProductId,
    setDeletingProductId,
    getProductStatusBadge
  } = useProducts();

  // State for expanded products and their variants
  const [expandedProducts, setExpandedProducts] = useState<Set<string>>(new Set());
  const [productVariants, setProductVariants] = useState<Record<string, ProductVariant[]>>({});
  const [loadingVariants, setLoadingVariants] = useState<Set<string>>(new Set());

  // Toggle product expansion and load variants if needed
  const toggleProductExpansion = async (productId: string) => {
    const newExpanded = new Set(expandedProducts);

    if (newExpanded.has(productId)) {
      // Collapse
      newExpanded.delete(productId);
    } else {
      // Expand and load variants if not already loaded
      newExpanded.add(productId);

      if (!productVariants[productId]) {
        setLoadingVariants(prev => new Set(prev).add(productId));

        try {
          const result = await getProductWithVariants(productId);
          if (result.success && result.data) {
            setProductVariants(prev => ({
              ...prev,
              [productId]: result.data?.variants || []
            }));
          } else {
            toast.error("Failed to load product variants");
            newExpanded.delete(productId); // Don't expand if loading failed
          }
        } catch (error) {
          console.error("Error loading variants:", error);
          toast.error("Failed to load product variants");
          newExpanded.delete(productId); // Don't expand if loading failed
        } finally {
          setLoadingVariants(prev => {
            const newSet = new Set(prev);
            newSet.delete(productId);
            return newSet;
          });
        }
      }
    }

    setExpandedProducts(newExpanded);
  };

  return (
    <motion.div
      key="table-view"
      variants={viewTransitionVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="relative w-full rounded-xl bg-white dark:bg-black overflow-hidden shadow-md transition-all duration-300"
    >
      {isLoading && currentPage > 1 && (
        <div className="absolute bottom-0 left-0 right-0 p-4 flex items-center justify-center bg-neutral-50/80 dark:bg-black/80 z-10">
          <div className="bg-white dark:bg-black p-2 rounded-full shadow-lg">
            <Loader2 className="h-5 sm:h-6 w-5 sm:w-6 animate-spin text-primary" />
          </div>
        </div>
      )}

      <Table>
        <TableHeader>
          <TableRow className="hover:bg-neutral-50 dark:hover:bg-neutral-900">
            <TableHead className="w-[40px] text-center text-xs sm:text-sm text-neutral-600 dark:text-neutral-400"></TableHead>
            <TableHead className="text-center text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">Image</TableHead>
            <TableHead className="text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
              <div className="flex items-center">
                Name
                <ArrowUpDown className="ml-1 h-3 sm:h-3.5 w-3 sm:w-3.5 text-neutral-400 dark:text-neutral-500" />
              </div>
            </TableHead>
            <TableHead className="text-center text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">Type</TableHead>
            <TableHead className="text-center text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">Variants</TableHead>
            <TableHead className="text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">Base Price</TableHead>
            <TableHead className="text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">Discounted Price</TableHead>
            <TableHead className="text-center text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">Status</TableHead>
            <TableHead className="text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {!isLoading && products.length === 0 && (
            <TableRow>
              <TableCell
                colSpan={9}
                className="h-32 text-center"
              >
                <ProductEmptyState view="table" />
              </TableCell>
            </TableRow>
          )}
          {products.map((product) => {
            const isExpanded = expandedProducts.has(product.id);
            const isLoadingVariants = loadingVariants.has(product.id);
            const hasVariants = product.variant_count > 0;

            return (
              <React.Fragment key={product.id}>
                <TableRow
                  className={`
                    hover:bg-neutral-50 dark:hover:bg-neutral-900
                    ${isPending && deletingProductId === product.id ? 'opacity-50 pointer-events-none' : ''}
                    ${isExpanded ? 'bg-neutral-50 dark:bg-neutral-900' : ''}
                    transition-all duration-200
                  `}
                >
                  {/* Expand/Collapse Button */}
                  <TableCell className="p-2">
                    {hasVariants ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleProductExpansion(product.id)}
                        disabled={isLoadingVariants}
                        className="h-6 w-6 p-0"
                      >
                        {isLoadingVariants ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          <motion.div
                            animate={{ rotate: isExpanded ? 90 : 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <ChevronRight className="h-3 w-3" />
                          </motion.div>
                        )}
                      </Button>
                    ) : (
                      <div className="h-6 w-6" /> // Empty space for products without variants
                    )}
                  </TableCell>

                  <TableCell className="flex justify-center">
                {product.image_url ? (
                  <div className="relative w-10 sm:w-12 h-10 sm:h-12 rounded-md overflow-hidden border border-neutral-200 dark:border-neutral-700">
                    <Image
                      src={product.image_url}
                      alt={product.name ?? "Product image"}
                      className="object-cover w-full h-full transition-transform hover:scale-110"
                      width={48}
                      height={48}
                      layout="responsive"
                    />
                  </div>
                ) : (
                  <div className="w-10 sm:w-12 h-10 sm:h-12 bg-neutral-100 dark:bg-neutral-900 rounded-md flex items-center justify-center text-neutral-400 dark:text-neutral-500">
                    <Package className="w-5 sm:w-6 h-5 sm:h-6" />
                  </div>
                )}
              </TableCell>
              <TableCell className="font-medium text-xs sm:text-sm text-neutral-800 dark:text-neutral-100">
                <div className="max-w-xs truncate">{product.name}</div>
              </TableCell>
              <TableCell className="capitalize text-center text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
                <div className="font-normal text-xs bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 px-2 py-1 rounded-full inline-block">
                  {product.product_type}
                </div>
              </TableCell>
              <TableCell className="text-center text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
                {product.variant_count > 0 ? (
                  <div className="flex flex-col items-center gap-1">
                    <div className="font-medium text-neutral-800 dark:text-neutral-200">
                      {product.variant_count}
                    </div>
                    <div className="text-xs text-neutral-500 dark:text-neutral-400">
                      {product.available_variant_count} available
                    </div>
                  </div>
                ) : (
                  <div className="text-neutral-400 dark:text-neutral-500 text-xs">
                    No variants
                  </div>
                )}
              </TableCell>
              <TableCell className="text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
                {product.base_price?.toLocaleString("en-IN", {
                  style: "currency",
                  currency: "INR",
                }) ?? "-"}
              </TableCell>
              <TableCell className="text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
                {product.discounted_price?.toLocaleString("en-IN", {
                  style: "currency",
                  currency: "INR",
                }) ?? "-"}
              </TableCell>
              <TableCell className="text-center">
                {getProductStatusBadge(product.is_available)}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1 sm:space-x-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link href={`/dashboard/business/products/edit/${product.id}`}>
                          <Button
                            variant="ghost"
                            size="icon"
                            disabled={isPending}
                            className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 h-7 sm:h-8 w-7 sm:w-8"
                          >
                            <Edit className="h-3.5 sm:h-4 w-3.5 sm:w-4" />
                          </Button>
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent className="bg-black dark:bg-neutral-100 text-white dark:text-neutral-800 text-xs">
                        <p>Edit Item</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => setDeletingProductId(product.id!)}
                          disabled={isPending || deletingProductId === product.id}
                          className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 h-7 sm:h-8 w-7 sm:w-8"
                        >
                          {deletingProductId === product.id && isPending ? (
                            <Loader2 className="h-3.5 sm:h-4 w-3.5 sm:w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-3.5 sm:h-4 w-3.5 sm:w-4" />
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent className="bg-black dark:bg-neutral-100 text-white dark:text-neutral-800 text-xs">
                        <p>Delete Item</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </TableCell>
            </TableRow>

            {/* Expanded Variant Section */}
            <AnimatePresence>
              {isExpanded && hasVariants && productVariants[product.id] && (
                <motion.tr
                  key={`expanded-${product.id}`}
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  className="bg-neutral-50 dark:bg-neutral-900"
                >
                  <TableCell colSpan={9} className="p-0">
                    <div className="p-4">
                      <VariantTable
                        productId={product.id}
                        variants={productVariants[product.id]}
                        className="border-0 bg-transparent"
                        onAddVariant={() => {
                          // Navigate to add variant page or open modal
                          window.location.href = `/dashboard/business/products/edit/${product.id}?tab=variants`;
                        }}
                        onEditVariant={(variant) => {
                          // Navigate to edit variant page or open modal
                          window.location.href = `/dashboard/business/products/edit/${product.id}?tab=variants&variant=${variant.id}`;
                        }}
                        onDeleteVariant={(variantId) => {
                          // Handle variant deletion
                          console.log("Delete variant:", variantId);
                          // You would implement the delete logic here
                        }}
                        onToggleVariantAvailability={(variantId, isAvailable) => {
                          // Handle variant availability toggle
                          console.log("Toggle variant availability:", variantId, isAvailable);
                          // You would implement the toggle logic here
                        }}
                      />
                    </div>
                  </TableCell>
                </motion.tr>
              )}
            </AnimatePresence>
          </React.Fragment>
            );
          })}
        </TableBody>
      </Table>
    </motion.div>
  );
}
