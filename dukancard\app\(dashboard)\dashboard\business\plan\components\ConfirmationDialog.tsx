"use client";

import React from "react";
import { Loader2 } from "lucide-react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Card } from "@/components/ui/card";
import { VisuallyHidden } from "@/components/ui/visually-hidden";

interface ConfirmationDialogProps {
  isOpen: boolean;
  message?: string;
  description?: string;
}

/**
 * A non-dismissible dialog that shows a loading spinner and message
 * Used for displaying waiting/processing states during subscription operations
 */
export default function ConfirmationDialog({
  isOpen,
  message = "Waiting for confirmation from <PERSON><PERSON><PERSON><PERSON>",
  description = "Please wait while we receive webhook confirmation. This may take a moment.",
}: ConfirmationDialogProps) {
  // Use state to animate the dots
  const [dots, setDots] = React.useState("");

  // Animate the dots
  React.useEffect(() => {
    if (!isOpen) return;

    const interval = setInterval(() => {
      setDots((prev) => {
        if (prev.length >= 3) return "";
        return prev + ".";
      });
    }, 500);

    return () => clearInterval(interval);
  }, [isOpen]);

  return (
    <Dialog open={isOpen} modal={true}>
      <DialogContent
        className="max-w-md mx-auto p-0 border-none shadow-lg"
        // Remove the close button by setting hideClose to true
        hideClose={true}
        // Prevent closing on outside click or escape key
        onEscapeKeyDown={(e) => e.preventDefault()}
        onPointerDownOutside={(e) => e.preventDefault()}
        onInteractOutside={(e) => e.preventDefault()}
      >
        {/* Add DialogTitle for accessibility, but visually hide it since we show the message in a different way */}
        <DialogTitle asChild>
          <VisuallyHidden>{message}</VisuallyHidden>
        </DialogTitle>

        <Card className="overflow-hidden border-primary/20">
          <div className="p-6">
            <div className="flex flex-col items-center text-center">
              <div className="relative">
                <div className="absolute -inset-4 rounded-full bg-primary/10 animate-pulse blur-xl opacity-75" />
                <Loader2 className="h-12 w-12 animate-spin text-primary relative z-10" />
              </div>
              <h3 className="mt-6 text-lg font-medium text-foreground">
                {message}
                <span className="inline-block w-8 text-left">{dots}</span>
              </h3>
              <p className="mt-2 text-sm text-muted-foreground">{description}</p>
              <p className="mt-4 text-xs text-muted-foreground italic">
                This may take a few moments. Please don&apos;t refresh the page.
              </p>
            </div>
          </div>
        </Card>
      </DialogContent>
    </Dialog>
  );
}
