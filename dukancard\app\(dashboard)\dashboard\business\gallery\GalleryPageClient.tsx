"use client";

import { motion } from "framer-motion";
import { toast } from "sonner";
import { GalleryImage } from "./types";
import { uploadGalleryImage, deleteGalleryImage } from "./actions";
import { getGalleryLimit } from "./utils/fileValidation";
import { useGalleryState } from "./hooks/useGalleryState";
import { useDragAndDrop } from "./hooks/useDragAndDrop";
import { useReordering } from "./hooks/useReordering";
import { compressImageUltraAggressiveClient } from "@/lib/utils/client-image-compression";

// Components
import GalleryHeader from "./components/GalleryHeader";
import UploadBox from "./components/UploadBox";
import GalleryGrid from "./components/GalleryGrid";
import UploadDialog from "./components/UploadDialog";
import DeleteDialog from "./components/DeleteDialog";
import Lightbox from "./components/Lightbox";

interface GalleryPageClientProps {
  initialImages: GalleryImage[];
  userPlan: string;
  businessName: string;
}

export default function GalleryPageClient({
  initialImages,
  userPlan,
  businessName: _businessName, // Prefixed with underscore to indicate intentionally unused
}: GalleryPageClientProps) {
  const galleryLimit = getGalleryLimit(userPlan);

  // Custom hooks for state management
  const {
    images,
    isClient,
    uploadState,
    deleteState,
    lightboxState,
    updateImages,
    updateUploadState,
    updateDeleteState,
    updateLightboxState,
  } = useGalleryState(initialImages);

  const canAddMore = images.length < galleryLimit;

  const {
    isDragging,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
    handleFileSelect,
  } = useDragAndDrop({
    canAddMore,
    userPlan,
    galleryLimit,
    updateUploadState,
  });

  const {
    reorderState,
    activeId,
    handleDragStart,
    handleDragEnd,
    handleSaveOrder,
    handleResetOrder,
  } = useReordering({
    images,
    updateImages,
  });

  // Handle file selection from input
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // Handle image upload
  const handleUpload = async () => {
    if (!uploadState.selectedFile) return;

    updateUploadState({ isUploading: true });
    try {
      // Compress image on client-side first
      const compressionResult = await compressImageUltraAggressiveClient(uploadState.selectedFile, {
        maxDimension: 1200,
        targetSizeKB: 100
      });

      // Convert compressed blob to file
      const compressedFile = new File([compressionResult.blob], uploadState.selectedFile.name, {
        type: compressionResult.blob.type
      });

      const formData = new FormData();
      formData.append("image", compressedFile);

      const { success, image, error } = await uploadGalleryImage(formData);

      if (success && image) {
        const newImages = [...images, image];
        updateImages(newImages);

        toast.success("Image uploaded", {
          description: "Your gallery image has been uploaded successfully",
        });
        updateUploadState({ uploadDialogOpen: false });
      } else {
        // Show user-friendly error message
        if (error?.includes("File size must be less than 15MB")) {
          toast.error("Image too large", {
            description: "Please select an image smaller than 15MB"
          });
        } else if (error?.includes("Invalid file type")) {
          toast.error("Invalid file type", {
            description: "Please select a JPG, PNG, WebP, or GIF image"
          });
        } else if (error?.includes("reached the limit")) {
          toast.error("Gallery limit reached", {
            description: error
          });
        } else {
          toast.error("Upload failed", {
            description: error || "Failed to upload image"
          });
        }
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("Upload failed", {
        description: "An unexpected error occurred",
      });
    } finally {
      updateUploadState({ isUploading: false });
    }
  };

  // Handle image deletion
  const handleDelete = async () => {
    if (!deleteState.selectedImage) return;

    updateDeleteState({ isDeleting: true });
    try {
      const result = await deleteGalleryImage(deleteState.selectedImage.id);
      const { success, error } = result;

      if (success) {
        const filteredImages = images.filter((img) => img.id !== deleteState.selectedImage!.id);
        updateImages(filteredImages);

        // Show appropriate message based on whether there was a warning
        const deleteResult = result as { success: boolean; error?: string; warning?: string };
        if (deleteResult.warning) {
          toast.success("Image deleted", {
            description: deleteResult.warning,
          });
        } else {
          toast.success("Image deleted", {
            description: "The gallery image has been deleted successfully",
          });
        }
        updateDeleteState({ deleteDialogOpen: false, selectedImage: null });
      } else {
        toast.error("Deletion failed", {
          description: error || "Failed to delete image",
        });
      }
    } catch (error) {
      console.error("Error deleting image:", error);
      toast.error("Deletion failed", {
        description: "An unexpected error occurred",
      });
    } finally {
      updateDeleteState({ isDeleting: false });
    }
  };

  // Handle clear preview
  const handleClearPreview = () => {
    updateUploadState({
      selectedFile: null,
      previewUrl: null,
    });
  };

  // Event handlers for components
  const handleViewImage = (url: string) => {
    updateLightboxState({ lightboxImage: url });
  };

  const handleDeleteImage = (image: GalleryImage) => {
    updateDeleteState({
      selectedImage: image,
      deleteDialogOpen: true,
    });
  };

  const handleUploadClick = () => {
    updateUploadState({ uploadDialogOpen: true });
  };

  const handleUploadDialogClose = (open: boolean) => {
    updateUploadState({ uploadDialogOpen: open });
  };

  const handleDeleteDialogClose = (open: boolean) => {
    updateDeleteState({ deleteDialogOpen: open });
  };

  const handleLightboxClose = () => {
    updateLightboxState({ lightboxImage: null });
  };

  // Additional handlers for new component structure
  const handleUploadDialogDrop = (e: React.DragEvent<HTMLDivElement>) => {
    // Use the existing handleDrop from useDragAndDrop hook
    handleDrop(e);
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header Section */}
      <GalleryHeader
        canAddMore={canAddMore}
        isUploading={uploadState.isUploading}
        onUploadClick={handleUploadClick}
      />

      {/* Upload Box - Above Gallery */}
      <UploadBox
        canAddMore={canAddMore}
        isDragging={isDragging}
        imagesCount={images.length}
        galleryLimit={galleryLimit}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleUploadClick}
      />

      {/* Main Gallery Grid */}
      <GalleryGrid
        images={images}
        imagesCount={images.length}
        galleryLimit={galleryLimit}
        canAddMore={canAddMore}
        isDragging={isDragging}
        userPlan={userPlan}
        isClient={isClient}
        reorderState={reorderState}
        activeId={activeId}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onSaveOrder={handleSaveOrder}
        onResetOrder={handleResetOrder}
        onViewImage={handleViewImage}
        onDeleteImage={handleDeleteImage}
        onUploadClick={handleUploadClick}
      />

      {/* Upload Dialog */}
      <UploadDialog
        uploadState={uploadState}
        isDragging={isDragging}
        imagesCount={images.length}
        galleryLimit={galleryLimit}
        onOpenChange={handleUploadDialogClose}
        onFileChange={handleFileChange}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleUploadDialogDrop}
        onUpload={handleUpload}
        onClearPreview={handleClearPreview}
      />

      {/* Delete Dialog */}
      <DeleteDialog
        deleteState={deleteState}
        onOpenChange={handleDeleteDialogClose}
        onDelete={handleDelete}
      />

      {/* Lightbox */}
      <Lightbox
        lightboxState={lightboxState}
        onClose={handleLightboxClose}
      />
    </motion.div>
  );
}
