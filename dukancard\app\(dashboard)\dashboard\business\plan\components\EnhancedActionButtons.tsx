"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  XCircle,
  RefreshCcw,
  ArrowLeftRight,
  Sparkles,
  ShieldCheck,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { SubscriptionStatus } from "../components/SubscriptionStatusBadge";

interface EnhancedActionButtonsProps {
  subscriptionStatus: SubscriptionStatus;
  isEligibleForRefund: boolean;
  onCancelSubscription: () => void;
  onRequestRefund: () => void;
  isPremiumPlan: boolean;
}

export default function EnhancedActionButtons({
  subscriptionStatus,
  isEligibleForRefund,
  onCancelSubscription,
  onRequestRefund,
  isPremiumPlan,
}: EnhancedActionButtonsProps) {
  // Animation variants
  const buttonVariants = {
    hover: {
      scale: 1.03,
      transition: { duration: 0.2 },
    },
    tap: {
      scale: 0.97,
      transition: { duration: 0.1 },
    },
  };

  const isActiveOrAuthenticated =
    subscriptionStatus === "active" || subscriptionStatus === "authenticated";

  return (
    <Card
      className={cn(
        "border shadow-sm transition-all duration-300 hover:shadow-md h-full",
        isPremiumPlan
          ? "bg-gradient-to-br from-white dark:from-neutral-900 to-[var(--brand-gold)]/5 dark:to-[var(--brand-gold)]/10 border-[var(--brand-gold)]/20 dark:border-[var(--brand-gold)]/10"
          : "bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-800"
      )}
    >
      <CardHeader>
        <div className="flex items-center gap-2">
          {isPremiumPlan ? (
            <Sparkles className="h-5 w-5 text-[var(--brand-gold)]" />
          ) : (
            <ShieldCheck className="h-5 w-5 text-primary" />
          )}
          <CardTitle>Subscription Actions</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {isActiveOrAuthenticated && (
          <>
            {/* Cancel Subscription Button */}
            <motion.div
              whileHover="hover"
              whileTap="tap"
              variants={buttonVariants}
            >
              <Button
                variant="outline"
                className="w-full border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-800 dark:hover:text-red-300 flex items-center gap-2"
                onClick={onCancelSubscription}
              >
                <XCircle className="h-4 w-4" />
                Cancel Subscription
              </Button>
            </motion.div>

            {/* Refund Button - Only show if eligible */}
            {isEligibleForRefund && (
              <motion.div
                whileHover="hover"
                whileTap="tap"
                variants={buttonVariants}
              >
                <Button
                  variant="outline"
                  className="w-full border-green-200 dark:border-green-800/50 text-green-700 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-800 dark:hover:text-green-300 flex items-center gap-2"
                  onClick={onRequestRefund}
                >
                  <ArrowLeftRight className="h-4 w-4" />
                  Request Refund
                </Button>
              </motion.div>
            )}
          </>
        )}

        {/* Informational text */}
        <div className="text-xs text-muted-foreground mt-4 space-y-2">
          {isActiveOrAuthenticated && (
            <p className="flex items-start gap-2">
              <RefreshCcw className="h-3.5 w-3.5 mt-0.5 flex-shrink-0" />
              <span>
                Your subscription will automatically renew at the end of your
                billing cycle unless cancelled.
              </span>
            </p>
          )}
          {isEligibleForRefund && (
            <p className="flex items-start gap-2">
              <ArrowLeftRight className="h-3.5 w-3.5 mt-0.5 flex-shrink-0" />
              <span>
                You can request a full refund within 7 days of subscription activation.
              </span>
            </p>
          )}
          {subscriptionStatus === "cancelled" && (
            <p className="flex items-start gap-2">
              <XCircle className="h-3.5 w-3.5 mt-0.5 flex-shrink-0" />
              <span>
                Your subscription has been cancelled and will not renew at the
                end of the current billing cycle.
              </span>
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
