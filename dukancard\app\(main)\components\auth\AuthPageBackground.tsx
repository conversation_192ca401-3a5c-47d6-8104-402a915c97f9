"use client";

import { useEffect, useState } from "react";
import { motion, useAnimation } from "framer-motion";

export default function AuthPageBackground() {
  const [isClient, setIsClient] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Animation controls
  const gradientControls = useAnimation();
  const blob1Controls = useAnimation();
  const blob2Controls = useAnimation();
  const blob3Controls = useAnimation();
  const lineControls = useAnimation();
  const nodeControls = useAnimation();

  // Only render on client side and detect mobile
  useEffect(() => {
    setIsClient(true);
    setIsMobile(window.innerWidth < 768);

    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Setup animations
  useEffect(() => {
    if (isClient) {
      // Gradient animation
      gradientControls.start({
        scale: 1.1,
        transition: {
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        },
      });

      // Blob animations
      blob1Controls.start({
        scale: 1.2,
        x: 20,
        transition: {
          duration: 10,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        },
      });

      blob2Controls.start({
        scale: 1.15,
        x: -15,
        transition: {
          duration: 12,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        },
      });

      blob3Controls.start({
        scale: 1.25,
        y: 25,
        transition: {
          duration: 14,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        },
      });

      // Line animation
      lineControls.start({
        scale: 1.1,
        transition: {
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        },
      });

      // Node animation
      nodeControls.start({
        scale: 1.3,
        transition: {
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        },
      });
    }
  }, [isClient, gradientControls, blob1Controls, blob2Controls, blob3Controls, lineControls, nodeControls]);

  return (
    <div className="absolute inset-0 -z-10 overflow-hidden">
      {isClient && (
        <>
          {/* Main gradient background */}
          <motion.div
            animate={gradientControls}
            className="absolute inset-0 opacity-40 dark:opacity-30"
            style={{
              background: `radial-gradient(circle at 50% 50%,
                var(--brand-gold) 0%,
                rgba(var(--brand-gold-rgb), 0.3) 25%,
                rgba(var(--brand-gold-rgb), 0.1) 50%,
                rgba(0, 0, 255, 0.1) 75%,
                rgba(0, 0, 255, 0.05) 100%)`,
              filter: isMobile ? "blur(60px)" : "blur(80px)",
            }}
          />

          {/* Top right blob */}
          <motion.div
            animate={blob1Controls}
            className="absolute top-0 right-0 w-[500px] h-[500px] rounded-full bg-[var(--brand-gold-rgb)]/5 blur-3xl dark:bg-[var(--brand-gold-rgb)]/10 opacity-70"
          />

          {/* Top left blob */}
          <motion.div
            animate={blob2Controls}
            className="absolute -top-20 -left-20 w-[300px] h-[300px] rounded-full bg-blue-500/5 blur-3xl dark:bg-blue-500/10 opacity-70"
          />

          {/* Bottom blob */}
          <motion.div
            animate={blob3Controls}
            className="absolute bottom-0 left-1/4 w-[400px] h-[400px] rounded-full bg-purple-500/5 blur-3xl dark:bg-purple-500/10 opacity-60"
          />

          {/* Circuit-like elements */}
          <svg
            className="absolute inset-0 w-full h-full opacity-10 dark:opacity-20 pointer-events-none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                <feGaussianBlur stdDeviation="2" result="blur" />
                <feComposite
                  in="SourceGraphic"
                  in2="blur"
                  operator="over"
                  result="glow"
                />
              </filter>
            </defs>

            {/* Single circuit line */}
            <motion.line
              animate={lineControls}
              x1="20%"
              y1="20%"
              x2="80%"
              y2="80%"
              stroke="var(--brand-gold)"
              strokeWidth="0.5"
              strokeOpacity="0.3"
              filter="url(#glow)"
            />

            {/* Single circuit node */}
            <motion.circle
              animate={nodeControls}
              cx="50%"
              cy="50%"
              r="2"
              fill="var(--brand-gold)"
              filter="url(#glow)"
            />
          </svg>
        </>
      )}
    </div>
  );
}