"use client";

import { toast } from "sonner";
import { Link as LinkIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import WhatsAppIcon from "@/app/components/icons/WhatsAppIcon";
import FacebookIcon from "@/app/components/icons/FacebookIcon";
import TwitterIcon from "@/app/components/icons/TwitterIcon";
import LinkedInIcon from "@/app/components/icons/LinkedInIcon";
import PinterestIcon from "@/app/components/icons/PinterestIcon";
import TelegramIcon from "@/app/components/icons/TelegramIcon";
import { BusinessCardData } from "../../schema";

interface ShareButtonsProps {
  cardData: BusinessCardData;
}

export default function ShareButtons({ cardData }: ShareButtonsProps) {
  // Share handler
  const handleShare = (platform: "whatsapp" | "facebook" | "twitter" | "linkedin" | "pinterest" | "telegram" | "copy") => {
    const slug = cardData.business_slug;
    if (!slug) {
      toast.error("Please set a business slug first to share.");
      return;
    }

    const url = `${window.location.origin}/${slug}`;
    const text = `Check out my digital business card: ${cardData.business_name || "My Business"} on Dukancard!`;

    let shareUrl = "";
    switch (platform) {
      case "whatsapp":
        shareUrl = `https://wa.me/?text=${encodeURIComponent(`${text} ${url}`)}`;
        break;
      case "facebook":
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case "twitter":
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
        break;
      case "linkedin":
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        break;
      case "pinterest":
        shareUrl = `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(url)}&description=${encodeURIComponent(text)}`;
        break;
      case "telegram":
        shareUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
        break;
      case "copy":
        navigator.clipboard.writeText(url)
          .then(() => toast.success("Link copied!"))
          .catch(() => toast.error("Failed to copy."));
        return;
    }

    if (shareUrl) window.open(shareUrl, "_blank", "noopener,noreferrer");
  };

  // Share button component
  const ShareButton = ({
    platform,
    children
  }: {
    platform: "whatsapp" | "facebook" | "twitter" | "linkedin" | "pinterest" | "telegram" | "copy";
    children: React.ReactNode;
  }) => (
    <Button
      variant="ghost"
      size="icon"
      onClick={() => handleShare(platform)}
      aria-label={`Share on ${platform.charAt(0).toUpperCase() + platform.slice(1)}`}
      disabled={!cardData.business_slug}
      className="text-muted-foreground hover:text-[var(--brand-gold)] disabled:opacity-50"
    >
      {children}
    </Button>
  );

  return (
    <div className="flex gap-1 justify-center items-center border border-border rounded-md p-1 bg-background">
      <span className="text-sm text-muted-foreground mr-1">Share:</span>
      <ShareButton platform="whatsapp"><WhatsAppIcon className="w-4 h-4" /></ShareButton>
      <ShareButton platform="facebook"><FacebookIcon className="w-4 h-4" /></ShareButton>
      <ShareButton platform="twitter"><TwitterIcon className="w-4 h-4" /></ShareButton>
      <ShareButton platform="linkedin"><LinkedInIcon className="w-4 h-4" /></ShareButton>
      <ShareButton platform="pinterest"><PinterestIcon className="w-4 h-4" /></ShareButton>
      <ShareButton platform="telegram"><TelegramIcon className="w-4 h-4" /></ShareButton>
      <ShareButton platform="copy"><LinkIcon className="w-4 h-4" /></ShareButton>
    </div>
  );
}
