import { supabaseAdmin } from '@/lib/utils/supabaseAdmin';
import PushNotificationService, { BusinessActivityData } from './pushNotificationService';

interface QueueItem {
  id: string;
  user_id: string;
  notification_type: string;
  data: BusinessActivityData;
  created_at: string;
}

class PushNotificationQueueProcessor {
  private static instance: PushNotificationQueueProcessor;
  private isProcessing = false;
  private processingInterval: any = null;
  private readonly BATCH_SIZE = 10;
  private readonly PROCESSING_INTERVAL = 30000; // 30 seconds

  static getInstance(): PushNotificationQueueProcessor {
    if (!PushNotificationQueueProcessor.instance) {
      PushNotificationQueueProcessor.instance = new PushNotificationQueueProcessor();
    }
    return PushNotificationQueueProcessor.instance;
  }

  /**
   * Start processing the push notification queue
   */
  startProcessing(): void {
    if (this.processingInterval) {
      console.log('Push notification queue processor already running');
      return;
    }

    console.log('Starting push notification queue processor');
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, this.PROCESSING_INTERVAL);

    // Process immediately on start
    this.processQueue();
  }

  /**
   * Stop processing the push notification queue
   */
  stopProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      console.log('Push notification queue processor stopped');
    }
  }

  /**
   * Process pending notifications in the queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      // Get unprocessed notifications
      const { data: queueItems, error } = await supabaseAdmin
        .from('push_notification_queue')
        .select('*')
        .eq('processed', false)
        .order('created_at', { ascending: true })
        .limit(this.BATCH_SIZE);

      if (error) {
        console.error('Error fetching push notification queue:', error);
        return;
      }

      if (!queueItems || queueItems.length === 0) {
        return;
      }

      console.log(`Processing ${queueItems.length} push notifications`);

      // Process each notification
      for (const item of queueItems) {
        await this.processQueueItem(item);
      }
    } catch (error) {
      console.error('Error processing push notification queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process a single queue item
   */
  private async processQueueItem(item: QueueItem): Promise<void> {
    try {
      if (item.notification_type === 'business_activity') {
        await PushNotificationService.sendBusinessActivityNotification(item.data);
      }

      // Mark as processed
      await supabaseAdmin
        .from('push_notification_queue')
        .update({
          processed: true,
          processed_at: new Date().toISOString(),
        })
        .eq('id', item.id);

      console.log(`Processed push notification ${item.id}`);
    } catch (error) {
      console.error(`Error processing push notification ${item.id}:`, error);
      
      // Optionally, you could implement retry logic here
      // For now, we'll just log the error and continue
    }
  }

  /**
   * Manually trigger queue processing (useful for testing)
   */
  async triggerProcessing(): Promise<void> {
    await this.processQueue();
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    pending: number;
    processed: number;
    total: number;
  }> {
    try {
      const [pendingResult, processedResult, totalResult] = await Promise.all([
        supabaseAdmin
          .from('push_notification_queue')
          .select('id', { count: 'exact' })
          .eq('processed', false),
        supabaseAdmin
          .from('push_notification_queue')
          .select('id', { count: 'exact' })
          .eq('processed', true),
        supabaseAdmin
          .from('push_notification_queue')
          .select('id', { count: 'exact' })
      ]);

      return {
        pending: pendingResult.count || 0,
        processed: processedResult.count || 0,
        total: totalResult.count || 0,
      };
    } catch (error) {
      console.error('Error getting queue stats:', error);
      return { pending: 0, processed: 0, total: 0 };
    }
  }

  /**
   * Clean up old processed notifications (older than 7 days)
   */
  async cleanupOldNotifications(): Promise<void> {
    try {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const { error } = await supabaseAdmin
        .from('push_notification_queue')
        .delete()
        .eq('processed', true)
        .lt('processed_at', sevenDaysAgo.toISOString());

      if (error) {
        console.error('Error cleaning up old notifications:', error);
      } else {
        console.log('Old processed notifications cleaned up');
      }
    } catch (error) {
      console.error('Error cleaning up old notifications:', error);
    }
  }
}

export default PushNotificationQueueProcessor.getInstance();
