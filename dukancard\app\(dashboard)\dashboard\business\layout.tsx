import React from "react";
// import { cookies } from "next/headers"; // Removed unused import
import { createClient } from "@/utils/supabase/server"; // Correct import name
import BusinessDashboardClientLayout from "./components/BusinessDashboardClientLayout";
import { validateRequiredBusinessFields, generateMissingFieldsMessage } from "@/utils/business-validation";
import { redirect } from "next/navigation";

// This is now a Server Component
export default async function BusinessDashboardLayout({
  // Make the function async
  children,
}: {
  children: React.ReactNode;
}) {
  // const cookieStore = cookies(); // createClient handles this
  const supabase = await createClient(); // Correct function call and add await as it's async

  let businessName: string | null = null;
  let logoUrl: string | null = null;
  let memberName: string | null = null;
  let userPlan: string | null = null;
  let _businessProfile: Record<string, unknown> | null = null;

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    // Get business profile data with all required fields for validation
    const { data: profile, error } = await supabase
      .from("business_profiles")
      .select(`
        business_name,
        logo_url,
        member_name,
        title,
        business_category,
        contact_email,
        phone,
        address_line,
        pincode,
        city,
        state,
        locality
      `)
      .eq("id", user.id)
      .single();

    // Get subscription data to determine plan
    const { data: subscription } = await supabase
      .from("payment_subscriptions")
      .select("plan_id")
      .eq("business_profile_id", user.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error(
        "Error fetching business profile in layout:",
        error.message
      );
      // Handle error appropriately, maybe redirect or show an error state
      // For now, we'll proceed with null values
    } else if (profile) {
      businessName = profile.business_name;
      logoUrl = profile.logo_url;
      memberName = profile.member_name;
      userPlan = subscription?.plan_id || "free";
      _businessProfile = profile;

      // Validate required business fields
      const validation = validateRequiredBusinessFields(profile);
      if (!validation.isComplete) {
        // Generate a message for missing fields
        const message = generateMissingFieldsMessage(validation.missingFieldLabels);

        // Redirect to onboarding with message
        redirect(`/onboarding?message=${encodeURIComponent(message)}`);
      }
    }
  } else {
    // This case should ideally be handled by middleware, but good to have a fallback
    console.warn("No user found in business dashboard layout.");
  }

  // Render the Client Layout component, passing fetched data as props
  return (
    <BusinessDashboardClientLayout
      businessName={businessName}
      logoUrl={logoUrl}
      memberName={memberName}
      userPlan={userPlan}
    >
      {children}
    </BusinessDashboardClientLayout>
  );
}
