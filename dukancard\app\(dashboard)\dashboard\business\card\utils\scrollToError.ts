/**
 * Scrolls to the first form error with enhanced smooth scrolling behavior
 * @param formId The ID of the form containing the errors
 */
export function scrollToFirstError(formId: string = 'business-card-form'): void {
  // Wait for the DOM to update with error messages
  setTimeout(() => {
    // Find the form
    const form = document.getElementById(formId);
    if (!form) return;

    // Find all error messages within the form
    const errorElements = form.querySelectorAll('[role="alert"]');
    if (!errorElements || errorElements.length === 0) {
      // If no error elements with role="alert", try to find form fields with errors
      const errorFields = form.querySelectorAll('.error-field, [aria-invalid="true"]');
      if (errorFields && errorFields.length > 0) {
        smoothScrollToElement(errorFields[0]);
        return;
      }
      return;
    }

    // Get the first error element
    const firstError = errorElements[0];

    // Use custom smooth scroll
    smoothScrollToElement(firstError);
  }, 100); // Small delay to ensure DOM has updated
}

/**
 * Performs a smoother scroll animation to an element
 * @param element The element to scroll to
 */
function smoothScrollToElement(element: Element): void {
  // Get the element's position
  const rect = element.getBoundingClientRect();
  const targetPosition = window.scrollY + rect.top - 150; // 150px offset from top
  const startPosition = window.scrollY;
  const distance = targetPosition - startPosition;

  // Add a subtle highlight effect to draw attention
  element.classList.add('error-highlight');
  setTimeout(() => {
    element.classList.remove('error-highlight');
  }, 3000); // Longer highlight duration

  // Use a more sophisticated easing function for smoother animation
  const easeOutCubic = (t: number): number => 1 - Math.pow(1 - t, 3);

  // Animation parameters
  const duration = 800; // Longer duration for smoother effect
  const startTime = performance.now();

  // Animation function
  function animateScroll(currentTime: number) {
    const elapsedTime = currentTime - startTime;
    const progress = Math.min(elapsedTime / duration, 1);
    const easedProgress = easeOutCubic(progress);

    window.scrollTo({
      top: startPosition + distance * easedProgress,
      behavior: 'auto' // We're handling the animation manually
    });

    if (progress < 1) {
      requestAnimationFrame(animateScroll);
    }
  }

  // Start the animation
  requestAnimationFrame(animateScroll);
}
