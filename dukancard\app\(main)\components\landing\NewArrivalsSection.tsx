"use client";

import React, { useEffect, useState } from "react";
import <PERSON> from "next/link";
import { Button } from "@/components/ui/button";
import { fetchAllProducts } from "@/app/(main)/discover/actions/productActions";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import ProductListItem from "@/app/components/ProductListItem";
import { motion } from "framer-motion";
import ProductGridSkeleton from "@/app/(main)/discover/components/ProductGridSkeleton"; // Import ProductGridSkeleton

export default function NewArrivalsSection() {
  const [products, setProducts] = useState<NearbyProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getNewProducts = async () => {
      setLoading(true);
      setError(null);
      try {
        const { data, error } = await fetchAllProducts({
          page: 1,
          limit: 6, // Fetch 6 new arrivals
          sortBy: "created_desc",
        });

        if (error) {
          setError(error);
        } else if (data) {
          setProducts(data.products);
        }
      } catch (err) {
        console.error("Failed to fetch new arrivals:", err);
        setError("Failed to load new arrivals.");
      } finally {
        setLoading(false);
      }
    };

    getNewProducts();
  }, []);

  return (
    <section className="py-8 md:py-12">
      <div className="container px-2 sm:px-4 md:px-6 mx-auto">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">New Arrivals</h2>
            <p className="max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              Discover the latest products and services added to Dukancard.
            </p>
          </div>
        </div>
        {loading ? (
          <div className="mx-auto max-w-7xl py-8">
            <ProductGridSkeleton />
          </div>
        ) : (
          <div className="mx-auto grid max-w-7xl items-start gap-2 sm:gap-3 md:gap-4 py-8 grid-cols-2 md:grid-cols-3 lg:grid-cols-6 justify-items-stretch">
            {error && <p className="text-red-500 col-span-full text-center">{error}</p>}
            {!error && products.length === 0 && (
              <p className="col-span-full text-center text-gray-500">No new products found.</p>
            )}
            {products.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                className="w-full h-full"
              >
                <ProductListItem product={product} isLink={true} />
              </motion.div>
            ))}
          </div>
        )}
        <div className="flex justify-center mt-8 w-full">
          <Link href="/discover?tab=products" passHref>
            <Button size="lg">View All Products</Button>
          </Link>
        </div>
      </div>
    </section>
  );
}