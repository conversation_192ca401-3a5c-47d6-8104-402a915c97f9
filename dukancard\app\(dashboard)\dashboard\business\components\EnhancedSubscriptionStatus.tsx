"use client";

import { motion } from "framer-motion";
// Removed Card imports as we're using custom div styling
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON>ert<PERSON>riangle, ArrowRight, Crown, ShieldCheck } from "lucide-react";
import Link from "next/link";
import { PricingPlan } from "@/lib/PricingPlans";
import FlipTimer from "./FlipTimer";
import { cn } from "@/lib/utils";

interface EnhancedSubscriptionStatusProps {
  subscriptionStatus: "active" | "trial" | "inactive";
  planDetails?: PricingPlan;
  trialEndDate?: string | null;
}

export default function EnhancedSubscriptionStatus({
  subscriptionStatus,
  planDetails,
  trialEndDate,
}: EnhancedSubscriptionStatusProps) {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
  };

  // Get plan icon based on plan ID
  const getPlanIcon = () => {
    if (!planDetails) return null;

    switch (planDetails.id) {
      case "premium":
        return <Crown className="w-5 h-5 text-[var(--brand-gold)]" />;
      case "pro":
        return (
          <ShieldCheck className="w-5 h-5 text-blue-500 dark:text-blue-400" />
        );
      default:
        return (
          <ShieldCheck className="w-5 h-5 text-green-500 dark:text-green-400" />
        );
    }
  };

  // Get plan badge styling based on plan ID
  const getPlanBadgeStyles = () => {
    if (!planDetails) return "";

    switch (planDetails.id) {
      case "premium":
        return "bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] border-[var(--brand-gold)]/30";
      case "pro":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border-blue-300 dark:border-blue-700";
      default:
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border-green-300 dark:border-green-700";
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="h-full"
    >
      <div
        className={cn(
          "rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 shadow-md p-3 sm:p-4 md:p-5 transition-all duration-300 hover:shadow-lg overflow-hidden h-full flex flex-col",
          subscriptionStatus === "active" &&
            planDetails?.id === "premium" &&
            "bg-gradient-to-br from-white dark:from-neutral-900 to-[var(--brand-gold)]/5"
        )}
      >
        <div className="flex items-center justify-between gap-2 mb-4 pb-3 border-b border-neutral-100 dark:border-neutral-800">
          <div className="flex items-center gap-2">
            <div className="p-1.5 sm:p-2 rounded-lg bg-primary/10 text-primary">
              {getPlanIcon()}
            </div>
            <div>
              <h3 className="text-sm sm:text-base font-semibold text-neutral-800 dark:text-neutral-100 truncate">
                Subscription Status
              </h3>
            </div>
          </div>

          {(subscriptionStatus === "trial" ||
            subscriptionStatus === "active") && (
            <Button
              asChild
              variant="outline"
              size="sm"
              className="border-[var(--brand-gold)]/50 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 dark:hover:text-[var(--brand-gold)] cursor-pointer text-xs h-8 px-2 sm:h-9 sm:px-3"
            >
              <Link href="/dashboard/business/plan">
                {subscriptionStatus === "trial" ? "Upgrade" : "Manage"}
                <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-1 sm:ml-2" />
              </Link>
            </Button>
          )}
        </div>

        <div className="flex-1 flex flex-col justify-center">
          {subscriptionStatus === "active" && planDetails && (
            <div className="flex flex-col items-center justify-center text-center">
              <Badge
                variant="outline"
                className={cn("border px-2 py-1 mb-2", getPlanBadgeStyles())}
              >
                <ShieldCheck className="w-3.5 h-3.5 mr-1" />
                Active
              </Badge>

              <h3 className="text-base sm:text-lg font-medium mb-1">
                {planDetails.name}
              </h3>
              <p className="text-xs sm:text-sm text-muted-foreground max-w-[250px] mx-auto px-2">
                Your subscription is active and all features are enabled
              </p>
            </div>
          )}

          {subscriptionStatus === "trial" && trialEndDate && planDetails && (
            <div className="flex flex-col items-center justify-center text-center">
              <Badge
                variant="outline"
                className="border px-2 py-1 mb-2 bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 border-amber-300 dark:border-amber-700"
              >
                <Crown className="w-3.5 h-3.5 mr-1" />
                Trial
              </Badge>

              <h3 className="text-base sm:text-lg font-medium mb-1">
                {planDetails.name}
              </h3>

              <div className="mt-2">
                <FlipTimer
                  endDate={trialEndDate}
                  label="Trial ends in:"
                  tooltipText="Your trial will expire soon. Upgrade to continue using all features."
                />
              </div>
            </div>
          )}

          {subscriptionStatus === "inactive" && (
            <Alert
              variant="default"
              className="bg-blue-50 border-blue-300 text-blue-800 dark:bg-blue-950/50 dark:border-blue-700/50 dark:text-blue-300"
            >
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Free Plan Active</AlertTitle>
              <AlertDescription>
                You are currently on the free plan with limited features.{" "}
                <Link
                  href="/dashboard/business/plan"
                  className="font-medium underline hover:text-blue-700 dark:hover:text-blue-200"
                >
                  Upgrade to unlock more features
                </Link>{" "}
                and grow your business.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </div>
    </motion.div>
  );
}
