import React, { useState, useEffect, useRef } from 'react';
import { View, Text } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useIsFocused } from '@react-navigation/native';
import { formatIndianNumberShort } from '@/lib/utils';
import { realtimeService } from '@/lib/services/realtimeService';
import { createBusinessProfileStyles } from '@/styles/dashboard/business/business-profile-styles';

interface BusinessProfile {
  id: string;
  business_name: string;
  total_likes: number;
  total_subscriptions: number;
  average_rating: number;
}

interface BusinessProfileStatsProps {
  initialProfile: BusinessProfile;
  userId: string;
}

export const BusinessProfileStats: React.FC<BusinessProfileStatsProps> = ({
  initialProfile,
  userId,
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const isFocused = useIsFocused(); // Only subscribe when screen is focused
  const [profile, setProfile] = useState<BusinessProfile>(initialProfile);
  const isMountedRef = useRef(true);
  const subscriptionRef = useRef<any>(null);
  const styles = createBusinessProfileStyles(colorScheme);

  // Cleanup function for subscription
  const cleanupSubscription = () => {
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe();
      subscriptionRef.current = null;
    }
  };

  // Real-time subscription effect
  useEffect(() => {
    isMountedRef.current = true;

    if (isFocused) {
      // Screen is focused - start real-time subscription
      subscriptionRef.current = realtimeService.subscribeToBusinessUpdates(
        userId,
        (event: any) => {
          if (!isMountedRef.current) return;

          if (event.new) {
            const newData = event.new as BusinessProfile;
            // Update the profile state with new data
            setProfile(newData);
          }
        }
      );
    } else {
      // Screen is not focused - cleanup subscription
      cleanupSubscription();
    }

    // Cleanup subscription when focus changes or component unmounts
    return () => {
      cleanupSubscription();
    };
  }, [isFocused, userId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      cleanupSubscription();
    };
  }, []);

  return (
    <View style={[styles.profileStats, { backgroundColor: isDark ? '#2a2a2a' : '#F9FAFB' }]}>
      <View style={styles.profileStatItem}>
        <Text style={[styles.statValue, { color: isDark ? '#D4AF37' : '#D4AF37' }]}>
          {formatIndianNumberShort(profile.total_likes)}
        </Text>
        <Text style={[styles.statLabel, { color: isDark ? '#999' : '#666' }]}>Likes</Text>
      </View>
      <View style={styles.profileStatItem}>
        <Text style={[styles.statValue, { color: isDark ? '#D4AF37' : '#D4AF37' }]}>
          {formatIndianNumberShort(profile.total_subscriptions)}
        </Text>
        <Text style={[styles.statLabel, { color: isDark ? '#999' : '#666' }]}>Subscribers</Text>
      </View>
      <View style={styles.profileStatItem}>
        <Text style={[styles.statValue, { color: isDark ? '#D4AF37' : '#D4AF37' }]}>
          {profile.average_rating.toFixed(1)}
        </Text>
        <Text style={[styles.statLabel, { color: isDark ? '#999' : '#666' }]}>Rating</Text>
      </View>
    </View>
  );
};
