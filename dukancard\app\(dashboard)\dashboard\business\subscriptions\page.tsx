import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertTriangle, Users } from 'lucide-react';
import BusinessSubscriptionsPageClient from './components/BusinessSubscriptionsPageClient';
import { Suspense } from 'react';
import { SubscriptionListSkeleton } from '@/app/components/shared/subscriptions';

// Import the fetch functions
import { fetchBusinessSubscribers, fetchBusinessFollowing } from './actions';

export const metadata: Metadata = {
  title: "Subscriptions",
  robots: "noindex, nofollow",
};

export default async function BusinessSubscriptionsPage({
  searchParams
}: {
  searchParams: Promise<{ tab?: string; search?: string; page?: string }>
}) {
  // Properly await searchParams to fix the error
  const { tab, search, page: pageParam } = await searchParams;
  const supabase = await createClient();
  const page = pageParam ? parseInt(pageParam) : 1;
  const searchTerm = search || "";
  const activeTab = tab || "subscribers";

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/login?message=Please log in to view your subscriptions.');
  }

  try {
    // Always fetch both counts for tab display, but only fetch detailed data for active tab
    const [subscribersCountResult, followingCountResult] = await Promise.all([
      fetchBusinessSubscribers(user.id, 1, 1), // Just get count
      fetchBusinessFollowing(user.id, 1, 1, ""), // Just get count
    ]);

    // Get the total counts
    const subscribersCount = subscribersCountResult.totalCount;
    const followingCount = followingCountResult.totalCount;

    // Fetch detailed data based on active tab
    let subscribersResult = null;
    let followingResult = null;

    if (activeTab === "subscribers") {
      subscribersResult = await fetchBusinessSubscribers(
        user.id,
        page,
        10 // 10 items per page
        // Note: No search parameter for subscribers tab
      );
    } else {
      followingResult = await fetchBusinessFollowing(
        user.id,
        page,
        10, // 10 items per page
        searchTerm
      );
    }

    return (
      <div className="relative space-y-6 max-w-6xl mx-auto">
        <Suspense fallback={
          <div className="relative z-10">
            <Card className="border shadow-md bg-white dark:bg-black border-neutral-200 dark:border-neutral-800">
              <CardHeader className="pb-4 border-b border-neutral-100 dark:border-neutral-800">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                  <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-500 dark:text-blue-400 self-start">
                    <Users className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                      Subscriptions
                    </h3>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                      Manage your subscribers and following
                    </p>
                  </div>
                </div>

                {/* Tabs skeleton */}
                <div className="mt-4 flex space-x-1 border-b border-neutral-200 dark:border-neutral-800">
                  <Skeleton className="h-10 w-24 rounded-t-md" />
                  <Skeleton className="h-10 w-24 rounded-t-md" />
                </div>

                {/* Search skeleton */}
                <div className="mt-4">
                  <Skeleton className="h-10 w-full rounded-md" />
                </div>
              </CardHeader>

              <CardContent className="pt-4">
                <SubscriptionListSkeleton />
              </CardContent>
            </Card>
          </div>
        }>
          <BusinessSubscriptionsPageClient
            initialSubscribers={subscribersResult?.items || []}
            subscribersCount={subscribersCount}
            subscribersCurrentPage={subscribersResult?.currentPage || 1}
            initialFollowing={followingResult?.items || []}
            followingCount={followingCount}
            followingCurrentPage={followingResult?.currentPage || 1}
            searchTerm={searchTerm}
            activeTab={activeTab}
          />
        </Suspense>
      </div>
    );
  } catch (_error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Could not load subscription data. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }
}
