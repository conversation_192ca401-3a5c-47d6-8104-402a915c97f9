"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";

interface UseUrlParameterHandlerProps {
  setActiveTab: (_tab: "plans" | "subscription") => void;
}

export function useUrlParameterHandler({ setActiveTab }: UseUrlParameterHandlerProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Check for subscription success
    const subscriptionSuccess = searchParams.get('subscription_success');
    const subscriptionId = searchParams.get('subscription_id');
    const timestamp = searchParams.get('timestamp');

    if (subscriptionSuccess === 'true' && subscriptionId && timestamp) {
      // Show success toast
      toast.success(
        `Subscription ${subscriptionId} has been successfully authorized!`,
        {
          description: "Your subscription is now active.",
          duration: 5000,
        }
      );

      // Set active tab to subscription
      setActiveTab("subscription");

      // Refresh the page to get the latest subscription status
      // Use a small delay to ensure the toast is visible
      setTimeout(() => {
        router.refresh();
      }, 1000);

      // Remove the query parameters from the URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }

    // Check for subscription error
    const subscriptionError = searchParams.get('subscription_error');
    const errorMessage = searchParams.get('error');

    if (subscriptionError === 'true' && errorMessage) {
      // Show error toast
      toast.error(
        "Subscription authorization failed",
        {
          description: decodeURIComponent(errorMessage),
          duration: 8000,
        }
      );

      // Set active tab to plans
      setActiveTab("plans");

      // Scroll to the plans section
      setTimeout(() => {
        document.querySelector('[value="plans"]')?.scrollIntoView({ behavior: 'smooth' });
      }, 100);

      // Remove the query parameters from the URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }

    // Check for upgrade parameter
    const upgrade = searchParams.get('upgrade');
    if (upgrade) {
      // Set active tab to plans
      setActiveTab("plans");

      // Show toast message based on the upgrade parameter
      if (upgrade === 'analytics') {
        toast.info(
          "Analytics Feature Requires Upgrade",
          {
            description: "The analytics feature is not available on the free tier. Please upgrade to Basic plan or higher to access analytics.",
            duration: 8000,
          }
        );

      } else {
        toast.info(
          "Plan Upgrade Required",
          {
            description: "Please upgrade your plan to access additional features.",
            duration: 5000,
          }
        );
      }

      // Scroll to the plans section
      setTimeout(() => {
        document.querySelector('[value="plans"]')?.scrollIntoView({ behavior: 'smooth' });
      }, 100);

      // Remove the query parameters from the URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  }, [searchParams, router, setActiveTab]);

  return null; // This hook doesn't return anything, it just handles side effects
}
