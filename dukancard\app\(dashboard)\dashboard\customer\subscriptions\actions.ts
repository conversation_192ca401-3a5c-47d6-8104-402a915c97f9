"use server";

import { createClient } from '@/utils/supabase/server';
import { createAdminClient } from '@/utils/supabase/admin';

// Define types for the subscription data
export interface BusinessProfileData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

export interface SubscriptionWithProfile {
  id: string;
  business_profiles: BusinessProfileData | null;
}

export interface SubscriptionsResult {
  items: SubscriptionWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

/**
 * Fetch subscriptions with pagination and search
 */
export async function fetchSubscriptions(
  userId: string,
  page: number = 1,
  limit: number = 10,
  searchTerm: string = ""
): Promise<SubscriptionsResult> {
  const _supabase = await createClient();
  const supabaseAdmin = createAdminClient();

  try {
    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build a complex query that joins subscriptions with business_profiles
    // This ensures proper database-level pagination with search filtering
    let query = supabaseAdmin
      .from('subscriptions')
      .select(`
        id,
        business_profile_id,
        business_profiles!inner (
          id,
          business_name,
          business_slug,
          logo_url,
          city,
          state,
          pincode,
          address_line
        )
      `, { count: 'exact' })
      .eq('user_id', userId);

    // Apply search filter if provided
    if (searchTerm && searchTerm.trim()) {
      query = query.ilike('business_profiles.business_name', `%${searchTerm.trim()}%`);
    }

    // Apply pagination at database level
    query = query.range(offset, offset + limit - 1);

    // Execute the query
    const { data: subscriptionsWithProfiles, count: totalCount, error: queryError } = await query;

    if (queryError) {
      throw new Error("Failed to fetch customer subscriptions data");
    }

    // Transform the data to match the expected interface
    let transformedSubscriptions: SubscriptionWithProfile[] = [];

    if (subscriptionsWithProfiles && subscriptionsWithProfiles.length > 0) {
      transformedSubscriptions = subscriptionsWithProfiles.map(sub => ({
        id: sub.id,
        business_profiles: Array.isArray(sub.business_profiles) ? sub.business_profiles[0] : sub.business_profiles
      }));
    }

    // Calculate hasMore based on database-level pagination
    const hasMore = totalCount ? totalCount > offset + limit : false;

    return {
      items: transformedSubscriptions,
      totalCount: totalCount || 0,
      hasMore,
      currentPage: page
    };
  } catch (error) {
    throw error;
  }
}
