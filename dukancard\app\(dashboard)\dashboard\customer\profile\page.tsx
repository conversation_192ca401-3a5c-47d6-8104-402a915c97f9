import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import ProfilePageClient from './components/ProfilePageClient';
import { isCustomerAddressComplete } from '@/lib/utils/addressValidation';

export const metadata: Metadata = {
  title: 'My Profile - Dukancard',
  description: 'Manage your Dukancard customer profile.',
  robots: 'noindex, nofollow',
};

export default async function CustomerProfilePage() {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    redirect('/login');
  }

  // Fetch customer profile data from customer_profiles table using user.id
  const { data: profile, error: profileError } = await supabase
    .from('customer_profiles')
    .select('avatar_url, address, pincode, city, state, locality')
    .eq('id', user.id)
    .maybeSingle(); // Use maybeSingle to handle potential null profile

  // Get name from auth.users table (full_name column)
  let initialName: string | null = null;
  if (user.user_metadata?.full_name) {
    initialName = user.user_metadata.full_name;
  } else if (user.user_metadata?.name) {
    initialName = user.user_metadata.name;
  }

  let initialAvatarUrl: string | null = null;
  let initialAddressData = null;
  let hasCompleteAddress = false;

  if (profileError) {
    console.error('Error fetching customer profile:', profileError);
    // Handle error appropriately, maybe show a message to the user
    // For now, we'll proceed with null values, the components can handle it
  } else {
    initialAvatarUrl = profile?.avatar_url || null; // Use fetched avatar_url or null
    initialAddressData = {
      address: profile?.address,
      pincode: profile?.pincode,
      city: profile?.city,
      state: profile?.state,
      locality: profile?.locality,
    };

    // Check if address is complete
    hasCompleteAddress = isCustomerAddressComplete(initialAddressData);
  }

  return (
    <ProfilePageClient
      initialName={initialName}
      initialAvatarUrl={initialAvatarUrl}
      initialAddressData={initialAddressData}
      hasCompleteAddress={hasCompleteAddress}
    />
  );
}
