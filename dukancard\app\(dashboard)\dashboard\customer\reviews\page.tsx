import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import ReviewsPageClient from './components/ReviewsPageClient';
import { requireCompleteProfile } from '@/lib/actions/customerProfiles/addressValidation';

// Define interfaces for the expected data structure
// interface BusinessProfileDataForReview {
//   id: string;
//   business_name: string | null;
//   business_slug: string | null;
//   logo_url: string | null;
// }

// Unused interface - keeping for potential future use
// interface ReviewWithProfile {
//   id: string;
//   rating: number;
//   review_text: string | null;
//   created_at: string;
//   updated_at: string;
//   business_profile_id: string;
//   user_id: string;
//   business_profiles: BusinessProfileDataForReview | null;
// }

export const metadata: Metadata = {
  title: "My Reviews - Dukancard",
  robots: "noindex, nofollow",
};

export default async function CustomerReviewsPage() {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/login?message=Please log in to view your reviews.');
  }

  // Check if customer has complete address
  await requireCompleteProfile(user.id);

  try {
    // Get count of reviews written by the customer
    const { count: reviewsCount } = await supabase
      .from('ratings_reviews')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    return <ReviewsPageClient reviewsCount={reviewsCount || 0} />;
  } catch (_error) {
    // If there's an error fetching count, still render the page with 0 count
    return <ReviewsPageClient reviewsCount={0} />;
  }
}
