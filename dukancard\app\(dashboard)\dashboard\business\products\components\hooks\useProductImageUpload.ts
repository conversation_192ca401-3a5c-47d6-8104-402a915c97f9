"use client";

import { useState } from "react";
import { toast } from "sonner";
import { compressImageUltraAggressiveClient } from "@/lib/utils/client-image-compression";

export type ProductImageUploadStatus = "idle" | "uploading" | "success" | "error";

interface UseProductImageUploadOptions {
  initialImageUrl?: string | null;
}

export function useProductImageUpload({
  initialImageUrl = null,
}: UseProductImageUploadOptions = {}) {
  const [imageUploadStatus, setImageUploadStatus] = useState<ProductImageUploadStatus>("idle");
  const [imageUploadError, setImageUploadError] = useState<string | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(initialImageUrl);
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);
  const [removeCurrentImage, setRemoveCurrentImage] = useState<boolean>(false);
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [isCameraOpen, setIsCameraOpen] = useState<boolean>(false);

  // Check if camera is available
  const isCameraAvailable = typeof navigator !== 'undefined' &&
    navigator.mediaDevices &&
    !!navigator.mediaDevices.getUserMedia;

  // File selection handler
  const handleFileSelect = async (file: File | null) => {
    if (imagePreview && !initialImageUrl) {
      URL.revokeObjectURL(imagePreview);
      setImagePreview(null);
    }

    if (file) {
      try {
        // Accept any image type
        if (!file.type.startsWith("image/")) {
          toast.error("Please select a valid image file.");
          setImageUploadStatus("error");
          setImageUploadError("Please select a valid image file.");
          return;
        }

        // Check file size limit (15MB)
        if (file.size > 15 * 1024 * 1024) {
          toast.error("Image is too large. Please select a smaller image (max 15MB).");
          setImageUploadStatus("error");
          setImageUploadError("Image is too large. Please select a smaller image (max 15MB).");
          return;
        }

        // Use the original file (server will handle compression)
        setOriginalFile(file);
        const reader = new FileReader();
        reader.onloadend = () => {
          setImageToCrop(reader.result as string);
        };
        reader.readAsDataURL(file);

        setRemoveCurrentImage(false);
      } catch (error) {
        console.error("Error processing image:", error);
        toast.error("Failed to process image. Please try again.");
        setImageUploadStatus("error");
        setImageUploadError("Failed to process image. Please try again.");
      }
    } else {
      setSelectedImageFile(null);
      setImagePreview(null);
      setImageUploadStatus("idle");
      setImageUploadError(null);
    }
  };

  // Handle image removal
  const handleRemoveImage = () => {
    setSelectedImageFile(null);
    setImagePreview(null);
    setRemoveCurrentImage(true);
    setImageUploadStatus("idle");
    setImageUploadError(null);

    // If there was a local preview URL, revoke it
    if (imagePreview && imagePreview !== initialImageUrl) {
      URL.revokeObjectURL(imagePreview);
    }
  };

  // Handle crop completion
  const handleCropComplete = async (croppedBlob: Blob | null) => {
    setImageToCrop(null); // Close dialog

    if (croppedBlob) {
      try {
        // Convert blob to file for compression
        const fileName = originalFile?.name || `product-image-${Date.now()}.jpg`;
        const croppedFile = new File([croppedBlob], fileName, {
          type: 'image/png', // Canvas outputs PNG
          lastModified: Date.now()
        });

        // Compress the image on client-side
        const compressionResult = await compressImageUltraAggressiveClient(croppedFile, {
          maxDimension: 800,
          targetSizeKB: 100
        });

        // Convert compressed blob back to file
        const compressedFile = new File([compressionResult.blob], fileName, {
          type: compressionResult.blob.type
        });

        // Create a preview URL
        const previewUrl = URL.createObjectURL(compressedFile);
        setImagePreview(previewUrl);
        setSelectedImageFile(compressedFile);
        setImageUploadStatus("success");
      } catch (error) {
        console.error("Error processing cropped image:", error);
        toast.error("Failed to process cropped image");
        setImageUploadStatus("error");
        setImageUploadError("Failed to process cropped image");
      }
    } else {
      // Handle crop cancellation or error
      console.log("Cropping cancelled or failed.");
      setOriginalFile(null);
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      if (fileInput) fileInput.value = "";
    }
  };

  // Handle crop dialog close
  const handleCropDialogClose = () => {
    setImageToCrop(null);
    setOriginalFile(null);
    // Clear the file input
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) fileInput.value = "";
  };

  // Handle camera open/close
  const handleCameraOpen = () => {
    setIsCameraOpen(true);
  };

  const handleCameraClose = () => {
    setIsCameraOpen(false);
  };

  // Handle camera capture
  const handleCameraCapture = async (imageBlob: Blob) => {
    try {
      // Create a file from the blob
      const file = new File([imageBlob], `camera-photo-${Date.now()}.jpg`, {
        type: 'image/jpeg',
        lastModified: Date.now()
      });

      // Check file size limit (15MB)
      if (file.size > 15 * 1024 * 1024) {
        toast.error("Image is too large. Please try again with a smaller image (max 15MB).");
        return;
      }

      // Send to crop (server will handle compression)
      setOriginalFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImageToCrop(reader.result as string);
      };
      reader.readAsDataURL(file);

      setRemoveCurrentImage(false);
    } catch (error) {
      console.error("Error processing camera image:", error);
      toast.error("Failed to process camera image");
    }
  };

  // Image error display component
  const imageErrorDisplay = imageUploadStatus === "error" && imageUploadError ? imageUploadError : null;

  return {
    imageUploadStatus,
    imageUploadError,
    imagePreview,
    selectedImageFile,
    removeCurrentImage,
    imageToCrop,
    isCameraOpen,
    isCameraAvailable,
    handleFileSelect,
    handleRemoveImage,
    handleCropComplete,
    handleCropDialogClose,
    handleCameraOpen,
    handleCameraClose,
    handleCameraCapture,
    imageErrorDisplay
  };
}
