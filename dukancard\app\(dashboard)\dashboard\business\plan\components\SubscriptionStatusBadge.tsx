"use client";

import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  CheckCircle2,
  Clock,
  AlertTriangle,
  XCircle,
  HelpCircle,
  ShieldCheck,
  Hourglass,
  CreditCard,
  CalendarClock,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import {
  SUBSCRIPTION_STATUS
} from "@/lib/razorpay/webhooks/handlers/utils";

export type SubscriptionStatus =
  | "active"
  | "authenticated"
  | "created"
  | "pending"
  | "halted"
  | "cancelled"
  | "expired"
  | "completed"
  | "trial"
  | "inactive"
  | "paused";

interface SubscriptionStatusBadgeProps {
  status: SubscriptionStatus;
  className?: string;
  showTooltip?: boolean;
}

const statusConfig = {
  [SUBSCRIPTION_STATUS.ACTIVE]: {
    label: "Active",
    icon: Shield<PERSON>he<PERSON>,
    className:
      "bg-gradient-to-r from-green-100 to-green-50 dark:from-green-900/40 dark:to-green-900/20 text-green-800 dark:text-green-300 border border-green-300/70 dark:border-green-700/70 shadow-sm",
    tooltip: "Your subscription is active and billing is up to date.",
    description:
      "Your subscription is active. You have access to all premium features.",
    animate: true,
  },
  paused: {
    label: "Paused",
    icon: Clock,
    className:
      "bg-gradient-to-r from-blue-100 to-blue-50 dark:from-blue-900/40 dark:to-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-300/70 dark:border-blue-700/70 shadow-sm",
    tooltip: "Your subscription is currently paused.",
    description:
      "Your subscription is paused. You can resume it at any time.",
    animate: true,
  },
  [SUBSCRIPTION_STATUS.AUTHENTICATED]: {
    label: "Payment Authorized",
    icon: CreditCard,
    className:
      "bg-gradient-to-r from-blue-100 to-blue-50 dark:from-blue-900/40 dark:to-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-300/70 dark:border-blue-700/70 shadow-sm",
    tooltip:
      "Your payment has been authorized but the subscription hasn't started yet.",
    description:
      "Your payment has been authorized but billing hasn't started yet. Your subscription will be active soon.",
    animate: true,
  },
  created: {
    label: "Created",
    icon: Clock,
    className:
      "bg-gradient-to-r from-purple-100 to-purple-50 dark:from-purple-900/40 dark:to-purple-900/20 text-purple-700 dark:text-purple-300 border border-purple-300/70 dark:border-purple-700/70 shadow-sm",
    tooltip: "Your subscription has been created but not yet authenticated.",
    description:
      "Your subscription has been created but not yet authenticated. Please complete the payment process.",
    animate: true,
  },
  [SUBSCRIPTION_STATUS.PENDING]: {
    label: "Payment Pending",
    icon: Hourglass,
    className:
      "bg-gradient-to-r from-yellow-100 to-yellow-50 dark:from-yellow-900/40 dark:to-yellow-900/20 text-yellow-700 dark:text-yellow-300 border border-yellow-300/70 dark:border-yellow-700/70 shadow-sm",
    tooltip: "A payment attempt failed and retries are being attempted.",
    description:
      "Your subscription is pending activation. This usually happens when a payment is being processed.",
    animate: true,
  },
  [SUBSCRIPTION_STATUS.HALTED]: {
    label: "Halted",
    icon: AlertTriangle,
    className:
      "bg-gradient-to-r from-amber-100 to-amber-50 dark:from-amber-900/40 dark:to-amber-900/20 text-amber-700 dark:text-amber-300 border border-amber-300/70 dark:border-amber-700/70 shadow-sm",
    tooltip:
      "All payment retry attempts have failed. Please update your payment method.",
    description:
      "Your subscription has been temporarily halted. This may be due to a payment issue.",
    animate: true,
  },
  [SUBSCRIPTION_STATUS.CANCELLED]: {
    label: "Cancelled",
    icon: XCircle,
    className:
      "bg-gradient-to-r from-red-100 to-red-50 dark:from-red-900/40 dark:to-red-900/20 text-red-700 dark:text-red-300 border border-red-300/70 dark:border-red-700/70 shadow-sm",
    tooltip: "Your subscription has been cancelled.",
    description:
      "Your subscription has been cancelled. You no longer have access to premium features.",
    animate: false,
  },
  [SUBSCRIPTION_STATUS.EXPIRED]: {
    label: "Expired",
    icon: XCircle,
    className:
      "bg-gradient-to-r from-gray-100 to-gray-50 dark:from-gray-800/40 dark:to-gray-800/20 text-gray-700 dark:text-gray-300 border border-gray-300/70 dark:border-gray-700/70 shadow-sm",
    tooltip:
      "Your subscription has expired because authentication wasn't completed by the start time.",
    description:
      "Your subscription has expired. You no longer have access to premium features.",
    animate: false,
  },
  [SUBSCRIPTION_STATUS.COMPLETED]: {
    label: "Completed",
    icon: CheckCircle2,
    className:
      "bg-gradient-to-r from-purple-100 to-purple-50 dark:from-purple-900/40 dark:to-purple-900/20 text-purple-700 dark:text-purple-300 border border-purple-300/70 dark:border-purple-700/70 shadow-sm",
    tooltip: "Your subscription has completed its term.",
    description:
      "Your subscription has completed its full term. You no longer have access to premium features.",
    animate: false,
  },
  [SUBSCRIPTION_STATUS.TRIAL]: {
    label: "Trial",
    icon: CalendarClock,
    className:
      "bg-gradient-to-r from-amber-100 to-amber-50 dark:from-amber-900/40 dark:to-amber-900/20 text-amber-700 dark:text-amber-300 border border-amber-300/70 dark:border-amber-600/70 shadow-sm",
    tooltip: "You're currently in a trial period.",
    description:
      "Your trial is currently active. You have access to all premium features.",
    animate: true,
  },
  inactive: {
    label: "Inactive",
    icon: XCircle,
    className:
      "bg-gradient-to-r from-red-100 to-red-50 dark:from-red-900/40 dark:to-red-900/20 text-red-700 dark:text-red-300 border border-red-300/70 dark:border-red-700/70 shadow-sm",
    tooltip: "You don't have an active subscription.",
    description:
      "You don't have an active subscription. Choose a plan below to get started.",
    animate: false,
  },
};

export default function SubscriptionStatusBadge({
  status,
  className = "",
  showTooltip = true,
}: SubscriptionStatusBadgeProps) {
  const config = statusConfig[status] || {
    label: "Unknown",
    icon: HelpCircle,
    className: "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300",
    tooltip: "Unknown subscription status",
    animate: false,
  };

  const Icon = config.icon;

  const badgeContent = (
    <div className="flex items-center">
      {config.animate ? (
        <motion.div
          initial={{ opacity: 0.7, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            repeatType: "reverse"
          }}
          className="mr-1.5"
        >
          <Icon className="w-3.5 h-3.5" />
        </motion.div>
      ) : (
        <Icon className="w-3.5 h-3.5 mr-1.5" />
      )}
      {config.label}
    </div>
  );

  const badge = (
    <Badge
      variant="outline"
      className={cn(
        config.className,
        className,
        "px-2.5 py-1 text-xs font-medium"
      )}
    >
      {badgeContent}
    </Badge>
  );

  if (!showTooltip) {
    return badge;
  }

  return (
    <TooltipProvider>
      <Tooltip delayDuration={100}>
        <TooltipTrigger asChild>{badge}</TooltipTrigger>
        <TooltipContent
          side="top"
          className="bg-neutral-800/95 dark:bg-neutral-950/95 backdrop-blur-sm border border-neutral-700/50 dark:border-neutral-800/50 text-white text-xs p-3 rounded-lg shadow-lg max-w-xs"
        >
          <div className="flex items-start gap-2">
            <Icon className="w-4 h-4 text-primary mt-0.5" />
            <p>{config.tooltip}</p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
