import * as z from "zod";
import {
  businessCardSchema,
  type BusinessCardData,
  requiredFieldsForOnline,
  requiredFieldsForSaving,
} from "../schema";

/**
 * Validates business card data with different requirements based on status
 * @param formData - The business card data to validate
 * @returns Validation result with success/error information
 */
export function validateBusinessCardData(formData: BusinessCardData) {
  // First, validate required fields for saving regardless of status
  const baseSchema = businessCardSchema.refine(
    (data) => {
      return requiredFieldsForSaving.every(
        (field) => data[field] && String(data[field]).trim() !== ""
      );
    },
    {
      message: `Required fields missing: ${requiredFieldsForSaving.join(", ")}.`,
      path: ["member_name"], // Point to the first required field
    }
  );

  // Then, if status is online, also validate online-specific required fields
  const schemaToUse =
    formData.status === "online"
      ? baseSchema.refine(
          (data) => {
            return requiredFieldsForOnline.every(
              (field) => data[field] && String(data[field]).trim() !== ""
            );
          },
          {
            message: `Cannot set status to online. Required fields missing: ${requiredFieldsForOnline.join(
              ", "
            )}.`,
            path: ["status"],
          }
        )
      : baseSchema;

  return schemaToUse.safeParse(formData);
}

/**
 * Validates slug format
 * @param slug - The slug to validate
 * @returns Validation result
 */
export function validateSlugFormat(slug: string) {
  const slugValidation = z
    .string()
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .min(3)
    .safeParse(slug);
  
  return slugValidation;
}
