"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { ArrowRight, Mail, Phone } from "lucide-react";
import { Button } from "@/components/ui/button";
import { siteConfig } from "@/lib/site-config";

export default function ContactHeroSection() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  const buttonVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5, delay: 0.6 },
    },
    hover: {
      scale: 1.05,
      transition: { duration: 0.2 },
    },
    tap: {
      scale: 0.98,
      transition: { duration: 0.1 },
    },
  };

  const iconContainerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.8,
      },
    },
  };

  const iconVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 10 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: { duration: 0.5, type: "spring", stiffness: 100 },
    },
  };

  // Handle direct contact actions
  const handleEmailClick = () => {
    window.location.href = `mailto:${siteConfig.contact.email}`;
  };

  const handleCallClick = () => {
    window.location.href = `tel:${siteConfig.contact.phone.replace(/\s+/g, '')}`;
  };

  const handleGetStarted = () => {
    window.location.href = "/register";
  };

  return (
    <section className="relative py-16 md:py-24 px-4 overflow-hidden">
      {/* Background gradient effects */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 left-1/2 -translate-x-1/2 w-1/2 h-1/2 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-1/3 h-1/3 bg-blue-500/10 dark:bg-blue-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto max-w-7xl">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="text-center max-w-4xl mx-auto"
        >
          <motion.h1
            variants={itemVariants}
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6"
          >
            Get in <span className="text-[var(--brand-gold)] relative">
              Touch
              {isClient && (
                <motion.div
                  className="absolute -bottom-1 left-0 h-1 bg-gradient-to-r from-[var(--brand-gold)]/30 via-[var(--brand-gold)] to-[var(--brand-gold)]/30 rounded-full"
                  initial={{ width: 0, left: "50%" }}
                  animate={{ width: "100%", left: 0 }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              )}
            </span>
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto"
          >
            Have questions about Dukancard? Our team is here to help you elevate
            your digital presence. Reach out to us through any of the channels
            below.
          </motion.p>

          <motion.div
            variants={iconContainerVariants}
            className="flex flex-wrap justify-center gap-4 md:gap-6 mb-10"
          >
            <motion.div variants={iconVariants}>
              <Button
                onClick={handleEmailClick}
                variant="outline"
                size="lg"
                className="bg-white/80 dark:bg-black/50 backdrop-blur-sm border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group"
              >
                <Mail className="mr-2 h-5 w-5 text-[var(--brand-gold)] group-hover:scale-110 transition-transform" />
                Email Us
              </Button>
            </motion.div>

            <motion.div variants={iconVariants}>
              <Button
                onClick={handleCallClick}
                variant="outline"
                size="lg"
                className="bg-white/80 dark:bg-black/50 backdrop-blur-sm border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] group"
              >
                <Phone className="mr-2 h-5 w-5 text-[var(--brand-gold)] group-hover:scale-110 transition-transform" />
                Call Us
              </Button>
            </motion.div>


          </motion.div>

          <motion.div
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            <Button
              onClick={handleGetStarted}
              size="lg"
              className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-6 py-2 h-auto shadow-md hover:shadow-lg transition-all duration-300"
            >
              Get Started
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
