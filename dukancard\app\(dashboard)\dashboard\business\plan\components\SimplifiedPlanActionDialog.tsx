"use client";
import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Title,
  DialogHeader,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  CreditCard,
  CheckCircle,
  Sparkles,
  Shield,
  Store
} from "lucide-react";
import { PricingPlan } from "@/lib/PricingPlans";
import { cn } from "@/lib/utils";
import { useSubscriptionProcessing } from "../context/SubscriptionProcessingContext";
import { Badge } from "@/components/ui/badge";

import DialogBackground from "./DialogBackground";

// Animation variants
const dialogVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  },
};

const buttonVariants = {
  hover: { scale: 1.03 },
  tap: { scale: 0.98 },
};

// Helper function to get the correct icon based on plan name
const getPlanIcon = (planName: string) => {
  const iconProps = { className: "w-6 h-6 text-[var(--brand-gold)]" };

  if (planName.includes("Basic")) {
    return <CreditCard {...iconProps} />;
  } else if (planName.includes("Growth")) {
    return <Store {...iconProps} />;
  } else if (planName.includes("Pro")) {
    return <Shield {...iconProps} />;
  } else {
    return <Sparkles {...iconProps} />;
  }
};



interface SimplifiedPlanActionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  plan: PricingPlan;
  trialEndDate: string | null;
  _onSubscribe: () => Promise<void>;
  isLoading?: boolean; // Optional prop to control loading state from parent
  razorpaySubscriptionId?: string | null; // To detect if user has existing subscription
}

export default function SimplifiedPlanActionDialog({
  isOpen,
  onClose,
  plan,
  trialEndDate,
  _onSubscribe,
  isLoading: externalLoading,
  razorpaySubscriptionId,
}: SimplifiedPlanActionDialogProps) {
  // Get the subscription processing context functions
  const {
    resetProcessing
  } = useSubscriptionProcessing();

  // Determine if this is a subscription switch or fresh subscription
  const isSubscriptionSwitch = Boolean(razorpaySubscriptionId);
  const isTrialUser = Boolean(trialEndDate && new Date(trialEndDate) > new Date());

  // Ref for checking if component is mounted
  const isMounted = useRef(true);

  // State for client-side rendering check
  const [isClient, setIsClient] = useState(false);

  // State to track if Razorpay modal is open
  const [isRazorpayOpen, setIsRazorpayOpen] = useState(false);

  // Use internal loading state only if external loading is not provided
  const [internalLoading, setInternalLoading] = useState(false);


  // CRITICAL FIX: Add state to track if dialog was just opened to prevent immediate closure
  const [isDialogJustOpened, setIsDialogJustOpened] = useState(false);



  // Add debouncing state to prevent multiple rapid clicks
  const [isProcessing, setIsProcessing] = useState(false);

  // Use external loading state if provided, otherwise use internal state
  // If Razorpay modal is open, we don't want to show loading state
  // Also consider processing state for button disable
  const isLoading = isRazorpayOpen ? false : (externalLoading !== undefined ? externalLoading : internalLoading);
  const isButtonDisabled = isLoading || isProcessing;

  // Don't allow dialog to close while processing subscription
  const canCloseDialog = !isProcessing && !isLoading;

  // Set client-side rendering flag
  useEffect(() => {
    setIsClient(true);
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Reset processing state when dialog is closed or unmounted
  useEffect(() => {
    // Only run this effect when the dialog is closed
    if (!isOpen) {
      console.log('[DIALOG] Dialog closed - resetting all states');

      // Reset internal loading state
      if (externalLoading === undefined) {
        setInternalLoading(false);
      }

      // CRITICAL FIX: Aggressively reset Razorpay open state
      setIsRazorpayOpen(false);

      // Reset processing state to ensure no lingering toast notifications
      resetProcessing();

      // Reset the "just opened" flag when dialog is closed
      setIsDialogJustOpened(false);
    }
  }, [isOpen, externalLoading, resetProcessing]);

  // CRITICAL FIX: Add effect to ensure Razorpay state is reset when dialog opens
  useEffect(() => {
    if (isOpen) {
      console.log('[DIALOG] Dialog opened - ensuring Razorpay state is reset');
      // Force reset Razorpay state when dialog opens to prevent stuck state
      setIsRazorpayOpen(false);

      // CRITICAL FIX: Mark dialog as just opened to prevent immediate closure
      setIsDialogJustOpened(true);

      // Clear the "just opened" flag after a short delay
      const timer = setTimeout(() => {
        setIsDialogJustOpened(false);
      }, 500); // 500ms should be enough to prevent immediate closure

      return () => clearTimeout(timer);
    } else {
      // Reset the flag when dialog is closed
      setIsDialogJustOpened(false);
    }
  }, [isOpen]);

  // Fallback mechanism to reset Razorpay state if it gets stuck
  useEffect(() => {
    if (isRazorpayOpen) {
      console.log('[DIALOG] Razorpay modal detected as open, setting up fallback timer');

      // Set up a fallback timer to reset Razorpay state if it gets stuck
      const fallbackTimer = setTimeout(() => {
        console.log('[DIALOG] Fallback timer triggered - checking if Razorpay modal is still open');

        // Check if Razorpay elements still exist
        const razorpayFrame = document.querySelector('iframe[name^="rzp_"]');
        const razorpayBackdrop = document.querySelector('.razorpay-backdrop');
        const razorpayContainer = document.querySelector('.razorpay-container');
        const razorpayModal = document.querySelector('.razorpay-modal');

        const isStillOpen = !!(razorpayFrame || razorpayBackdrop || razorpayContainer || razorpayModal);

        if (!isStillOpen) {
          console.log('[DIALOG] Fallback: Razorpay modal no longer detected, resetting state');
          setIsRazorpayOpen(false);
        } else {
          console.log('[DIALOG] Fallback: Razorpay modal still detected, keeping state');
        }
      }, 30000); // 30 second fallback

      return () => {
        clearTimeout(fallbackTimer);
      };
    }
  }, [isRazorpayOpen]);

  // Listen for Razorpay modal opening
  useEffect(() => {
    // Function to detect Razorpay modal
    const checkForRazorpayModal = () => {
      try {
        // Razorpay creates an iframe with specific attributes and various container elements
        const razorpayFrame = document.querySelector('iframe[name^="rzp_"]');
        const razorpayBackdrop = document.querySelector('.razorpay-backdrop');
        const razorpayContainer = document.querySelector('.razorpay-container');
        const razorpayModal = document.querySelector('.razorpay-modal');

        // Additional selectors for different Razorpay modal types
        const razorpayCheckout = document.querySelector('#razorpay-checkout-frame');
        const razorpayOverlay = document.querySelector('.razorpay-overlay');
        const razorpayWrapper = document.querySelector('.razorpay-wrapper');

        // Check for any high z-index iframes that might be Razorpay
        const highZIndexIframes = Array.from(document.querySelectorAll('iframe')).filter(iframe => {
          try {
            const style = window.getComputedStyle(iframe);
            const zIndex = parseInt(style.zIndex);
            return zIndex > 1000; // Razorpay typically uses high z-index values
          } catch (_error) {
            // Ignore errors from cross-origin iframes
            return false;
          }
        });

        const isRazorpayVisible = !!(
          razorpayFrame ||
          razorpayBackdrop ||
          razorpayContainer ||
          razorpayModal ||
          razorpayCheckout ||
          razorpayOverlay ||
          razorpayWrapper ||
          highZIndexIframes.length > 0
        );

        if (isRazorpayVisible !== isRazorpayOpen) {
          console.log('[DIALOG] Razorpay modal state changed:', isRazorpayVisible);
          console.log('[DIALOG] Razorpay elements found:', {
            razorpayFrame: !!razorpayFrame,
            razorpayBackdrop: !!razorpayBackdrop,
            razorpayContainer: !!razorpayContainer,
            razorpayModal: !!razorpayModal,
            highZIndexIframes: highZIndexIframes.length
          });

          if (isRazorpayVisible) {
            console.log('[DIALOG] Razorpay modal opened - hiding our dialog to prevent interference');
          } else {
            console.log('[DIALOG] Razorpay modal closed - showing our dialog again');
          }

          setIsRazorpayOpen(isRazorpayVisible);
        }
      } catch (error) {
        console.warn('[DIALOG] Error checking for Razorpay modal:', error);
        // If there's an error, assume Razorpay is not open to prevent stuck state
        if (isRazorpayOpen) {
          console.log('[DIALOG] Resetting Razorpay state due to detection error');
          setIsRazorpayOpen(false);
        }
      }
    };

    // Only run the interval if the dialog is open
    if (isOpen) {
      // Set up an interval to check for Razorpay modal
      const intervalId = setInterval(checkForRazorpayModal, 300); // Check more frequently

      // Clean up interval on unmount
      return () => {
        clearInterval(intervalId);
      };
    }
  }, [isRazorpayOpen, isOpen]);



  const handleSubscribe = async () => {
    console.log('🔥 [DIALOG] BUTTON CLICKED - handleSubscribe called!');
    console.log('[DIALOG] Button state:', {
      isProcessing,
      externalLoading,
      isButtonDisabled
    });

    // Prevent multiple simultaneous subscription attempts
    if (isProcessing || (externalLoading !== undefined && externalLoading)) {
      console.log('[DIALOG] Already processing subscription, ignoring duplicate click');
      return;
    }

    try {
      // Set processing state to prevent duplicate clicks
      setIsProcessing(true);

      // Only set internal loading if we're not using external loading
      if (externalLoading === undefined) {
        setInternalLoading(true);
      }

      // Reset Razorpay open state before starting
      setIsRazorpayOpen(false);

      console.log('[DIALOG] Starting subscription process with create/cancel flow...');

      // Always use the centralized create/cancel flow for all payment methods
      await _onSubscribe();

      // Dialog will be closed by the parent component
    } catch (_error) {
      console.error('[DIALOG] Subscription error:', _error);

      // Keep the dialog open if there's an error
      // Only reset internal loading if we're not using external loading
      if (externalLoading === undefined && isMounted.current) {
        setInternalLoading(false);
      }
      // Also reset Razorpay open state in case of error
      setIsRazorpayOpen(false);
    } finally {
      // Always reset processing state
      setIsProcessing(false);
    }
  };



  // No need to extract features as per user request

  // CRITICAL FIX: Ensure dialog can always be opened by adding safety check
  const shouldShowDialog = isOpen && !isRazorpayOpen;

  // Debug logging for dialog state
  useEffect(() => {
    console.log('[DIALOG] Dialog state:', {
      isOpen,
      isRazorpayOpen,
      shouldShowDialog,
      canCloseDialog,
      isProcessing,
      isLoading,
      isDialogJustOpened
    });
  }, [isOpen, isRazorpayOpen, shouldShowDialog, canCloseDialog, isProcessing, isLoading, isDialogJustOpened]);

  return (
    <Dialog
      open={shouldShowDialog} // Hide dialog when Razorpay modal is open
      onOpenChange={(open) => {
        console.log('[DIALOG] onOpenChange called with:', open, {
          canCloseDialog,
          isRazorpayOpen,
          isDialogJustOpened,
          isOpen
        });

        // CRITICAL FIX: Prevent immediate closure if dialog was just opened
        if (!open && isDialogJustOpened) {
          console.log('[DIALOG] Dialog close ignored - dialog was just opened, preventing immediate closure');
          return;
        }

        if (!open && canCloseDialog && !isRazorpayOpen) {
          console.log('[DIALOG] Dialog closing allowed');
          // Reset internal loading state when dialog is closed
          if (externalLoading === undefined) {
            setInternalLoading(false);
          }
          // Reset processing state to ensure no lingering toast notifications
          resetProcessing();
          onClose();
        } else if (!open && !canCloseDialog) {
          console.log('[DIALOG] Dialog closing prevented - processing UPI/netbanking subscription');
        } else if (!open && isRazorpayOpen) {
          console.log('[DIALOG] Dialog close ignored - Razorpay modal is open');
        } else if (open && !isOpen) {
          console.log('[DIALOG] Dialog trying to open but isOpen is false - this might indicate a state issue');
        }
      }}
    >
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto w-[95vw] p-0 border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-xl rounded-xl">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={dialogVariants}
          className="relative overflow-hidden"
        >
          {/* Background gradient effect */}
          {isClient && <DialogBackground variant="gold" intensity="medium" />}

          {/* Header with plan icon and name */}
          <DialogHeader className="p-6 pb-4 border-b border-neutral-200 dark:border-neutral-800">
            <motion.div
              variants={itemVariants}
              className="flex items-center gap-3"
            >
              <div className="p-2 rounded-full bg-[var(--brand-gold)]/15">
                {getPlanIcon(plan.name)}
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-foreground">
                  {plan.name}
                </DialogTitle>
                {plan.recommended && (
                  <Badge variant="outline" className="mt-1 text-xs bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] border-[var(--brand-gold)]/20">
                    Recommended
                  </Badge>
                )}
              </div>
            </motion.div>
          </DialogHeader>

          {/* Content section */}
          <div className="p-6 space-y-6">
            {/* Price section with animation */}
            <motion.div variants={itemVariants} className="space-y-2">
              <div className="flex items-baseline gap-2">
                <span className="text-3xl font-bold text-foreground">
                  {plan.price}
                </span>
                <span className="text-muted-foreground text-sm">
                  {plan.period}
                </span>
              </div>

              {plan.savings && (
                <div className="text-green-600 dark:text-green-400 text-sm font-medium flex items-center gap-1.5">
                  <CheckCircle className="w-4 h-4" />
                  {plan.savings}
                </div>
              )}
            </motion.div>

            {/* Subscription note with animation */}
            <motion.div variants={itemVariants}>
              <div className="text-xs text-muted-foreground p-3 bg-neutral-100 dark:bg-neutral-900 rounded-lg">
                <p className="mb-1 font-medium">
                  {isSubscriptionSwitch ? "Plan Switch Notice:" : "Subscription Note:"}
                </p>
                <p>
                  {isSubscriptionSwitch
                    ? "Switching plans will cancel your current subscription and create a new one. You will lose access to your previous plan benefits immediately."
                    : isTrialUser
                    ? "Your subscription will be created and payment will be processed when your trial ends."
                    : "A new subscription will be created for the selected plan."
                  }
                </p>
              </div>
            </motion.div>
          </div>

          {/* Footer with buttons */}
          <motion.div variants={itemVariants}>
            <DialogFooter className="flex flex-col-reverse sm:flex-row gap-3 p-6 pt-4 border-t border-neutral-200 dark:border-neutral-800 bg-neutral-50 dark:bg-neutral-950">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="outline"
                  onClick={() => {
                    if (canCloseDialog) {
                      console.log('[DIALOG] Cancel button clicked - closing dialog');
                      if (externalLoading === undefined) {
                        setInternalLoading(false);
                      }
                      // Reset processing state to ensure no lingering toast notifications
                      resetProcessing();
                      onClose();
                    } else {
                      console.log('[DIALOG] Cancel button clicked but dialog close prevented - processing UPI/netbanking subscription');
                    }
                  }}
                  disabled={!canCloseDialog}
                  className="w-full sm:w-auto py-6 rounded-xl transition-all duration-200"
                >
                  Cancel
                </Button>
              </motion.div>

              <motion.div
                whileHover="hover"
                whileTap="tap"
                variants={buttonVariants}
                className="w-full sm:w-auto"
              >
                <Button
                  onClick={handleSubscribe}
                  className={cn(
                    "w-full relative overflow-hidden py-6",
                    "bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90",
                    "text-black dark:text-neutral-900 font-medium transition-all duration-200",
                    "shadow-[0_0_15px_rgba(var(--brand-gold-rgb),0.5)] hover:shadow-[0_0_20px_rgba(var(--brand-gold-rgb),0.7)]",
                    "rounded-xl"
                  )}
                  disabled={isButtonDisabled}
                >
                  {/* Shimmer effect */}
                  {isClient && !isLoading && !isProcessing && (
                    <motion.div
                      className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none"
                      initial={{ x: "-100%" }}
                      animate={{ x: "100%" }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    />
                  )}

                  {/* Background glow effect */}
                  {isClient && !isLoading && !isProcessing && (
                    <div className="absolute inset-0 w-full h-full opacity-75 pointer-events-none">
                      <div className="absolute inset-0 bg-[var(--brand-gold)]/20 blur-md rounded-xl" />
                    </div>
                  )}

                  {isLoading || isProcessing ? (
                    <div className="flex items-center justify-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>
                        {isProcessing ? "Starting..." : "Processing..."}
                      </span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <CreditCard className="w-4 h-4" />
                      <span>
                        {isSubscriptionSwitch ? "Switch Plan" : "Subscribe Now"}
                      </span>
                    </div>
                  )}
                </Button>
              </motion.div>
            </DialogFooter>
          </motion.div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
