"use client";

import { Card } from "@/components/ui/card";
import { CheckCircle, Star } from "lucide-react";

interface TestimonialCardProps {
  name: string;
  business: string;
  quote: string;
  _index: number;
  rating?: number;
}

export default function TestimonialCard({
  name,
  business,
  quote,
  _index,
  rating = 5,
}: TestimonialCardProps) {
  return (
    <div className="h-full">
      <Card className="bg-white/90 dark:bg-black/40 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 p-6 h-full hover:shadow-lg transition-all duration-300 hover:border-[var(--brand-gold)]/70 dark:hover:shadow-[var(--brand-gold)]/10 rounded-xl group relative overflow-hidden">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/0 to-purple-500/5 dark:from-transparent dark:to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        {/* Avatar and user info with enhanced styling */}
        <div className="flex items-center mb-6 relative z-10">
          <div className="relative">
            <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[var(--brand-gold)] via-amber-500 to-purple-500 flex items-center justify-center text-white font-bold text-lg shadow-md relative z-10">
              {name.charAt(0)}
            </div>
          </div>

          <div className="ml-4">
            <h3 className="text-lg font-semibold text-foreground">
              {name}
            </h3>
            <div className="flex items-center">
              <p className="text-purple-500 dark:text-purple-400 text-sm font-medium">
                {business}
              </p>
              {/* Verified badge */}
              <div className="ml-2 bg-green-100 dark:bg-green-900/30 rounded-full px-2 py-0.5 text-[10px] text-green-700 dark:text-green-400 font-medium flex items-center hover:scale-110 transition-transform duration-200">
                <CheckCircle className="w-3 h-3 mr-1" />
                Verified
              </div>
            </div>
          </div>
        </div>

        {/* Quote with enhanced styling */}
        <div className="relative mb-6 z-10">
          {/* Quote marks */}
          <div className="absolute -top-2 -left-1 text-purple-200 dark:text-purple-900 text-4xl opacity-50">&ldquo;</div>
          <p className="text-neutral-600 dark:text-neutral-300 italic pl-3 pr-3 relative">
            {quote}
          </p>
          <div className="absolute -bottom-4 -right-1 text-purple-200 dark:text-purple-900 text-4xl opacity-50">&rdquo;</div>
        </div>

        {/* Simple star rating */}
        <div className="flex text-[var(--brand-gold)] relative z-10">
          {Array(5)
            .fill(0)
            .map((_, i) => (
              <Star
                key={i}
                size={18}
                className={i < rating
                  ? "fill-[var(--brand-gold)] text-[var(--brand-gold)]"
                  : "text-neutral-300 dark:text-neutral-600"
                }
              />
            ))}
        </div>
      </Card>
    </div>
  );
}
