"use client";

import { AlertCircle, Calendar, Info, XCircle } from "lucide-react";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
} from "@/components/ui/alert-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface TrialSubscriptionWarningDialogProps {
  open: boolean;
  onOpenChange: (_open: boolean) => void;
  subscriptionId: string;
  onCancelSubscription: () => void;
}

export default function TrialSubscriptionWarningDialog({
  open,
  onOpenChange,
  onCancelSubscription,
}: TrialSubscriptionWarningDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md max-h-[90vh] overflow-y-auto border-neutral-200 dark:border-neutral-800 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm shadow-xl">
        {/* Dialog Header with Badge */}
        <div className="mb-4">
          <Badge
            variant="outline"
            className="w-fit mb-2 bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50 px-2 py-1"
          >
            <Calendar className="h-3.5 w-3.5 mr-1" />
            Trial Subscription
          </Badge>

          {/* Title with Icon */}
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2.5 rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400">
              <AlertCircle className="h-5 w-5" />
            </div>
            <h2 className="text-xl font-bold">Action Required</h2>
          </div>

          {/* Description */}
          <p className="text-muted-foreground">
            You are currently on a trial period with an authorized subscription
            that will activate when your trial ends.
          </p>
        </div>

        {/* Dialog Content */}
        <div className="space-y-4">
          {/* Information Card */}
          <div className="rounded-lg border border-neutral-200 dark:border-neutral-700/50 overflow-hidden">
            <div className="bg-neutral-50 dark:bg-neutral-800/50 px-4 py-2 border-b border-neutral-200 dark:border-neutral-700/50">
              <h3 className="font-medium flex items-center gap-2">
                <Info className="h-4 w-4 text-blue-500" />
                What You Need to Know
              </h3>
            </div>
            <div className="p-4 space-y-3 text-sm">
              <div className="flex items-start gap-2">
                <XCircle className="h-4 w-4 text-amber-500 mt-0.5" />
                <p>
                  To switch to a different plan or billing cycle, you need to
                  cancel your current subscription first.
                </p>
              </div>
              <div className="flex items-start gap-2">
                <Calendar className="h-4 w-4 text-green-500 mt-0.5" />
                <p>
                  After cancellation, you can select and subscribe to any
                  available plan.
                </p>
              </div>
            </div>
          </div>

          {/* Note about subscription */}
          <div className="text-sm text-muted-foreground bg-muted/30 p-3 rounded-lg">
            <p>
              Your trial benefits will continue until the end of your trial
              period, even after cancelling the future subscription.
            </p>
          </div>
        </div>

        {/* Dialog Footer */}
        <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-between gap-3 sm:gap-2 pt-4 mt-2">
          <AlertDialogCancel className="w-full sm:w-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800">
            Close
          </AlertDialogCancel>
          <Button
            variant="destructive"
            onClick={onCancelSubscription}
            className="w-full sm:w-auto bg-red-600 hover:bg-red-700"
          >
            Cancel Future Subscription
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
