"use client";

import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { useState } from "react";
import PricingCard from "@/app/components/PricingCard";
import { PricingCardProvider } from "@/app/components/PricingCardContext";
import { pricingPlans, PricingPlan } from "@/lib/PricingPlans";
import SectionBackground from "../SectionBackground";
import BillingToggle from "./BillingToggle";
import FloatingPricingElements from "./FloatingPricingElements";
import { sectionFadeIn, itemFadeIn } from "./animations";

export default function PricingSection() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("monthly");
  const [, setIsHovering] = useState(false);
  const router = useRouter();
  const plans = pricingPlans(billingCycle);

  const handleGetStarted = () => {
    router.push(`/register`);
  };

  const handlePlanClick = (plan: PricingPlan) => {
    if (plan.id === "enterprise") {
      // For Enterprise, redirect to contact page
      router.push("/contact");
    } else if (plan.available) {
      handleGetStarted();
    }
  };

  // Add hover state for enhanced animations
  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
  };

  return (
    <PricingCardProvider>
      <motion.section
        variants={sectionFadeIn}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.05 }} // Reveal when just 5% is in view
        className="py-10 px-2 md:px-4 max-w-full mx-auto relative"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
      {/* Enhanced background with SectionBackground component */}
      <div className="absolute inset-0 -z-10">
        <SectionBackground variant="blue" intensity="low" />
      </div>

      {/* Floating pricing elements */}
      <FloatingPricingElements />
      <motion.div
        variants={itemFadeIn}
        custom={0}
        className="text-center max-w-3xl mx-auto mb-12"
      >
        <div className="relative inline-block">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Simple,{" "}
            <span className="text-[var(--brand-gold)] relative">
              Transparent
              {/* Animated underline */}
              <motion.div
                className="absolute -bottom-1 left-0 h-1 bg-gradient-to-r from-[var(--brand-gold)]/30 via-[var(--brand-gold)] to-[var(--brand-gold)]/30 rounded-full"
                initial={{ width: 0, left: "50%" }}
                animate={{ width: "100%", left: 0 }}
                transition={{ duration: 1, delay: 0.5 }}
              />
              {/* Subtle glow effect */}
              <motion.div
                className="absolute -inset-2 bg-[var(--brand-gold)]/20 rounded-full blur-xl"
                animate={{ opacity: 0.8 }}
                initial={{ opacity: 0.4 }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
              />
            </span>{" "}
            Pricing
          </h2>
        </div>
        <p className="text-lg text-neutral-600 dark:text-neutral-300 mb-8 max-w-2xl mx-auto">
          Choose the perfect plan for your business needs and elevate your
          digital presence with Dukancard&apos;s premium digital card
          solutions.
        </p>
      </motion.div>

      {/* Billing Toggle */}
      <motion.div
        variants={itemFadeIn}
        custom={1}
        className="flex justify-center items-center mb-16"
      >
        <BillingToggle
          billingCycle={billingCycle}
          setBillingCycle={setBillingCycle}
        />
      </motion.div>

      {/* Pricing Cards with enhanced animations */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-1 md:gap-2 items-stretch px-1 md:px-2">
        {plans.map((plan, index) => (
          <motion.div
            key={plan.id}
            custom={index + 2} // Stagger delay
            variants={itemFadeIn}
            className="w-full relative" // Added relative for positioning
          >
            {/* Highlight glow for popular plan */}
            {plan.mostPopular && (
              <motion.div
                className="absolute -inset-2 bg-[var(--brand-gold)]/20 dark:bg-[var(--brand-gold)]/30 rounded-xl blur-lg -z-10"
                initial={{ opacity: 0.5, scale: 0.95 }}
                animate={{
                  opacity: [0.5, 0.8, 0.5],
                  scale: [0.95, 1, 0.95]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "mirror"
                }}
              />
            )}

            {/* Enhanced card without hover effect */}
            <div>
              <PricingCard
                plan={plan}
                index={index}
                onButtonClick={() => handlePlanClick(plan)}
              />
            </div>

            {/* Removed redundant "Most Popular" label */}
          </motion.div>
        ))}
      </div>
    </motion.section>
    </PricingCardProvider>
  );
}
