"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Card } from "@/components/ui/card";
import {
  Globe,
  Users,
  ShieldCheck,
  BookOpen,
  HeartHandshake,
  Sparkles,
} from "lucide-react";

export default function CoreValuesSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.1 });
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };
  
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, delay: i * 0.1 },
    }),
  };

  // Core values data
  const values = [
    {
      icon: <Globe className="w-10 h-10 text-[var(--brand-gold)]" />,
      title: "Accessibility",
      description:
        "We believe every business, regardless of size or technical ability, should have access to premium digital tools.",
    },
    {
      icon: <Users className="w-10 h-10 text-[var(--brand-gold)]" />,
      title: "Community",
      description:
        "We're building more than a product - we're creating a network of digitally empowered businesses.",
    },
    {
      icon: <ShieldCheck className="w-10 h-10 text-[var(--brand-gold)]" />,
      title: "Integrity",
      description:
        "We operate with transparency and honesty in all our customer relationships.",
    },
    {
      icon: <BookOpen className="w-10 h-10 text-[var(--brand-gold)]" />,
      title: "Education",
      description:
        "We're committed to helping businesses understand and leverage digital tools effectively.",
    },
    {
      icon: <HeartHandshake className="w-10 h-10 text-[var(--brand-gold)]" />,
      title: "Partnership",
      description:
        "We see ourselves as partners in our customers' success, not just a service provider.",
    },
    {
      icon: <Sparkles className="w-10 h-10 text-[var(--brand-gold)]" />,
      title: "Innovation",
      description:
        "We continuously evolve our platform to meet the changing needs of businesses.",
    },
  ];

  return (
    <section 
      ref={sectionRef}
      className="py-20 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-16"
      >
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Our Core <span className="text-[var(--brand-gold)]">Values</span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          The principles that guide everything we do at Dukancard.
        </p>
      </motion.div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
      >
        {values.map((value, index) => (
          <motion.div
            key={index}
            custom={index}
            variants={itemVariants}
            whileHover={{ 
              y: -10,
              transition: { type: "spring", stiffness: 300 }
            }}
          >
            <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/20 p-8 h-full hover:border-primary/50 dark:hover:border-[var(--brand-gold)]/50 transition-all duration-300 hover:shadow-lg dark:hover:shadow-lg dark:hover:shadow-[var(--brand-gold)]/10">
              <div className="relative mb-6">
                {/* Icon with animated glow effect */}
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  {value.icon}
                </motion.div>
                
                {/* Glow effect */}
                <motion.div
                  className="absolute inset-0 rounded-full blur-md -z-10"
                  animate={{
                    opacity: [0.2, 0.4, 0.2],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse",
                    delay: index * 0.3,
                  }}
                  style={{
                    backgroundColor: "rgba(var(--brand-gold-rgb), 0.2)",
                  }}
                />
              </div>
              
              <h3 className="text-xl font-semibold text-card-foreground mb-2">
                {value.title}
              </h3>
              
              <p className="text-muted-foreground">
                {value.description}
              </p>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </section>
  );
}
