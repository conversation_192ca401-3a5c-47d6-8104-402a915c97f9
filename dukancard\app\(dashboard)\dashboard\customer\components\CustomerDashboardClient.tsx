"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { User, Settings } from "lucide-react";

import CustomerMetricsOverview from "./CustomerMetricsOverview";

interface CustomerDashboardClientProps {
  customerName: string;
  _customerEmail?: string;
  userId: string;
  initialReviewCount: number;
  initialSubscriptionCount: number;
  initialLikesCount: number;
}

export default function CustomerDashboardClient({
  customerName,
  _customerEmail,
  userId,
  initialReviewCount,
  initialSubscriptionCount,
  initialLikesCount,
}: CustomerDashboardClientProps) {
  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-6"
    >
      <motion.div variants={itemVariants} className="mb-6">
        {/* Main card container */}
        <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-4 sm:p-5 md:p-6 mb-4 transition-all duration-300 hover:shadow-lg">
          <div>
            {/* Welcome Section */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
              <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
                <User className="w-4 sm:w-5 h-4 sm:h-5" />
              </div>
              <div className="flex-1">
                <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Welcome, {customerName}
                </h3>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                  Manage your subscriptions and interactions
                </p>
              </div>
              <div className="mt-2 sm:mt-0">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-auto inline-flex"
                >
                  <div className="relative group inline-flex">
                    {/* Strong inner glow effect that fills only the button */}
                    <div className="absolute inset-0 bg-purple-500/5 dark:bg-purple-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none" />

                    {/* Border glow effect - matches button size */}
                    <div
                      className="absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300"
                      style={{
                        boxShadow: `inset 0 0 20px rgba(147, 51, 234, 0.2), 0 0 20px rgba(147, 51, 234, 0.3)`
                      }}
                    />

                    {/* Strong decorative colored glow elements - positioned relative to button */}
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none" />
                    <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none" />

                    <Button
                      asChild
                      variant="outline"
                      size="sm"
                      className={`
                        relative overflow-hidden rounded-xl p-3
                        bg-white dark:bg-black
                        border border-purple-200/50 dark:border-purple-700/50
                        shadow-purple-500/40 shadow-lg
                        hover:shadow-xl hover:shadow-purple-500/40
                        transition-all duration-300
                        text-purple-500 dark:text-purple-400
                        hover:bg-purple-500/5 dark:hover:bg-purple-500/10
                        text-xs sm:text-sm h-auto
                      `}
                    >
                      <Link href="/dashboard/customer/profile" className="flex items-center relative">
                        <Settings className="mr-1.5 h-3.5 w-3.5" />
                        Edit Profile

                        {/* Shimmer effect - only on hover */}
                        <motion.div
                          className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100"
                          initial={{ x: "-100%" }}
                          whileHover={{
                            x: "100%",
                            transition: {
                              duration: 0.6,
                              ease: "easeInOut"
                            }
                          }}
                        />
                      </Link>
                    </Button>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Customer Metrics Overview */}
            <div className="mb-8">
              <CustomerMetricsOverview
                initialReviewCount={initialReviewCount}
                initialSubscriptionCount={initialSubscriptionCount}
                initialLikesCount={initialLikesCount}
                userId={userId}
              />
            </div>


          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
