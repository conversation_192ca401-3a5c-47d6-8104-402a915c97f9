import {
  Toolt<PERSON>,
  TooltipContent,
  Toolt<PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Phone, MapPin, Clock, Truck, Mail } from "lucide-react";
import { formatTimeTo12Hour, formatDayGroup } from "./utils/cardUtils";

interface CardBusinessInfoProps {
  fullAddress?: string;
  displayAddressLine?: string;
  locality?: string;
  displayCityStatePin?: string;
  phone?: string;
  displayPhone?: string;
  isAuthenticated: boolean;
  telUrl?: string;
  displayEmail?: string;
  mailtoUrl?: string;
  business_hours?: Record<string, { isOpen?: boolean; openTime?: string; closeTime?: string }> | null;
  delivery_info?: string;
}

export default function CardBusinessInfo({
  fullAddress,
  displayAddressLine,
  locality,
  displayCityStatePin,
  phone,
  displayPhone,
  isAuthenticated,
  telUrl,
  displayEmail,
  mailtoUrl,
  business_hours,
  delivery_info,
}: CardBusinessInfoProps) {
  return (
    <div className="flex flex-col gap-2 sm:gap-3 max-w-xs mx-auto overflow-hidden">
      {/* Address section (on top) */}
      {fullAddress && (
        <div className="text-sm text-neutral-700 dark:text-neutral-100 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 sm:p-2.5 rounded-lg w-full">
          <div className="flex items-start mb-2.5">
            <MapPin className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-[var(--theme-color)]" />
            <div className="flex flex-col overflow-hidden">
              {displayAddressLine && locality ? (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="font-medium text-xs line-clamp-1 cursor-default">
                        {displayAddressLine}, {locality}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        {displayAddressLine}, {locality}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                <>
                  {displayAddressLine && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="font-medium text-xs line-clamp-1 cursor-default">
                            {displayAddressLine}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{displayAddressLine}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  {locality && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="text-xs text-neutral-600 dark:text-neutral-300 line-clamp-1 cursor-default">
                            {locality}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{locality}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </>
              )}
              {displayCityStatePin && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="text-xs text-neutral-500 dark:text-neutral-400 line-clamp-1 cursor-default">
                        {displayCityStatePin}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{displayCityStatePin}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>


        </div>
      )}
      {/* Contact info section (below address) */}
      <div className="text-sm text-neutral-700 dark:text-neutral-100 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 sm:p-2.5 rounded-lg w-full">
        {/* Phone */}
        {phone && (
          <div className="flex items-center mb-2.5">
            <Phone className="w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]" />
            <div className="overflow-hidden">
              {displayPhone && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <a
                        href={isAuthenticated && telUrl ? telUrl : "#"}
                        className={
                          isAuthenticated && telUrl
                            ? "hover:underline font-medium text-xs truncate block"
                            : "cursor-default font-medium text-xs truncate block"
                        }
                      >
                        {displayPhone}
                      </a>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{displayPhone}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
        )}
        {/* Email */}
        {displayEmail && (
          <div className="flex items-center mb-2.5">
            <Mail className="w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]" />
            <div className="overflow-hidden">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <a
                      href={
                        isAuthenticated && mailtoUrl ? mailtoUrl : "#"
                      }
                      className={
                        isAuthenticated
                          ? "hover:underline font-medium text-xs truncate block"
                          : "cursor-default font-medium text-xs truncate block"
                      }
                    >
                      {displayEmail}
                    </a>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{displayEmail}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        )}
        {/* Business hours - only show if at least one day is open */}
        {(() => {
          // Check if business_hours exists and is an object
          if (!business_hours || typeof business_hours !== "object" || Object.keys(business_hours).length === 0) {
            return null;
          }

          try {
            // Check if at least one day has isOpen: true
            const hasOpenDay = Object.values(business_hours).some(
              (hours) => hours && typeof hours === "object" && (hours as { isOpen?: boolean }).isOpen
            );

            // If no days are open, don't show the business hours section
            if (!hasOpenDay) {
              return null;
            }

            // Get open days and their hours
            const openDays = Object.entries(
              business_hours as Record<string, unknown>
            )
              .filter(([, hours]) => {
                return (
                  hours &&
                  typeof hours === "object" &&
                  (hours as { isOpen?: boolean }).isOpen
                );
              })
              .map(([day, hours]) => {
                const hourData = hours as {
                  isOpen: boolean;
                  openTime?: string;
                  closeTime?: string;
                };
                return {
                  day,
                  hours:
                    hourData.openTime && hourData.closeTime
                      ? `${formatTimeTo12Hour(
                          hourData.openTime
                        )} - ${formatTimeTo12Hour(
                          hourData.closeTime
                        )}`
                      : "",
                };
              })
              .filter((item) => item.hours);

            // If no valid open days with hours, return null
            if (openDays.length === 0) {
              return null;
            }

            // Group days with the same hours
            const hourGroups: Record<string, string[]> = {};
            openDays.forEach(({ day, hours }) => {
              if (!hourGroups[hours]) {
                hourGroups[hours] = [];
              }
              hourGroups[hours].push(day);
            });

            // Return the business hours section with formatted day groups
            return (
              <div className="flex items-start mb-2.5">
                <Clock className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-[var(--theme-color)]" />
                <div className="text-xs font-medium">
                  {Object.entries(hourGroups).map(
                    ([hours, days], index) => {
                      // Format days (e.g., "Mon, Tue, Wed" or "Mon-Wed")
                      const formattedDays = formatDayGroup(days);

                      return (
                        <div
                          key={index}
                          className="flex justify-between"
                        >
                          <span className="capitalize">
                            {formattedDays}:
                          </span>
                          <span className="ml-2">{hours}</span>
                        </div>
                      );
                    }
                  )}
                </div>
              </div>
            );
          } catch (error) {
            // If there's an error parsing the business hours, return null
            console.error("Error parsing business hours:", error);
            return null;
          }
        })()}
        {/* Delivery info - now available for all users */}
        {delivery_info && (
          <div className="flex items-center">
            <Truck className="w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]" />
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <p className="font-medium text-xs line-clamp-1 cursor-default">
                    {delivery_info}
                  </p>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{delivery_info}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
      </div>
    </div>
  );
}