"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
// Unused import removed: import { Button } from "@/components/ui/button";
import {
  // Unused import removed: AlertCircle,
  PauseCircle,
  PlayCircle,
  RefreshCw,
  XCircle,
  // Unused import removed: Shield,
  Settings,
} from "lucide-react";
import { SubscriptionStatusType } from "@/lib/types/subscription";
import { ReactNode } from "react";
import ModernRefundDialog from "./ModernRefundDialog";
import ModernCancellationDialog from "./ModernCancellationDialog";
import {
  SUBSCRIPTION_STATUS,
  SubscriptionStateManager
} from "@/lib/razorpay/webhooks/handlers/utils";

interface EnhancedSubscriptionActionCardProps {
  subscriptionId: string;
  status: SubscriptionStatusType;
  isEligibleForRefund?: boolean;
  onCancelSubscription?: (_cancelImmediately?: boolean) => Promise<void>;
  onPauseSubscription?: () => Promise<void>;
  onResumeSubscription?: () => Promise<void>;
  onRequestRefund?: (_speed: "normal" | "optimum") => Promise<void>;
  planId?: string; // Added planId to check if user is on free plan
}

export default function EnhancedSubscriptionActionCard({
  status,
  isEligibleForRefund = false,
  onCancelSubscription,
  onPauseSubscription,
  onResumeSubscription,
  onRequestRefund,
  subscriptionId: _subscriptionId,
  planId = ""
}: EnhancedSubscriptionActionCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showRefundDialog, setShowRefundDialog] = useState(false);
  const [refundSpeed] = useState<"normal" | "optimum">("normal");
  const [activeAction, setActiveAction] = useState<string | null>(null);
  const [_cancelImmediately, setCancelImmediately] = useState(false);

  // Handle cancel subscription - not used directly but kept for reference
  const _handleCancelSubscription = async () => {
    if (!onCancelSubscription) return;

    // Close the dialog first before starting the cancellation process
    setShowCancelDialog(false);

    // Wait a short time for the dialog to close
    await new Promise(resolve => setTimeout(resolve, 300));

    setIsLoading(true);
    try {
      await onCancelSubscription();
    } catch (error) {
      console.error("Error cancelling subscription:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle pause subscription
  const handlePauseSubscription = async () => {
    if (!onPauseSubscription) return;

    setIsLoading(true);
    try {
      await onPauseSubscription();
    } catch (error) {
      console.error("Error pausing subscription:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle resume subscription
  const handleResumeSubscription = async () => {
    if (!onResumeSubscription) return;

    setIsLoading(true);
    try {
      await onResumeSubscription();
    } catch (error) {
      console.error("Error resuming subscription:", error);
    } finally {
      setIsLoading(false);
    }
  };



  // Handle request refund
  const handleRequestRefund = async () => {
    if (!onRequestRefund) return;

    setIsLoading(true);
    try {
      await onRequestRefund(refundSpeed);
      setShowRefundDialog(false);
    } catch (error) {
      console.error("Error requesting refund:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user is on free plan using centralized logic
  // IMPORTANT: For paused subscriptions, we temporarily downgrade to free plan but they still have razorpay_subscription_id
  // So we should show resume/cancel buttons for paused subscriptions even if plan_id is "free"
  const isFreePlan = SubscriptionStateManager.isFreeStatus(status, planId);
  const isPausedSubscription = status === "paused" || status === SUBSCRIPTION_STATUS.HALTED;

  // For paused subscriptions, we should show buttons even if they're temporarily on free plan
  const shouldShowButtons = !isFreePlan || isPausedSubscription;

  console.log('[SUBSCRIPTION_ACTIONS] Button visibility logic:', {
    status,
    planId,
    isFreePlan,
    isPausedSubscription,
    shouldShowButtons,
    subscriptionId: _subscriptionId
  });

  // Determine which actions are available based on subscription status and plan using centralized logic
  // Free plan users cannot cancel, pause, resume, or request refunds as it's not related to Razorpay
  // EXCEPT for paused subscriptions which are temporarily on free plan but have valid razorpay_subscription_id
  const cancellableStatuses: string[] = [SUBSCRIPTION_STATUS.ACTIVE, SUBSCRIPTION_STATUS.AUTHENTICATED, "paused", SUBSCRIPTION_STATUS.HALTED];
  const refundableStatuses: string[] = [SUBSCRIPTION_STATUS.ACTIVE, SUBSCRIPTION_STATUS.CANCELLED];

  const canCancel = shouldShowButtons && cancellableStatuses.includes(status) && onCancelSubscription;
  const canPause = shouldShowButtons && status === SUBSCRIPTION_STATUS.ACTIVE && onPauseSubscription;
  const canResume = shouldShowButtons && (status === "paused" || status === SUBSCRIPTION_STATUS.HALTED) && onResumeSubscription;
  const canRequestRefund = shouldShowButtons && isEligibleForRefund && refundableStatuses.includes(status) && onRequestRefund;

  // If no actions are available, don't render the card
  if (!canCancel && !canPause && !canResume && !canRequestRefund) {
    return null;
  }

  // Define interface for action items
  interface ActionItem {
    id: string;
    icon: ReactNode;
    label: string;
    description: string;
    color: string;
    textColor: string;
    bgColor: string;
    borderColor: string;
    action: () => void;
  }

  // Define action items
  const actionItems = [
    canPause && {
      id: "pause",
      icon: <PauseCircle className="h-5 w-5" />,
      label: "Pause Subscription",
      description: "Temporarily pause your subscription",
      color: "bg-amber-500",
      textColor: "text-amber-600 dark:text-amber-400",
      bgColor: "bg-amber-50 dark:bg-amber-900/20",
      borderColor: "border-amber-100 dark:border-amber-800/30",
      action: handlePauseSubscription
    },
    canResume && {
      id: "resume",
      icon: <PlayCircle className="h-5 w-5" />,
      label: "Resume Subscription",
      description: "Resume your paused subscription",
      color: "bg-green-500",
      textColor: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-50 dark:bg-green-900/20",
      borderColor: "border-green-100 dark:border-green-800/30",
      action: handleResumeSubscription
    },
    canRequestRefund && {
      id: "refund",
      icon: <RefreshCw className="h-5 w-5" />,
      label: "Request Refund",
      description: "Request a refund for your subscription",
      color: "bg-purple-500",
      textColor: "text-purple-600 dark:text-purple-400",
      bgColor: "bg-purple-50 dark:bg-purple-900/20",
      borderColor: "border-purple-100 dark:border-purple-800/30",
      action: () => setShowRefundDialog(true)
    },
    canCancel && {
      id: "cancel",
      icon: <XCircle className="h-6 w-6" />,
      label: "Cancel Subscription",
      description: "Cancel your subscription permanently",
      color: "bg-red-500",
      textColor: "text-red-600 dark:text-red-400",
      bgColor: "bg-red-50 dark:bg-red-900/20",
      borderColor: "border-red-100 dark:border-red-800/30",
      action: () => setShowCancelDialog(true)
    }
  ].filter(Boolean) as ActionItem[];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="w-full"
    >
      <Card className="overflow-hidden backdrop-blur-sm bg-white/80 dark:bg-black/50 border border-neutral-200/80 dark:border-neutral-800/80 shadow-sm">
        <CardHeader className="pb-2 bg-gradient-to-br from-white/90 to-white/70 dark:from-black/70 dark:to-black/50 border-b border-neutral-200/80 dark:border-neutral-800/80">
          <div className="flex items-center gap-2">
            <motion.div
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
              className="p-2 rounded-full bg-primary/10 text-primary"
            >
              <Settings className="h-5 w-5" />
            </motion.div>
            <div>
              <CardTitle className="text-xl">Manage Subscription</CardTitle>
              <CardDescription>
                Manage your subscription settings and payment methods
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-4">
          <div className="flex flex-col items-center">
            {actionItems.map((item) => (
              <motion.div
                key={item.id}
                whileHover={{ y: -5 }}
                whileTap={{ scale: 0.98 }}
                onHoverStart={() => setActiveAction(item.id)}
                onHoverEnd={() => setActiveAction(null)}
                className={`relative overflow-hidden rounded-xl ${item.bgColor} border ${item.borderColor} p-4 cursor-pointer transition-all duration-300 w-full max-w-sm mx-auto mb-4 ${item.id === 'cancel' ? 'shadow-md hover:shadow-lg' : ''}`}
                onClick={item.action}
              >
                {/* Background animation */}
                <AnimatePresence>
                  {activeAction === item.id && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 0.05, scale: 1 }}
                      exit={{ opacity: 0, scale: 0 }}
                      transition={{ duration: 0.3 }}
                      className={`absolute inset-0 rounded-full ${item.color}`}
                      style={{
                        top: '50%',
                        left: '50%',
                        width: '150%',
                        height: '150%',
                        transform: 'translate(-50%, -50%)'
                      }}
                    />
                  )}
                </AnimatePresence>

                <div className="relative z-10 flex flex-col items-center text-center space-y-2">
                  <div className={`rounded-full ${item.color} text-white ${item.id === 'cancel' ? 'p-4' : 'p-3'}`}>
                    {item.icon}
                  </div>
                  <h3 className={`font-medium ${item.textColor} ${item.id === 'cancel' ? 'text-lg' : ''}`}>{item.label}</h3>
                  <p className="text-xs text-muted-foreground">{item.description}</p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Modern Cancellation Dialog */}
          <ModernCancellationDialog
            isOpen={showCancelDialog}
            onClose={() => setShowCancelDialog(false)}
            onCancelSubscription={async (cancelImmediately) => {
              // Close the dialog first before starting the cancellation process
              setShowCancelDialog(false);

              // Wait a short time for the dialog to close
              await new Promise(resolve => setTimeout(resolve, 300));

              // Store the cancelImmediately value in the component state
              // This is needed to pass it to the SubscriptionManager component
              setCancelImmediately(cancelImmediately);

              // Call the parent's onCancelSubscription function with the cancelImmediately parameter
              if (onCancelSubscription) {
                try {
                  setIsLoading(true);
                  await onCancelSubscription(cancelImmediately);
                } catch (error) {
                  console.error("Error cancelling subscription:", error);
                  // Error handling will be done in the parent component
                } finally {
                  setIsLoading(false);
                }
              }
            }}
            isLoading={isLoading}
            isWithinRefundWindow={isEligibleForRefund}
            subscriptionStatus={status}
            effectiveCancellationDate={null}
            authenticatedSubscription={status === "authenticated" ? { id: "authenticated" } : null}
          />

          {/* Modern Refund Dialog */}
          <ModernRefundDialog
            isOpen={showRefundDialog}
            onClose={() => setShowRefundDialog(false)}
            onRequestRefund={handleRequestRefund}
            isLoading={isLoading}
            isEligibleForRefund={isEligibleForRefund}
          />
        </CardContent>
      </Card>
    </motion.div>
  );
}
