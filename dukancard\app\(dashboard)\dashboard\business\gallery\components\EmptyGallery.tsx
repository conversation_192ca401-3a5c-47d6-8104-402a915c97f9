import { motion } from "framer-motion";
import { Image as ImageIcon, Upload } from "lucide-react";
import { cn } from "@/lib/utils";
import { EnhancedGlowButton } from "@/components/ui/enhanced-glow-button";

interface EmptyGalleryProps {
  canAddMore: boolean;
  isDragging: boolean;
  userPlan: string;
  galleryLimit: number;
  onUploadClick: () => void;
}

export default function EmptyGallery({
  canAddMore,
  isDragging,
  userPlan,
  galleryLimit,
  onUploadClick,
}: EmptyGalleryProps) {
  return (
    <motion.div
      className={cn(
        "text-center py-16 border-2 border-dashed rounded-xl bg-muted/30 transition-all duration-300",
        canAddMore && "hover:bg-muted/50 cursor-pointer",
        isDragging && canAddMore && "border-amber-500 bg-amber-500/10"
      )}
      initial={{ scale: 0.95, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ delay: 0.3 }}
      onClick={canAddMore ? onUploadClick : undefined}
    >
      <div className={cn(
        "w-16 h-16 mx-auto rounded-full bg-amber-500/10 flex items-center justify-center mb-4 transition-all duration-300",
        isDragging && canAddMore && "bg-amber-500/20 scale-110"
      )}>
        <ImageIcon className={cn(
          "h-8 w-8 text-amber-500 opacity-80 transition-all duration-300",
          isDragging && canAddMore && "opacity-100"
        )} />
      </div>
      <h3 className="text-xl font-medium mb-2">
        {canAddMore ? "No photos yet" : "Gallery limit reached"}
      </h3>
      <p className="text-sm text-muted-foreground max-w-md mx-auto mb-6">
        {canAddMore ? (
          <>
            Add photos to showcase your business, products, or services.
            {userPlan === "free"
              ? " Free plan allows 1 photo."
              : userPlan === "basic"
              ? " Basic plan allows 3 photos."
              : userPlan === "growth"
              ? " Growth plan allows 10 photos."
              : userPlan === "pro"
              ? " Pro plan allows 50 photos."
              : userPlan === "enterprise"
              ? " Enterprise plan allows 100 photos."
              : ""}
            <br />
            <span className="text-amber-600 dark:text-amber-400 font-medium">
              Click here or drag and drop images to upload.
            </span>
          </>
        ) : (
          <>
            You&apos;ve reached your plan&apos;s gallery limit of {galleryLimit} photos.
            <br />
            <span className="text-blue-600 dark:text-blue-400 font-medium">
              Upgrade your plan to add more photos.
            </span>
          </>
        )}
      </p>
      {canAddMore && (
        <EnhancedGlowButton
          onClick={(e) => {
            e.stopPropagation();
            onUploadClick();
          }}
          disabled={!canAddMore}
          className="font-medium"
          roundedFull
        >
          <div className="flex items-center">
            <Upload className="mr-2 h-4 w-4" />
            Upload Photo
          </div>
        </EnhancedGlowButton>
      )}
    </motion.div>
  );
}
