"use client";

import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>C<PERSON>cle,
  Clock,
  Hourglass,
  CreditCard,
  Sparkles,
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { SubscriptionStatus } from "../page";
import { PricingPlan } from "@/lib/PricingPlans";
import { useSubscriptionProcessing } from "../context/SubscriptionProcessingContext";
import EnhancedSubscriptionDetailsCard from "./EnhancedSubscriptionDetailsCard";
import EnhancedInvoiceHistoryCard from "./EnhancedInvoiceHistoryCard";

import EnhancedActionButtons from "./EnhancedActionButtons";
import SubscriptionStatusIndicator from "../components/SubscriptionStatusIndicator";

interface EnhancedCurrentPlanSectionProps {
  _userId: string;
  currentPlanDetails?: PricingPlan;
  subscriptionStatus: SubscriptionStatus;
  trialEndDate: string | null;
  subscriptionEndDate: string | null;
  currentSubscriptionId: string | null;
  nextBillingDate: string | null;
  cancellationRequestedAt: string | null;
  planCycle: "monthly" | "yearly";
  _authenticatedSubscriptionStartDate?: string | null;
  razorpayStatus?: string | null;
  razorpayDates?: {
    current_start: string | null;
    current_end: string | null;
    charge_at: string | null;
    start_at: string | null;
    end_at: string | null;
    created_at: string | null;
    ended_at: string | null;
    change_scheduled_at: string | null;
  } | null;
  isEligibleForRefund: boolean;
  onCancelSubscription: () => void;
  onRequestRefund: () => void;
  isTrialActive: boolean;
}

export default function EnhancedCurrentPlanSection({
  currentPlanDetails,
  subscriptionStatus,
  trialEndDate,
  subscriptionEndDate,
  currentSubscriptionId,
  nextBillingDate,
  cancellationRequestedAt,
  planCycle,
  razorpayStatus,
  razorpayDates,
  isEligibleForRefund,
  onCancelSubscription,
  onRequestRefund,
  isTrialActive,
}: EnhancedCurrentPlanSectionProps) {
  // Get subscription processing status from context
  const { status: processingStatus, message: processingMessage } =
    useSubscriptionProcessing();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  // Determine if the current plan is premium for enhanced styling
  const isPremiumPlan =
    currentPlanDetails?.id === "pro" ||
    currentPlanDetails?.id === "enterprise";
  const isActiveOrAuthenticated =
    subscriptionStatus === "active" || subscriptionStatus === "authenticated";

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Section Header */}
      <motion.div variants={itemVariants} className="flex items-center gap-2 mb-4">
        <div className={cn(
          "p-2 rounded-lg text-primary self-start",
          isPremiumPlan && isActiveOrAuthenticated
            ? "bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]"
            : "bg-primary/10"
        )}>
          {isPremiumPlan && isActiveOrAuthenticated ? (
            <Sparkles className="w-5 h-5" />
          ) : (
            <CreditCard className="w-5 h-5" />
          )}
        </div>
        <div>
          <h2 className="text-xl font-semibold text-neutral-800 dark:text-neutral-100">
            Current Subscription
          </h2>
          <p className="text-sm text-neutral-500 dark:text-neutral-400">
            Manage your current subscription and billing details
          </p>
        </div>
      </motion.div>

      {/* Subscription Processing Status Indicator */}
      {processingStatus !== "idle" && (
        <SubscriptionStatusIndicator
          status={processingStatus}
          message={processingMessage}
        />
      )}

      {/* Subscription Status Alerts */}
      {subscriptionStatus === "inactive" && !isTrialActive && (
        <motion.div variants={itemVariants}>
          <Alert
            variant="destructive"
            className="bg-red-50 border-red-200 text-red-800 dark:bg-red-950/50 dark:border-red-800/50 dark:text-red-300"
          >
            <XCircle className="h-4 w-4" />
            <AlertTitle>No Active Subscription</AlertTitle>
            <AlertDescription>
              You don&apos;t have an active subscription. Choose a plan below to
              unlock premium features.
            </AlertDescription>
          </Alert>
        </motion.div>
      )}

      {subscriptionStatus === "pending" && (
        <motion.div variants={itemVariants}>
          <Alert
            variant="default"
            className="bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-950/50 dark:border-yellow-800/50 dark:text-yellow-300"
          >
            <Hourglass className="h-4 w-4" />
            <AlertTitle>Subscription Pending</AlertTitle>
            <AlertDescription>
              Your subscription is pending activation. This usually happens when
              a payment is being processed. Please check back in a few minutes.
            </AlertDescription>
          </Alert>
        </motion.div>
      )}

      {subscriptionStatus === "halted" && (
        <motion.div variants={itemVariants}>
          <Alert
            variant="default"
            className="bg-orange-50 border-orange-200 text-orange-800 dark:bg-orange-950/50 dark:border-orange-800/50 dark:text-orange-300"
          >
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Subscription Halted</AlertTitle>
            <AlertDescription>
              Your subscription has been halted due to a payment issue. Please
              update your payment method to resume your subscription.
            </AlertDescription>
          </Alert>
        </motion.div>
      )}

      {cancellationRequestedAt && (
        <motion.div variants={itemVariants}>
          <Alert
            variant="default"
            className="bg-orange-50 border-orange-200 text-orange-800 dark:bg-orange-950/50 dark:border-orange-800/50 dark:text-orange-300"
          >
            <Clock className="h-4 w-4" />
            <AlertTitle>Subscription Cancellation Scheduled</AlertTitle>
            <AlertDescription>
              Your subscription has been scheduled for cancellation at the end of
              the current billing cycle. You will continue to have access to all
              features until then.
            </AlertDescription>
          </Alert>
        </motion.div>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Subscription Details Card */}
        <motion.div variants={itemVariants} className="lg:col-span-2">
          <EnhancedSubscriptionDetailsCard
            status={subscriptionStatus}
            razorpayStatus={razorpayStatus}
            planName={currentPlanDetails?.name || "No Plan"}
            planCycle={planCycle}
            startDate={razorpayDates?.created_at || null}
            _endDate={subscriptionEndDate}
            _nextBillingDate={nextBillingDate}
            trialEndDate={trialEndDate}
            cancellationRequestedAt={cancellationRequestedAt}
            subscriptionId={currentSubscriptionId}
            razorpayDates={razorpayDates}
          />
        </motion.div>

        {/* Action Buttons */}
        <motion.div variants={itemVariants}>
          <EnhancedActionButtons
            subscriptionStatus={subscriptionStatus}
            isEligibleForRefund={isEligibleForRefund}
            onCancelSubscription={onCancelSubscription}
            onRequestRefund={onRequestRefund}
            isPremiumPlan={!!isPremiumPlan && isActiveOrAuthenticated}
          />
        </motion.div>

        {/* Invoice History Card */}
        {(subscriptionStatus === "active" ||
          subscriptionStatus === "authenticated" ||
          subscriptionStatus === "cancelled") && (
          <motion.div variants={itemVariants} className="lg:col-span-2">
            <EnhancedInvoiceHistoryCard subscriptionId={currentSubscriptionId} />
          </motion.div>
        )}


      </div>
    </motion.div>
  );
}
