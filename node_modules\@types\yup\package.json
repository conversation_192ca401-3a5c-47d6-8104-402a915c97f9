{"name": "@types/yup", "version": "0.29.14", "description": "TypeScript definitions for yup", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yup", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/dhardtke", "githubUsername": "d<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/vtserman", "githubUsername": "vtserman"}, {"name": "Moreton Bay Regional Council", "url": "https://github.com/MoretonBayRC", "githubUsername": "MoretonBayRC"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/sseppola", "githubUsername": "s<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/YashdalfTheGray", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>TheG<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vincentjames501", "githubUsername": "vincentjames501"}, {"name": "<PERSON>", "url": "https://github.com/robertbullen", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/sat0yu", "githubUsername": "sat0yu"}, {"name": "<PERSON>", "url": "https://github.com/deskoh", "githubUsername": "deskoh"}, {"name": "<PERSON>", "url": "https://github.com/mauricedb", "githubUsername": "mauri<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/elias-garcia", "githubUsername": "<PERSON><PERSON><PERSON>gar<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/iansan5653", "githubUsername": "iansan5653"}, {"name": "<PERSON>", "url": "https://github.com/fjc0k", "githubUsername": "fjc0k"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lukaselmer", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/yup"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "f78a55493129ba9eb39ac8a879b24a8d6f3c67b1898c4b2dd8e5bce95009c449", "typeScriptVersion": "3.9"}