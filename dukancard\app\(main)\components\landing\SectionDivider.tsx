"use client";

import { motion } from "framer-motion";

interface SectionDividerProps {
  variant?: "gold" | "blue" | "purple" | "neutral";
  className?: string;
}

export default function SectionDivider({
  variant: _variant = "gold",
  className = ""
}: SectionDividerProps) {
  // Define colors based on variant - removed unused function

  return (
    <div className={`w-full h-12 md:h-16 relative overflow-hidden ${className}`}>
      {/* Center line */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-7xl px-4 mx-auto">
        <motion.div
          className="w-full h-px bg-gradient-to-r from-transparent via-neutral-400 to-transparent dark:via-neutral-600"
          initial={{ width: "0%", left: "50%" }}
          whileInView={{ width: "100%", left: "0%" }}
          viewport={{ once: true }}
          transition={{ duration: 1.2 }}
        />
      </div>

      {/* Decorative dots */}
      <motion.div
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-neutral-400 dark:bg-neutral-600"
        initial={{ scale: 0, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 0.7 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.7 }}
      />

      {/* Outer ring */}
      <motion.div
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full border-2 border-neutral-300 dark:border-neutral-700"
        initial={{ scale: 0, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 0.5 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.8 }}
      />
    </div>
  );
}
