import { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { Loader2 } from "lucide-react";
import AdvertisePageClient from "./AdvertisePageClient";

export async function generateMetadata(): Promise<Metadata> {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const title = "Advertise with Us";
  const description =
    "Promote your business with targeted advertising on Dukancard. Reach potential customers in specific localities across India.";
  const ogImage = `${siteUrl}/opengraph-image.png`;
  const pageUrl = `${siteUrl}/advertise`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url: pageUrl,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: "Advertise with Dukancard",
        },
      ],
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [ogImage],
    },
    alternates: {
      canonical: pageUrl,
    },
    // Add WebPage Schema
    other: {
      "application-ld+json": JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: title,
        description: description,
        url: pageUrl,
        isPartOf: {
          "@type": "WebSite",
          name: "Dukancard",
          url: siteUrl,
        },
      }),
    },
  };
}

export default function AdvertisePage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <Loader2 className="animate-spin" />
        </div>
      }
    >
      <AdvertisePageClient />
    </Suspense>
  );
}
