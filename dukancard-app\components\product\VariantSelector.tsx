import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';

interface ProductVariant {
  id: string;
  variant_name: string;
  variant_values: Record<string, string>;
  base_price?: number | null;
  discounted_price?: number | null;
  is_available: boolean;
  images: string[];
  featured_image_index: number;
}

interface VariantOption {
  type: string;
  value: string;
  display_value: string;
  color_code?: string;
  available: boolean;
  variants: ProductVariant[];
}

interface VariantSelectorProps {
  variants: ProductVariant[];
  selectedVariant?: ProductVariant | null;
  onVariantSelect: (variant: ProductVariant | null) => void;
  disabled?: boolean;
}

// Predefined variant options with color codes
const getPredefinedOptionsForType = (type: string): Array<{ value: string; display_value: string; color_code?: string }> => {
  const options: Record<string, Array<{ value: string; display_value: string; color_code?: string }>> = {
    color: [
      { value: 'red', display_value: 'Red', color_code: '#EF4444' },
      { value: 'blue', display_value: 'Blue', color_code: '#3B82F6' },
      { value: 'green', display_value: 'Green', color_code: '#10B981' },
      { value: 'yellow', display_value: 'Yellow', color_code: '#F59E0B' },
      { value: 'purple', display_value: 'Purple', color_code: '#8B5CF6' },
      { value: 'pink', display_value: 'Pink', color_code: '#EC4899' },
      { value: 'orange', display_value: 'Orange', color_code: '#F97316' },
      { value: 'black', display_value: 'Black', color_code: '#000000' },
      { value: 'white', display_value: 'White', color_code: '#FFFFFF' },
      { value: 'gray', display_value: 'Gray', color_code: '#6B7280' },
    ],
    size: [
      { value: 'xs', display_value: 'XS' },
      { value: 's', display_value: 'S' },
      { value: 'm', display_value: 'M' },
      { value: 'l', display_value: 'L' },
      { value: 'xl', display_value: 'XL' },
      { value: 'xxl', display_value: 'XXL' },
    ],
    material: [
      { value: 'cotton', display_value: 'Cotton' },
      { value: 'polyester', display_value: 'Polyester' },
      { value: 'silk', display_value: 'Silk' },
      { value: 'wool', display_value: 'Wool' },
      { value: 'leather', display_value: 'Leather' },
    ],
  };

  return options[type.toLowerCase()] || [];
};

export default function VariantSelector({
  variants,
  selectedVariant,
  onVariantSelect,
  disabled = false,
}: VariantSelectorProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string>>({});
  const [variantTypes, setVariantTypes] = useState<Record<string, VariantOption[]>>({});

  // Colors
  const backgroundColor = isDark ? '#1F1F1F' : '#F8F9FA';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const subtitleColor = isDark ? '#9CA3AF' : '#6B7280';
  const borderColor = isDark ? '#333333' : '#E5E7EB';
  const selectedColor = '#D4AF37';

  // Process variants to extract variant types and options
  useEffect(() => {
    if (!variants || variants.length === 0) return;

    const types: Record<string, Set<string>> = {};
    const typeOptions: Record<string, VariantOption[]> = {};

    // Extract all variant types and their possible values from actual variants
    variants.forEach(variant => {
      Object.entries(variant.variant_values).forEach(([type, value]) => {
        if (!types[type]) {
          types[type] = new Set();
        }
        types[type].add(value);
      });
    });

    // Create variant options for each type
    Object.entries(types).forEach(([type, values]) => {
      const predefinedOptions = getPredefinedOptionsForType(type);

      typeOptions[type] = Array.from(values).map(value => {
        const variantsWithThisOption = variants.filter(variant =>
          variant.variant_values[type] === value
        );

        // Find predefined option for display value and color code
        const predefinedOption = predefinedOptions.find(opt => opt.value === value);

        return {
          type,
          value,
          display_value: predefinedOption?.display_value || value,
          color_code: predefinedOption?.color_code,
          available: variantsWithThisOption.some(v => v.is_available),
          variants: variantsWithThisOption,
        };
      });
    });

    setVariantTypes(typeOptions);
  }, [variants]);

  // Update selected variant when options change
  useEffect(() => {
    if (Object.keys(selectedOptions).length === 0) {
      onVariantSelect(null);
      return;
    }

    // Find matching variant
    const matchingVariant = variants.find(variant => {
      return Object.entries(selectedOptions).every(([type, value]) =>
        variant.variant_values[type] === value
      );
    });

    if (matchingVariant && matchingVariant !== selectedVariant) {
      onVariantSelect(matchingVariant);
    } else if (!matchingVariant && selectedVariant !== null) {
      onVariantSelect(null);
    }
  }, [selectedOptions, variants, selectedVariant, onVariantSelect]);

  // Handle option selection with deselection support
  const handleOptionSelect = (type: string, value: string) => {
    if (disabled) return;

    setSelectedOptions(prev => {
      const newSelection = { ...prev };

      // If option is already selected, deselect it
      if (newSelection[type] === value) {
        delete newSelection[type];
      } else {
        // Otherwise, select it
        newSelection[type] = value;
      }

      return newSelection;
    });
  };

  // Check if an option is available given current selections
  const isOptionAvailable = (type: string, value: string): boolean => {
    const testSelection = { ...selectedOptions, [type]: value };
    
    return variants.some(variant => {
      const matches = Object.entries(testSelection).every(([testType, testValue]) =>
        variant.variant_values[testType] === testValue
      );
      return matches && variant.is_available;
    });
  };

  if (!variants || variants.length === 0) {
    return null;
  }

  return (
    <View style={{ backgroundColor, borderRadius: 12, padding: 16, marginTop: 16 }}>
      <Text style={{ color: textColor, fontSize: 18, fontWeight: '600', marginBottom: 16 }}>
        Select Options
      </Text>

      {Object.entries(variantTypes).map(([type, options]) => {
        const isColorType = type.toLowerCase() === 'color';
        
        return (
          <View key={type} style={{ marginBottom: 20 }}>
            <Text style={{ 
              color: textColor, 
              fontSize: 16, 
              fontWeight: '500', 
              marginBottom: 12,
              textTransform: 'capitalize'
            }}>
              {type}
            </Text>

            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingHorizontal: 4 }}
            >
              {options.map((option) => {
                const isSelected = selectedOptions[type] === option.value;
                const isAvailable = isOptionAvailable(type, option.value);

                if (isColorType && option.color_code) {
                  // Color swatch for color variants
                  return (
                    <TouchableOpacity
                      key={`${type}-${option.value}`}
                      onPress={() => handleOptionSelect(type, option.value)}
                      disabled={!isAvailable && !isSelected || disabled}
                      style={{
                        width: 48,
                        height: 48,
                        borderRadius: 24,
                        backgroundColor: option.color_code,
                        marginRight: 12,
                        borderWidth: 2,
                        borderColor: isSelected ? selectedColor : borderColor,
                        opacity: (!isAvailable && !isSelected) ? 0.6 : 1,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.1,
                        shadowRadius: 4,
                        elevation: 2,
                      }}
                    >
                      {option.color_code === '#FFFFFF' && (
                        <View style={{
                          position: 'absolute',
                          inset: 2,
                          borderRadius: 22,
                          borderWidth: 1,
                          borderColor: '#E5E7EB',
                        }} />
                      )}
                    </TouchableOpacity>
                  );
                } else {
                  // Regular button for other variant types
                  return (
                    <TouchableOpacity
                      key={`${type}-${option.value}`}
                      onPress={() => handleOptionSelect(type, option.value)}
                      disabled={!isAvailable && !isSelected || disabled}
                      style={{
                        paddingHorizontal: 16,
                        paddingVertical: 10,
                        borderRadius: 8,
                        borderWidth: 2,
                        borderColor: isSelected ? selectedColor : borderColor,
                        backgroundColor: isSelected ? `${selectedColor}20` : 'transparent',
                        marginRight: 12,
                        opacity: (!isAvailable && !isSelected) ? 0.6 : 1,
                        minWidth: 60,
                        alignItems: 'center',
                      }}
                    >
                      <Text style={{
                        color: isSelected ? selectedColor : textColor,
                        fontSize: 14,
                        fontWeight: isSelected ? '600' : '500',
                      }}>
                        {option.display_value}
                      </Text>
                    </TouchableOpacity>
                  );
                }
              })}
            </ScrollView>
          </View>
        );
      })}

      {/* Selected variant info */}
      {selectedVariant && (
        <View style={{
          marginTop: 16,
          padding: 12,
          backgroundColor: isDark ? '#2A2A2A' : '#F0F9FF',
          borderRadius: 8,
          borderWidth: 1,
          borderColor: selectedColor,
        }}>
          <Text style={{ color: textColor, fontSize: 14, fontWeight: '500' }}>
            Selected: {selectedVariant.variant_name}
          </Text>
          {(selectedVariant.base_price !== null && selectedVariant.base_price !== undefined) && (
            <Text style={{ color: selectedColor, fontSize: 16, fontWeight: '600', marginTop: 4 }}>
              ₹{selectedVariant.base_price.toLocaleString('en-IN')}
              {selectedVariant.discounted_price && selectedVariant.discounted_price < selectedVariant.base_price && (
                <Text style={{ color: subtitleColor, fontSize: 14, textDecorationLine: 'line-through' }}>
                  {' '}₹{selectedVariant.base_price.toLocaleString('en-IN')}
                </Text>
              )}
            </Text>
          )}
        </View>
      )}
    </View>
  );
}
