"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Lightbulb, Users, Store, Sparkles } from "lucide-react";

export default function StorySection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };
  
  const leftVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6 }
    },
  };
  
  const rightVariants = {
    hidden: { opacity: 0, x: 50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6 }
    },
  };

  // Story milestones
  const storyPoints = [
    {
      icon: <Lightbulb className="w-8 h-8 text-[var(--brand-gold)]" />,
      title: "The Idea",
      content: "In 2024, our founder noticed a glaring gap in the market. While large businesses had sophisticated digital tools, small shop owners and local businesses were being left behind in the digital revolution."
    },
    {
      icon: <Users className="w-8 h-8 text-[var(--brand-gold)]" />,
      title: "The Research",
      content: "After interviewing hundreds of small business owners across India, we learned that they wanted a simple, affordable way to establish their digital presence without needing technical skills."
    },
    {
      icon: <Store className="w-8 h-8 text-[var(--brand-gold)]" />,
      title: "The Solution",
      content: "That's when Dukancard was born - a platform that would democratize digital presence for businesses of all sizes, with the premium look and feel traditionally reserved for large corporations."
    },
    {
      icon: <Sparkles className="w-8 h-8 text-[var(--brand-gold)]" />,
      title: "The Future",
      content: "Today, we're building a comprehensive ecosystem of digital tools that will help small businesses not just survive, but thrive in the digital economy."
    }
  ];

  return (
    <section 
      ref={sectionRef}
      className="py-20 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto"
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center"
      >
        {/* Left column - Text content */}
        <motion.div
          variants={leftVariants}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
            The <span className="text-[var(--brand-gold)]">Dukan</span>card Story
          </h2>
          
          <div className="space-y-8">
            {storyPoints.map((point, index) => (
              <motion.div 
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
                className="flex gap-4"
              >
                <div className="mt-1 relative">
                  <div className="p-2 rounded-full bg-background border border-border dark:bg-black/50 dark:border-[var(--brand-gold)]/20">
                    {point.icon}
                  </div>
                  
                  {/* Connecting line */}
                  {index < storyPoints.length - 1 && (
                    <div className="absolute top-10 bottom-0 left-1/2 w-0.5 h-full -translate-x-1/2 bg-gradient-to-b from-[var(--brand-gold)]/50 to-transparent"></div>
                  )}
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold text-foreground mb-2">
                    {point.title}
                  </h3>
                  <p className="text-muted-foreground">
                    {point.content}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
        
        {/* Right column - Visual representation */}
        <motion.div
          variants={rightVariants}
          className="relative"
        >
          <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-[#1a1a1a] dark:to-[#0d0d0d] rounded-2xl p-8 dark:border-[var(--brand-gold)]/30 shadow-lg dark:shadow-2xl dark:shadow-[var(--brand-gold)]/10 relative overflow-hidden">
            {/* Blur elements */}
            <div className="absolute -top-6 -left-6 w-24 h-24 bg-primary/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-xl z-[-1]"></div>
            <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-primary/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-xl z-[-1]"></div>
            
            {/* Card content */}
            <div className="relative z-10">
              <div className="flex justify-center mb-6">
                <motion.div
                  className="w-16 h-16 rounded-full bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 flex items-center justify-center"
                  animate={{
                    scale: [1, 1.05, 1],
                    boxShadow: [
                      "0 0 0 0 rgba(var(--brand-gold-rgb), 0.2)",
                      "0 0 0 10px rgba(var(--brand-gold-rgb), 0)",
                      "0 0 0 0 rgba(var(--brand-gold-rgb), 0)",
                    ],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse",
                  }}
                >
                  <span className="text-2xl font-bold text-[var(--brand-gold)]">DC</span>
                </motion.div>
              </div>
              
              <h3 className="text-2xl font-bold text-center mb-4">Our Journey</h3>
              
              <p className="text-muted-foreground mb-6 text-center">
                From a simple idea to a growing platform, our journey has been driven by a singular focus: making digital tools accessible to all businesses.
              </p>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 rounded-lg bg-background/50 dark:bg-black/30 border border-border dark:border-[var(--brand-gold)]/10">
                  <motion.div
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
                  >
                    <p className="text-3xl font-bold text-[var(--brand-gold)]">2024</p>
                    <p className="text-sm text-muted-foreground">Founded</p>
                  </motion.div>
                </div>
                
                <div className="text-center p-4 rounded-lg bg-background/50 dark:bg-black/30 border border-border dark:border-[var(--brand-gold)]/10">
                  <motion.div
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 2, repeat: Infinity, repeatType: "reverse", delay: 0.5 }}
                  >
                    <p className="text-3xl font-bold text-blue-500">10K+</p>
                    <p className="text-sm text-muted-foreground">Target Users</p>
                  </motion.div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </section>
  );
}
