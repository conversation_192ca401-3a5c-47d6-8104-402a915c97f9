# Notification Analytics Table

## Overview
The `notification_analytics` table tracks push notification performance metrics including delivery, open rates, and user actions. This data is used to optimize notification timing and content for better engagement.

## Table Structure

```sql
CREATE TABLE notification_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type TEXT NOT NULL,
    priority TEXT NOT NULL DEFAULT 'medium',
    sent_at TIMESTAMP WITH TIME ZONE NOT NULL,
    date DATE NOT NULL,
    opened BOOLEAN DEFAULT FALSE,
    opened_at TIMESTAMP WITH TIME ZONE,
    action_taken BOOLEAN DEFAULT FALSE,
    action_type TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Indexes

```sql
-- Efficient querying by user and date
CREATE INDEX idx_notification_analytics_user_date 
ON notification_analytics (user_id, date);

-- Efficient querying by notification type and time
CREATE INDEX idx_notification_analytics_type 
ON notification_analytics (notification_type, sent_at);

-- Engagement analysis index
CREATE INDEX idx_notification_analytics_engagement 
ON notification_analytics (user_id, opened, action_taken);

-- Performance analysis index
CREATE INDEX idx_notification_analytics_performance 
ON notification_analytics (date, notification_type, priority);
```

## Row Level Security (RLS)

```sql
-- Enable RLS
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;

-- Users can view their own notification analytics
CREATE POLICY "Users can view their own notification analytics" ON notification_analytics
    FOR SELECT USING (auth.uid() = user_id);

-- Service role can manage all notification analytics
CREATE POLICY "Service role can manage all notification analytics" ON notification_analytics
    FOR ALL USING (auth.role() = 'service_role');
```

## Fields Description

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `user_id` | UUID | Foreign key to auth.users.id (notification recipient) |
| `notification_type` | TEXT | Type of notification ('like', 'subscribe', 'rating', etc.) |
| `priority` | TEXT | Notification priority ('low', 'medium', 'high', 'urgent') |
| `sent_at` | TIMESTAMP | When the notification was sent |
| `date` | DATE | Date component for efficient daily aggregations |
| `opened` | BOOLEAN | Whether the user opened/viewed the notification |
| `opened_at` | TIMESTAMP | When the notification was opened |
| `action_taken` | BOOLEAN | Whether the user took an action after opening |
| `action_type` | TEXT | Type of action taken ('navigate', 'like_back', 'subscribe_back', etc.) |
| `created_at` | TIMESTAMP | When the record was created |

## Helper Functions

### Update Notification as Opened
```sql
CREATE OR REPLACE FUNCTION update_notification_opened(
    p_user_id UUID,
    p_notification_type TEXT,
    p_sent_at TIMESTAMP WITH TIME ZONE
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE notification_analytics 
    SET 
        opened = TRUE,
        opened_at = NOW()
    WHERE 
        user_id = p_user_id 
        AND notification_type = p_notification_type 
        AND sent_at = p_sent_at
        AND opened = FALSE;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Track Notification Action
```sql
CREATE OR REPLACE FUNCTION track_notification_action(
    p_user_id UUID,
    p_notification_type TEXT,
    p_sent_at TIMESTAMP WITH TIME ZONE,
    p_action_type TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE notification_analytics 
    SET 
        action_taken = TRUE,
        action_type = p_action_type
    WHERE 
        user_id = p_user_id 
        AND notification_type = p_notification_type 
        AND sent_at = p_sent_at;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Usage Examples

### Record a Sent Notification
```sql
INSERT INTO notification_analytics (
    user_id,
    notification_type,
    priority,
    sent_at,
    date
) VALUES (
    'user-uuid',
    'like',
    'low',
    NOW(),
    CURRENT_DATE
);
```

### Mark Notification as Opened
```sql
SELECT update_notification_opened(
    'user-uuid',
    'like',
    '2024-01-15 10:30:00+00'::timestamp with time zone
);
```

### Track User Action
```sql
SELECT track_notification_action(
    'user-uuid',
    'like',
    '2024-01-15 10:30:00+00'::timestamp with time zone,
    'navigate_to_business'
);
```

## Analytics Queries

### Daily Open Rates by Type
```sql
SELECT 
    date,
    notification_type,
    COUNT(*) as total_sent,
    COUNT(*) FILTER (WHERE opened = TRUE) as total_opened,
    ROUND(
        COUNT(*) FILTER (WHERE opened = TRUE) * 100.0 / COUNT(*), 
        2
    ) as open_rate_percent
FROM notification_analytics 
WHERE date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY date, notification_type
ORDER BY date DESC, notification_type;
```

### User Engagement by Priority
```sql
SELECT 
    priority,
    COUNT(*) as total_sent,
    COUNT(*) FILTER (WHERE opened = TRUE) as total_opened,
    COUNT(*) FILTER (WHERE action_taken = TRUE) as total_actions,
    ROUND(
        COUNT(*) FILTER (WHERE opened = TRUE) * 100.0 / COUNT(*), 
        2
    ) as open_rate,
    ROUND(
        COUNT(*) FILTER (WHERE action_taken = TRUE) * 100.0 / 
        COUNT(*) FILTER (WHERE opened = TRUE), 
        2
    ) as action_rate
FROM notification_analytics 
WHERE date >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY priority
ORDER BY priority;
```

### Best Performing Notification Times
```sql
SELECT 
    EXTRACT(hour FROM sent_at) as hour_of_day,
    COUNT(*) as total_sent,
    COUNT(*) FILTER (WHERE opened = TRUE) as total_opened,
    ROUND(
        COUNT(*) FILTER (WHERE opened = TRUE) * 100.0 / COUNT(*), 
        2
    ) as open_rate_percent
FROM notification_analytics 
WHERE date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY EXTRACT(hour FROM sent_at)
ORDER BY open_rate_percent DESC;
```

## Data Retention

### Clean Up Old Analytics Data
```sql
-- Keep only last 90 days of analytics data
DELETE FROM notification_analytics 
WHERE date < CURRENT_DATE - INTERVAL '90 days';
```

## Integration with Notification System

This table is used by:
- **NotificationFrequencyManager** - to record sent notifications
- **Push notification handlers** - to track opens and actions
- **Analytics dashboard** - to display performance metrics
- **Optimization algorithms** - to improve notification timing

## Best Practices

1. **Regular Cleanup**: Keep only necessary historical data (90 days recommended)
2. **Batch Inserts**: Use batch inserts for better performance
3. **Index Optimization**: Ensure proper indexes for your query patterns
4. **Privacy**: Respect user privacy when analyzing notification data
5. **Performance Monitoring**: Monitor query performance on large datasets

## Related Tables

- `push_tokens` - User push notification tokens
- `push_notification_queue` - Notification queue system
- `business_activities` - Source events that trigger notifications
