"use client";

import PlanPageContainer from "./PlanPageContainer";
import { PricingPlan } from "@/lib/PricingPlans";
import { SubscriptionStatus } from "../page";

interface EnhancedPlanPageWithManagerProps {
  userId: string;
  currentPlanDetails?: PricingPlan;
  subscriptionStatus: SubscriptionStatus;
  trialEndDate: string | null;
  subscriptionEndDate: string | null;
  monthlyPlans: PricingPlan[];
  yearlyPlans: PricingPlan[];
  currentSubscriptionId: string | null;
  nextBillingDate: string | null;
  cancellationRequestedAt: string | null;
  cancelledAt: string | null;
  planCycle: "monthly" | "yearly";
  authenticatedSubscriptionStartDate?: string | null;
  // New props for subscription dates from Supabase
  subscriptionStartDate?: string | null;
  subscriptionExpiryTime?: string | null;
  subscriptionChargeTime?: string | null;
  isEligibleForRefund?: boolean;
  // Payment method
  lastPaymentMethod?: string | null;
  // Razorpay subscription data to determine switch vs fresh subscription
  razorpaySubscriptionId?: string | null;
}

export default function EnhancedPlanPageWithManager(props: EnhancedPlanPageWithManagerProps) {
  return <PlanPageContainer {...props} />;
}