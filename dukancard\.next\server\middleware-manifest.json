{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_@upstash_redis_b3b75fae._.js", "server/edge/chunks/node_modules_a5b8fa46._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_3918d6b0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "AZMuPbqlbKhnEIjS3+deafwyw7vd5fDFEX7nHsJY2W4=", "__NEXT_PREVIEW_MODE_ID": "f89715d50d8bb65809874c3843463057", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "591bf7833a120ead6a0891507ab6d2244ca8de15c97d3269c351d94b0b5c885c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ae3c4c7edd1e736524eddab556ace00e701cf317a81e8aa81364f18a6d883e08"}}}, "sortedMiddleware": ["/"], "functions": {}}