"use client";

import { motion } from "framer-motion";
import { Sparkles } from "lucide-react";
import { PricingPlan } from "@/lib/PricingPlans";
import PricingCard from "@/app/components/PricingCard";
import { PricingCardProvider } from "@/app/components/PricingCardContext";
import BillingToggle from "./BillingToggle";

interface EnhancedPlanSelectionSectionProps {
  subscriptionStatus: string;
  billingCycle: "monthly" | "yearly";
  setBillingCycle: (_value: "monthly" | "yearly") => void;
  plans: PricingPlan[];
  currentPlanId?: string | null;
  currentPlanCycle?: "monthly" | "yearly";
  loadingStates: Record<string, boolean>;
  onPlanAction: (_plan: PricingPlan) => void;
}

export default function EnhancedPlanSelectionSection({
  subscriptionStatus,
  billingCycle,
  setBillingCycle,
  plans,
  currentPlanId,
  currentPlanCycle,
  loadingStates,
  onPlanAction,
}: EnhancedPlanSelectionSectionProps) {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  // Helper function to check if a plan is the current active or authenticated plan
  const isCurrentActivePlan = (plan: PricingPlan) => {
    return (
      plan.id === currentPlanId &&
      billingCycle === currentPlanCycle &&
      (subscriptionStatus === "active" || subscriptionStatus === "authenticated")
    );
  };

  // Helper function to check if a plan should be disabled
  const shouldDisablePlan = (plan: PricingPlan) => {
    // Always disable the free plan as we will handle free plan subscription by code
    if (plan.id === "free") {
      return true;
    }

    // Otherwise, use the default availability from the plan
    return !plan.available;
  };

  return (
    <PricingCardProvider>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black backdrop-blur-sm shadow-lg transition-all duration-300 hover:shadow-xl relative overflow-hidden"
      >
      {/* Background gradient effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent dark:from-blue-500/10 dark:to-transparent pointer-events-none"></div>

      {/* Content with relative positioning */}
      <div className="relative z-10 p-4 sm:p-6 md:p-8">
        {/* Section Header */}
        <motion.div variants={itemVariants} className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
            <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
              <Sparkles className="w-4 sm:w-5 h-4 sm:h-5" />
            </div>
            <div className="flex-1">
              <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                Available Plans
              </h3>
              <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                {subscriptionStatus === "inactive"
                  ? "Select a subscription plan that best fits your business needs and unlock premium features"
                  : "View and compare available subscription options to find the perfect fit for your business"}
              </p>
            </div>
          </div>
        </motion.div>

        {/* Billing Toggle */}
        <motion.div variants={itemVariants} className="flex justify-center mb-8">
          <BillingToggle
            billingCycle={billingCycle}
            setBillingCycle={setBillingCycle}
          />
        </motion.div>

        {/* Pricing Cards Grid */}
        <motion.div variants={itemVariants}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 md:gap-4 items-stretch">
            {plans.map((plan, index) => {
              // Determine button state and override text
              let buttonTextOverride: string | undefined = undefined;
              const planIsCurrentActive = isCurrentActivePlan(plan);
              const isDisabled = shouldDisablePlan(plan);

              if (isDisabled) {
                if (plan.id === "free") {
                  // For free plan, show appropriate message
                  if (currentPlanId === "free") {
                    buttonTextOverride = "Current Plan";
                  } else {
                    buttonTextOverride = "Auto Managed";
                  }
                } else {
                  // For other unavailable plans
                  // Let PricingCard handle "Coming Soon"
                }
              } else if (planIsCurrentActive) {
                // Let PricingCard handle "Current Plan"
              } else {
                // For all other available plans, show "View Plan" or similar
                buttonTextOverride = "View Plan"; // Or keep default from PricingPlan definition
              }

              // Create a modified plan object with updated availability
              const modifiedPlan = {
                ...plan,
                available: !isDisabled && plan.available
              };

              return (
                <div key={`${plan.id}-${billingCycle}`} className="w-full">
                  <PricingCard
                    plan={modifiedPlan}
                    index={index}
                    isLoading={loadingStates[plan.id]}
                    isCurrentPlan={planIsCurrentActive || (plan.id === "free" && currentPlanId === "free")}
                    buttonTextOverride={buttonTextOverride}
                    onButtonClick={() => onPlanAction(plan)}
                  />
                </div>
              );
            })}
          </div>
        </motion.div>
      </div>
    </motion.div>
    </PricingCardProvider>
  );
}
