import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Star, X } from 'lucide-react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { submitBusinessReview } from '@/lib/services/businessInteractions';
import { Toast } from '@/lib/utils/toast';

interface ReviewModalProps {
  visible: boolean;
  onClose: () => void;
  businessId: string;
  businessName: string;
  existingRating?: number | null;
  existingReview?: string | null;
  onReviewSubmitted: () => void;
}

export default function ReviewModal({
  visible,
  onClose,
  businessId,
  businessName,
  existingRating,
  existingReview,
  onReviewSubmitted,
}: ReviewModalProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [rating, setRating] = useState(existingRating || 0);
  const [reviewText, setReviewText] = useState(existingReview || '');
  const [submitting, setSubmitting] = useState(false);

  const backgroundColor = isDark ? '#1A202C' : '#FFFFFF';
  const textColor = isDark ? '#FFFFFF' : '#1A202C';
  const subtitleColor = isDark ? '#A0AEC0' : '#718096';
  const borderColor = isDark ? '#2D3748' : '#E2E8F0';
  const inputBackgroundColor = isDark ? '#2D3748' : '#F7FAFC';

  const handleSubmit = async () => {
    if (rating === 0) {
      Alert.alert('Rating Required', 'Please select a rating before submitting.');
      return;
    }

    setSubmitting(true);
    try {
      const result = await submitBusinessReview(businessId, rating, reviewText.trim() || undefined);
      
      if (result.success) {
        Toast.show(result.message, 'success');
        onReviewSubmitted();
        onClose();
        // Reset form
        setRating(existingRating || 0);
        setReviewText(existingReview || '');
      } else {
        Toast.show(result.message, 'error');
      }
    } catch (error) {
      Toast.show('Failed to submit review', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!submitting) {
      onClose();
      // Reset form to original values
      setRating(existingRating || 0);
      setReviewText(existingReview || '');
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={[styles.container, { backgroundColor }]}>
          {/* Header */}
          <View style={[styles.header, { borderBottomColor: borderColor }]}>
            <Text style={[styles.title, { color: textColor }]}>
              {existingRating ? 'Update Review' : 'Write a Review'}
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleClose}
              disabled={submitting}
            >
              <X size={24} color={textColor} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <View style={styles.content}>
            <Text style={[styles.businessName, { color: textColor }]}>
              {businessName}
            </Text>

            {/* Rating Section */}
            <View style={styles.ratingSection}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>
                Your Rating *
              </Text>
              <View style={styles.starsContainer}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <TouchableOpacity
                    key={star}
                    onPress={() => setRating(star)}
                    disabled={submitting}
                    style={styles.starButton}
                  >
                    <Star
                      size={32}
                      color={star <= rating ? '#F39C12' : '#E2E8F0'}
                      fill={star <= rating ? '#F39C12' : 'transparent'}
                    />
                  </TouchableOpacity>
                ))}
              </View>
              <Text style={[styles.ratingText, { color: subtitleColor }]}>
                {rating === 0 ? 'Tap to rate' : 
                 rating === 1 ? 'Poor' :
                 rating === 2 ? 'Fair' :
                 rating === 3 ? 'Good' :
                 rating === 4 ? 'Very Good' : 'Excellent'}
              </Text>
            </View>

            {/* Review Text Section */}
            <View style={styles.reviewSection}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>
                Your Review (Optional)
              </Text>
              <TextInput
                style={[
                  styles.reviewInput,
                  {
                    backgroundColor: inputBackgroundColor,
                    color: textColor,
                    borderColor: borderColor,
                  }
                ]}
                placeholder="Share your experience with this business..."
                placeholderTextColor={subtitleColor}
                value={reviewText}
                onChangeText={setReviewText}
                multiline
                numberOfLines={4}
                maxLength={500}
                editable={!submitting}
              />
              <Text style={[styles.characterCount, { color: subtitleColor }]}>
                {reviewText.length}/500
              </Text>
            </View>
          </View>

          {/* Footer */}
          <View style={[styles.footer, { borderTopColor: borderColor }]}>
            <TouchableOpacity
              style={[styles.cancelButton, { borderColor: borderColor }]}
              onPress={handleClose}
              disabled={submitting}
            >
              <Text style={[styles.cancelButtonText, { color: textColor }]}>
                Cancel
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.submitButton,
                { opacity: rating === 0 || submitting ? 0.5 : 1 }
              ]}
              onPress={handleSubmit}
              disabled={rating === 0 || submitting}
            >
              {submitting ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.submitButtonText}>
                  {existingRating ? 'Update Review' : 'Submit Review'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  container: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 20,
  },
  businessName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 24,
    textAlign: 'center',
  },
  ratingSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  starsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 8,
  },
  starButton: {
    padding: 4,
    marginHorizontal: 2,
  },
  ratingText: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
  },
  reviewSection: {
    marginBottom: 24,
  },
  reviewInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    textAlignVertical: 'top',
    minHeight: 100,
  },
  characterCount: {
    fontSize: 12,
    textAlign: 'right',
    marginTop: 8,
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  submitButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    backgroundColor: '#F39C12',
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
});
