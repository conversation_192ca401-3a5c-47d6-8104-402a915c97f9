"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AlertCircle } from "lucide-react";

interface PaymentMethodLimitationsDialogProps {
  open: boolean;
  onOpenChange: (_open: boolean) => void;
  onContinue: () => void;
  paymentMethod: string;
}

export function PaymentMethodLimitationsDialog({
  open,
  onOpenChange,
  onContinue,
  paymentMethod,
}: PaymentMethodLimitationsDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-yellow-500" />
            Payment Method Limitation
          </DialogTitle>
          <DialogDescription>
            Important: Due to Razorpay and RBI regulations, subscriptions with{" "}
            {paymentMethod} payment methods require a special process for
            changes.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4 text-sm">
          <p>
            When you click &quot;Continue&quot;, the following process will
            happen:
          </p>
          <ol className="list-decimal pl-5 space-y-2">
            <li>
              We&apos;ll create a new subscription with your selected plan
            </li>
            <li>You&apos;ll need to complete the payment authorization</li>
            <li>
              <strong>Only after the new subscription is active</strong>,
              we&apos;ll automatically cancel your previous subscription
            </li>
          </ol>
          <p className="text-muted-foreground">
            This ensures you don&apos;t lose access to your current subscription
            if the new payment fails or is abandoned. Your current subscription
            will remain active until the new one is successfully set up.
          </p>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={onContinue}>Continue</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
