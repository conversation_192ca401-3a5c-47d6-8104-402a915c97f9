export default function CardDivider() {
  return (
    <div className="h-6 w-full mx-auto max-w-xs mb-3 relative flex items-center justify-center">
      <div
        className="h-px w-full absolute"
        style={{
          background: `linear-gradient(to right, transparent, var(--theme-color-30), transparent)`,
        }}
      ></div>
      <div className="relative z-10 flex items-center justify-center space-x-2">
        <div className="w-1.5 h-1.5 rounded-full bg-[var(--theme-color-50)]"></div>
        <div className="w-1.5 h-1.5 rounded-full bg-[var(--theme-color-30)]"></div>
        <div className="w-1.5 h-1.5 rounded-full bg-[var(--theme-color-50)]"></div>
      </div>
    </div>
  );
}