"use client";

import { useState, useEffect } from "react";
import {
  Loader2,
  CheckCircle,
  AlertTriangle,
  Clock,
  CreditCard,
  Wallet,
  RefreshCcw,
  XCircle,
  PauseCircle,
  TimerOff,
  Hourglass,
  ShieldCheck,
  ServerCrash,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";

// Import the ProcessingStatus type from the context
import type { ProcessingStatus } from "../context/SubscriptionProcessingContext";

interface SubscriptionStatusIndicatorProps {
  status: ProcessingStatus;
  message?: string;
  className?: string;
}

export default function SubscriptionStatusIndicator({
  status,
  message,
  className,
}: SubscriptionStatusIndicatorProps) {
  const [dots, setDots] = useState("");

  // Animate the dots for all loading/waiting states
  useEffect(() => {
    // Define all states that should show the loading dots animation
    const loadingStates = [
      "processing",
      "waiting_for_webhook",
      "subscription_created",
      "subscription_authenticated",
      "subscription_pending",
      "refund_processing",
    ];

    if (!loadingStates.includes(status)) return;

    const interval = setInterval(() => {
      setDots((prev) => {
        if (prev.length >= 3) return "";
        return prev + ".";
      });
    }, 500);

    return () => clearInterval(interval);
  }, [status]);

  return (
    <AnimatePresence mode="wait">
      {status !== "idle" && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
          className={cn(
            "p-4 rounded-lg mb-4 w-full",
            // Basic processing states
            status === "processing" &&
              "bg-blue-50 dark:bg-blue-900/50 border border-blue-200 dark:border-blue-800",
            status === "success" &&
              "bg-green-50 dark:bg-green-900/50 border border-green-200 dark:border-green-800",
            status === "error" &&
              "bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800",
            status === "razorpay_server_error" &&
              "bg-amber-50 dark:bg-amber-900/50 border border-amber-200 dark:border-amber-800",

            // Webhook waiting state
            status === "waiting_for_webhook" &&
              "bg-purple-50 dark:bg-purple-900/50 border border-purple-200 dark:border-purple-800",

            // Payment states
            (status === "payment_authorized" ||
              status === "payment_captured") &&
              "bg-indigo-50 dark:bg-indigo-900/50 border border-indigo-200 dark:border-indigo-800",

            // Subscription states
            (status === "subscription_created" ||
              status === "subscription_authenticated") &&
              "bg-teal-50 dark:bg-teal-900/50 border border-teal-200 dark:border-teal-800",
            (status === "subscription_activated" ||
              status === "subscription_active") &&
              "bg-green-50 dark:bg-green-900/50 border border-green-200 dark:border-green-800",
            status === "subscription_pending" &&
              "bg-yellow-50 dark:bg-yellow-900/50 border border-yellow-200 dark:border-yellow-800",
            (status === "subscription_halted" ||
              status === "subscription_cancelled" ||
              status === "subscription_completed" ||
              status === "subscription_expired") &&
              "bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800",

            // Refund states
            status === "refund_processing" &&
              "bg-amber-50 dark:bg-amber-900/50 border border-amber-200 dark:border-amber-800",
            status === "refund_processed" &&
              "bg-green-50 dark:bg-green-900/50 border border-green-200 dark:border-green-800",

            className
          )}
        >
          <div className="flex items-start gap-3">
            {/* Basic processing states */}
            {status === "processing" && (
              <Loader2 className="w-5 h-5 text-blue-600 dark:text-blue-400 animate-spin mt-0.5" />
            )}
            {status === "success" && (
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
            )}
            {status === "error" && (
              <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
            )}
            {status === "razorpay_server_error" && (
              <ServerCrash className="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5" />
            )}

            {/* Webhook waiting state */}
            {status === "waiting_for_webhook" && (
              <Clock className="w-5 h-5 text-purple-600 dark:text-purple-400 animate-pulse mt-0.5" />
            )}

            {/* Payment states */}
            {(status === "payment_authorized" ||
              status === "payment_captured") && (
              <CreditCard className="w-5 h-5 text-indigo-600 dark:text-indigo-400 mt-0.5" />
            )}

            {/* Subscription states */}
            {(status === "subscription_created" ||
              status === "subscription_authenticated") && (
              <Wallet className="w-5 h-5 text-teal-600 dark:text-teal-400 mt-0.5" />
            )}
            {(status === "subscription_activated" ||
              status === "subscription_active") && (
              <ShieldCheck className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
            )}
            {status === "subscription_pending" && (
              <Hourglass className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
            )}
            {status === "subscription_halted" && (
              <PauseCircle className="w-5 h-5 text-gray-600 dark:text-gray-400 mt-0.5" />
            )}
            {status === "subscription_cancelled" && (
              <XCircle className="w-5 h-5 text-gray-600 dark:text-gray-400 mt-0.5" />
            )}
            {status === "subscription_completed" && (
              <CheckCircle className="w-5 h-5 text-gray-600 dark:text-gray-400 mt-0.5" />
            )}
            {status === "subscription_expired" && (
              <TimerOff className="w-5 h-5 text-gray-600 dark:text-gray-400 mt-0.5" />
            )}

            {/* Refund states */}
            {status === "refund_processing" && (
              <RefreshCcw className="w-5 h-5 text-amber-600 dark:text-amber-400 animate-spin mt-0.5" />
            )}
            {status === "refund_processed" && (
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
            )}
            <div className="flex-1">
              <p className="font-medium text-sm">
                {/* Basic processing states */}
                {status === "processing" && "Processing Subscription"}
                {status === "success" && "Subscription Updated"}
                {status === "error" && "Subscription Error"}
                {status === "razorpay_server_error" && "Payment Gateway Error"}

                {/* Webhook waiting state */}
                {status === "waiting_for_webhook" && "Waiting for Confirmation"}

                {/* Payment states */}
                {status === "payment_authorized" && "Payment Authorized"}
                {status === "payment_captured" && "Payment Captured"}

                {/* Subscription states */}
                {status === "subscription_created" && "Subscription Created"}
                {status === "subscription_authenticated" &&
                  "Subscription Authenticated"}
                {status === "subscription_activated" &&
                  "Subscription Activated"}
                {status === "subscription_active" && "Subscription Active"}
                {status === "subscription_pending" && "Subscription Pending"}
                {status === "subscription_halted" && "Subscription Halted"}
                {status === "subscription_cancelled" &&
                  "Subscription Cancelled"}
                {status === "subscription_completed" &&
                  "Subscription Completed"}
                {status === "subscription_expired" && "Subscription Expired"}

                {/* Refund states */}
                {status === "refund_processing" && "Processing Refund"}
                {status === "refund_processed" && "Refund Processed"}
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                {/* Basic processing states */}
                {status === "processing" && (
                  <>
                    {message ||
                      "Please wait while we process your subscription"}
                    <span className="inline-block w-6">{dots}</span>
                  </>
                )}
                {status === "success" &&
                  (message ||
                    "Your subscription has been successfully updated.")}
                {status === "error" && (
                  <>
                    {message?.includes("Razorpay servers are currently experiencing issues")
                      ? message
                      : message || "There was an error processing your subscription. Please try again."}
                    {message?.includes("Razorpay servers are currently experiencing issues") && (
                      <span className="block mt-1 text-xs">
                        This is a temporary issue with the payment gateway. Please try again in a few minutes.
                      </span>
                    )}
                  </>
                )}
                {status === "razorpay_server_error" && (
                  <>
                    {message || "Razorpay servers are currently experiencing issues. Please try again in a few minutes."}
                    <span className="block mt-1 text-xs">
                      This is a temporary issue with the payment gateway and not a problem with your account.
                    </span>
                  </>
                )}

                {/* Webhook waiting state */}
                {status === "waiting_for_webhook" && (
                  <>
                    {message ||
                      "Please wait while we confirm your payment with Razorpay"}
                    <span className="inline-block w-6">{dots}</span>
                  </>
                )}

                {/* Payment states */}
                {status === "payment_authorized" &&
                  (message ||
                    "Your payment has been authorized. Setting up your subscription...")}
                {status === "payment_captured" &&
                  (message ||
                    "Your payment has been captured. Activating your subscription...")}

                {/* Subscription states */}
                {status === "subscription_created" && (
                  <>
                    {message ||
                      "Your subscription has been created. Waiting for activation..."}
                    <span className="inline-block w-6">{dots}</span>
                  </>
                )}
                {status === "subscription_authenticated" && (
                  <>
                    {message ||
                      "Your subscription has been authenticated. Waiting for activation..."}
                    <span className="inline-block w-6">{dots}</span>
                  </>
                )}
                {status === "subscription_activated" &&
                  (message ||
                    "Your subscription has been activated successfully!")}
                {status === "subscription_active" &&
                  (message ||
                    "Your subscription is active and billing is up to date.")}
                {status === "subscription_pending" && (
                  <>
                    {message ||
                      "Your subscription is pending. Waiting for payment confirmation..."}
                    <span className="inline-block w-6">{dots}</span>
                  </>
                )}
                {status === "subscription_halted" &&
                  (message ||
                    "Your subscription has been halted due to payment issues. Please update your payment method.")}
                {status === "subscription_cancelled" &&
                  (message ||
                    "Your subscription has been cancelled. You can resubscribe anytime.")}
                {status === "subscription_completed" &&
                  (message || "Your subscription has completed its full term.")}
                {status === "subscription_expired" &&
                  (message ||
                    "Your subscription has expired. Please renew to continue accessing premium features.")}

                {/* Refund states */}
                {status === "refund_processing" && (
                  <>
                    {message || "Processing your refund request..."}
                    <span className="inline-block w-6">{dots}</span>
                  </>
                )}
                {status === "refund_processed" &&
                  (message || "Your refund has been processed successfully!")}
              </p>

              {/* Additional note for waiting states */}
              {(status === "waiting_for_webhook" ||
                status === "subscription_created" ||
                status === "subscription_authenticated" ||
                status === "subscription_pending") && (
                <p className="text-xs mt-2 text-muted-foreground italic">
                  This may take a moment. Please don&apos;t refresh the page.
                </p>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
