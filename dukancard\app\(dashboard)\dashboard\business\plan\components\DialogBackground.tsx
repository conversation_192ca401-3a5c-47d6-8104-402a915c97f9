"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";

interface DialogBackgroundProps {
  variant?: "gold" | "blue" | "gradient";
  intensity?: "low" | "medium" | "high";
  className?: string;
}

export default function DialogBackground({
  variant = "gold",
  intensity = "medium",
  className = "",
}: DialogBackgroundProps) {
  const [isClient, setIsClient] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if we're on the client side and detect mobile
  useEffect(() => {
    setIsClient(true);
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // Set intensity values based on prop
  const getIntensityValue = () => {
    switch (intensity) {
      case "low":
        return { light: 0.05, dark: 0.1 };
      case "high":
        return { light: 0.15, dark: 0.25 };
      case "medium":
      default:
        return { light: 0.1, dark: 0.15 };
    }
  };

  const intensityValue = getIntensityValue();

  // Get background colors based on variant
  const getBgColors = () => {
    switch (variant) {
      case "blue":
        return {
          primary: `bg-blue-500/${intensityValue.light} dark:bg-blue-500/${intensityValue.dark}`,
          secondary: "bg-blue-300/5 dark:bg-blue-600/10",
        };
      case "gradient":
        return {
          primary: `bg-gradient-to-br from-[var(--brand-gold)]/${intensityValue.light} to-blue-500/${intensityValue.light} dark:from-[var(--brand-gold)]/${intensityValue.dark} dark:to-blue-500/${intensityValue.dark}`,
          secondary: "bg-transparent",
        };
      case "gold":
      default:
        return {
          primary: `bg-[var(--brand-gold)]/${intensityValue.light} dark:bg-[var(--brand-gold)]/${intensityValue.dark}`,
          secondary: "bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10",
        };
    }
  };

  const { primary, secondary } = getBgColors();

  if (!isClient) {
    return null;
  }

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {/* Main gradient background */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="absolute inset-0"
      >
        <motion.div
          animate={{
            y: [0, -8, 0],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          }}
          className={`absolute top-0 right-0 w-[70%] h-[70%] rounded-full blur-3xl opacity-70 ${primary}`}
          style={{
            filter: isMobile ? "blur(40px)" : "blur(60px)",
          }}
        />

        <motion.div
          animate={{
            y: [0, 10, 0],
            x: [0, -5, 0],
            scale: [1, 1.03, 1],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          }}
          className={`absolute bottom-0 left-0 w-[50%] h-[50%] rounded-full blur-3xl opacity-50 ${secondary}`}
          style={{
            filter: isMobile ? "blur(30px)" : "blur(50px)",
          }}
        />
      </motion.div>

      {/* Digital noise texture */}
      <div className="absolute inset-0 bg-[url('/noise.svg')] opacity-5 dark:opacity-10 mix-blend-overlay" />

      {/* Enhanced seamless gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-blue-500/5 dark:from-neutral-900/5 dark:to-blue-500/10" />
    </div>
  );
}
