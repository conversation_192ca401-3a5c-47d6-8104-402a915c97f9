'use client';

import React, { useEffect, useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useSearchParams } from 'next/navigation';
import { updateCustomerAddress, type AddressFormState } from '../actions';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { MapPin, Save, Loader2, Globe, Building2, Info } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Form,
} from "@/components/ui/form";
import { usePincodeDetails } from './hooks/usePincodeDetails';

// Address form schema with proper validation
const AddressFormSchema = z.object({
  address: z
    .string()
    .max(100, { message: "Address cannot exceed 100 characters." })
    .optional()
    .or(z.literal("")),
  pincode: z
    .string()
    .min(1, { message: "Pincode is required" })
    .regex(/^\d{6}$/, { message: "Must be a valid 6-digit pincode" }),
  city: z
    .string()
    .min(1, { message: "City is required" })
    .max(50, { message: "City cannot exceed 50 characters." })
    .refine((val) => val.trim().length > 0, { message: "City cannot be empty" }),
  state: z
    .string()
    .min(1, { message: "State is required" })
    .max(50, { message: "State cannot exceed 50 characters." })
    .refine((val) => val.trim().length > 0, { message: "State cannot be empty" }),
  locality: z
    .string()
    .min(1, { message: "Locality is required" })
    .refine((val) => val.trim().length > 0, { message: "Locality cannot be empty" }),
});

type AddressFormData = z.infer<typeof AddressFormSchema>;

interface AddressFormProps {
  initialData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  };
}

export default function AddressForm({ initialData }: AddressFormProps) {
  const [isPending, startTransition] = useTransition();
  const [formState, setFormState] = useState<AddressFormState>({
    message: null,
    success: false,
    errors: {}
  });
  const searchParams = useSearchParams();
  const redirectMessage = searchParams.get('message');

  const form = useForm<AddressFormData>({
    resolver: zodResolver(AddressFormSchema),
    defaultValues: {
      address: initialData?.address || '',
      pincode: initialData?.pincode || '',
      city: initialData?.city || '',
      state: initialData?.state || '',
      locality: initialData?.locality || '',
    },
  });

  // Use pincode details hook
  const { isPincodeLoading, availableLocalities, handlePincodeChange } =
    usePincodeDetails({
      form,
      initialPincode: initialData?.pincode,
      initialLocality: initialData?.locality,
    });

  // Handle redirect message toast
  useEffect(() => {
    if (redirectMessage) {
      toast.info(redirectMessage);
    }
  }, [redirectMessage]);

  // Handle form state changes
  useEffect(() => {
    if (formState.message) {
      if (formState.success) {
        toast.success(formState.message);
        // Reset form state after success
        setFormState({ message: null, success: false, errors: {} });
      } else {
        toast.error(formState.message);
      }
    }
  }, [formState]);

  const onSubmit = (data: AddressFormData) => {
    const formData = new FormData();
    formData.append('address', data.address || '');
    formData.append('pincode', data.pincode);
    formData.append('city', data.city);
    formData.append('state', data.state);
    formData.append('locality', data.locality);

    startTransition(async () => {
      try {
        const initialState: AddressFormState = {
          message: null,
          errors: {},
          success: false
        };

        const result = await updateCustomerAddress(initialState, formData);
        setFormState(result);
      } catch (error) {
        console.error('Error submitting address form:', error);
        setFormState({
          message: 'An unexpected error occurred. Please try again.',
          success: false,
          errors: {}
        });
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <MapPin className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold">Address Information</h3>
      </div>

      {/* Show redirect message if present */}
      {redirectMessage && (
        <Alert className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/50">
          <Info className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          <AlertDescription className="text-amber-800 dark:text-amber-200">
            {redirectMessage}
          </AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {/* Address Field (Optional) */}
          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">
                  Address (Optional)
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="e.g., House/Flat No., Street Name"
                    {...field}
                    value={field.value ?? ""}
                    className="w-full"
                  />
                </FormControl>
                <FormDescription className="text-xs text-muted-foreground">
                  Your street address or building details
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Pincode Field */}
          <FormField
            control={form.control}
            name="pincode"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium flex items-center gap-1.5">
                  <Globe className="h-4 w-4 text-primary" />
                  Pincode *
                </FormLabel>
                <div className="flex items-center gap-2">
                  <FormControl className="flex-1">
                    <Input
                      placeholder="e.g., 751001"
                      {...field}
                      value={field.value ?? ""}
                      maxLength={6}
                      type="number"
                      onChange={(e) => {
                        field.onChange(e);
                        if (e.target.value.length === 6) {
                          handlePincodeChange(e.target.value);
                        }
                      }}
                      onInput={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.value = target.value.replace(/[^0-9]/g, "");
                      }}
                    />
                  </FormControl>
                  {isPincodeLoading && (
                    <Loader2 className="h-4 w-4 animate-spin text-primary" />
                  )}
                </div>
                <FormDescription className="text-xs text-muted-foreground">
                  6-digit pincode to auto-fill city and state
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* City and State fields */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium flex items-center gap-1.5">
                    <MapPin className="h-4 w-4 text-primary/50" />
                    City *
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Auto-filled from Pincode"
                      {...field}
                      value={field.value ?? ""}
                      className="bg-muted cursor-not-allowed"
                      readOnly
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium flex items-center gap-1.5">
                    <MapPin className="h-4 w-4 text-primary/50" />
                    State *
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Auto-filled from Pincode"
                      {...field}
                      value={field.value ?? ""}
                      className="bg-muted cursor-not-allowed"
                      readOnly
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Locality Field */}
          <FormField
            control={form.control}
            name="locality"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium flex items-center gap-1.5">
                  <Building2 className="h-4 w-4 text-primary" />
                  Locality / Area *
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value ?? ""}
                  disabled={availableLocalities.length === 0}
                >
                  <FormControl>
                    <SelectTrigger
                      disabled={availableLocalities.length === 0}
                      className="w-full"
                    >
                      <SelectValue
                        placeholder={
                          availableLocalities.length === 0
                            ? "Enter Pincode first"
                            : "Select your locality"
                        }
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="w-full">
                    {availableLocalities.map((loc) => (
                      <SelectItem key={loc} value={loc}>
                        {loc}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription className="text-xs text-muted-foreground">
                  Select the specific area within the pincode
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="mt-6 flex justify-end">
            <div className="relative group">
              {/* Border glow effect - matches button size */}
              <div
                className="absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300"
                style={{
                  boxShadow: `inset 0 0 20px rgba(147, 51, 234, 0.2), 0 0 20px rgba(147, 51, 234, 0.3)`
                }}
              />

              {/* Strong decorative colored glow elements - positioned relative to button */}
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none" />
              <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none" />
              <Button
                type="submit"
                disabled={isPending}
                variant="outline"
                size="sm"
                className={`
                  relative overflow-hidden rounded-xl p-3
                  bg-white dark:bg-black
                  border border-purple-200/50 dark:border-purple-700/50
                  shadow-purple-500/40 shadow-lg
                  hover:shadow-xl hover:shadow-purple-500/40
                  transition-all duration-300
                  text-purple-500 dark:text-purple-400
                  hover:bg-purple-500/5 dark:hover:bg-purple-500/10
                  text-xs sm:text-sm h-auto
                  ${isPending ? 'cursor-not-allowed opacity-80' : ''}
                `}
              >
              <AnimatePresence mode="wait">
                {isPending ? (
                  <motion.div
                    key="updating"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </motion.div>
                ) : (
                  <motion.div
                    key="update"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Update Address
                  </motion.div>
                )}
              </AnimatePresence>
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
