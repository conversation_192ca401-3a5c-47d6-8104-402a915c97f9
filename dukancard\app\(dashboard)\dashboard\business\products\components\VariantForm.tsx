"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion, AnimatePresence } from "framer-motion";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Package, Tag, Plus, X, AlertTriangle } from "lucide-react";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { useProductMultiImageUpload, ProductImage } from "./hooks/useProductMultiImageUpload";
import ProductImageCropDialog from "./ProductImageCropDialog";
import ProductMultiImageUpload from "./ProductMultiImageUpload";
import { addVariantFormSchema, updateVariantFormSchema } from "../actions/schemas";
import { ProductVariant } from "@/types/variants";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  getPredefinedOptionsForType,
  getAllVariantTypes,
} from "@/lib/constants/predefinedVariants";


// Form schema types
import { z } from "zod";
type AddVariantFormValues = z.infer<typeof addVariantFormSchema>;
type UpdateVariantFormValues = z.infer<typeof updateVariantFormSchema>;
type VariantFormValues = AddVariantFormValues | UpdateVariantFormValues;

interface VariantFormProps {
  productId: string;
  initialData?: ProductVariant | null;
  onSubmit: (
    _values: VariantFormValues,
    _imageFiles?: (File | null)[],
    _featuredImageIndex?: number,
    _removedImageIndices?: number[],
    _currentImages?: ProductImage[]
  ) => Promise<void>;
  isSubmitting: boolean;
  onCancel?: () => void;
}

export default function VariantForm({
  productId,
  initialData,
  onSubmit,
  isSubmitting,
  onCancel,
}: VariantFormProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [variantValues, setVariantValues] = useState<Record<string, string>>(
    initialData?.variant_values || {}
  );
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);

  // Determine if this is an edit form
  const isEdit = !!initialData;

  // State for combobox open/close
  const [openTypeCombobox, setOpenTypeCombobox] = useState<string | null>(null);
  const [openValueCombobox, setOpenValueCombobox] = useState<string | null>(null);

  // Use our custom hook for multi-image upload with cropping
  const {
    images,
    featuredImageIndex,
    removedOriginalIndices,
    imageToCrop,
    imageErrorDisplay,
    addImageSlot,
    removeImage,
    setAsFeatured,
    handleFileSelect,
    handleCropComplete,
    handleCropDialogClose
  } = useProductMultiImageUpload({
    initialImageUrls: initialData?.images || null,
    initialFeaturedIndex: initialData?.featured_image_index || 0,
    maxImages: 5
  });

  const form = useForm({
    resolver: zodResolver(addVariantFormSchema),
    defaultValues: {
      product_id: productId,
      variant_name: initialData?.variant_name || "",
      variant_values: initialData?.variant_values || {},
      base_price: initialData?.base_price || undefined,
      discounted_price: initialData?.discounted_price || undefined,
      is_available: initialData?.is_available ?? true,
      images: initialData?.images || [],
      featured_image_index: initialData?.featured_image_index || 0,
    },
  });

  // Simulate loading for consistent UX
  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  // Validation functions
  const validateVariantValues = (values: Record<string, string>): string[] => {
    const errors: string[] = [];

    // Check for empty keys or values
    Object.entries(values).forEach(([key, value]) => {
      if (!key.trim()) {
        errors.push("Variant type names cannot be empty");
      }
      if (!value.trim()) {
        errors.push("Variant values cannot be empty");
      }
      if (key.trim().length > 50) {
        errors.push("Variant type names cannot exceed 50 characters");
      }
      if (value.trim().length > 50) {
        errors.push("Variant values cannot exceed 50 characters");
      }
    });

    // Check for duplicate keys
    const keys = Object.keys(values).map(k => k.trim().toLowerCase());
    const uniqueKeys = new Set(keys);
    if (keys.length !== uniqueKeys.size) {
      errors.push("Duplicate variant type names are not allowed");
    }

    // Check for minimum requirement
    if (Object.keys(values).length === 0) {
      errors.push("At least one variant property is required");
    }

    return [...new Set(errors)]; // Remove duplicates
  };

  const validatePricing = (basePrice?: number, discountedPrice?: number): string[] => {
    const errors: string[] = [];

    if (basePrice !== undefined && basePrice <= 0) {
      errors.push("Base price must be greater than 0");
    }

    if (discountedPrice !== undefined && discountedPrice <= 0) {
      errors.push("Discounted price must be greater than 0");
    }

    if (basePrice && discountedPrice && discountedPrice >= basePrice) {
      errors.push("Discounted price must be less than base price");
    }

    return errors;
  };

  // Update form when variant values change
  useEffect(() => {
    form.setValue("variant_values", variantValues);

    // Validate variant values in real-time
    setIsValidating(true);
    const errors = validateVariantValues(variantValues);
    setValidationErrors(errors);
    setIsValidating(false);
  }, [variantValues, form]);

  const handleFormSubmit = async (values: VariantFormValues) => {
    try {
      setIsValidating(true);

      // Validate variant values
      const variantErrors = validateVariantValues(values.variant_values || {});
      const pricingErrors = validatePricing(
        values.base_price || undefined,
        values.discounted_price || undefined
      );
      const allErrors = [...variantErrors, ...pricingErrors];

      if (allErrors.length > 0) {
        setValidationErrors(allErrors);
        setIsValidating(false);
        toast.error("Please fix the validation errors before submitting");
        return;
      }

      // Clear validation errors
      setValidationErrors([]);

      // Get image files from the upload hook
      const imageFiles = images.map(img => img.file);
      // Use the removed indices tracked by the hook
      const removedImageIndices = removedOriginalIndices;

      // Add the ID for edit operations
      const submitValues = isEdit && initialData?.id
        ? { ...values, id: initialData.id }
        : values;

      await onSubmit(submitValues, imageFiles, featuredImageIndex, removedImageIndices, images);

      // Clear validation state on success
      setValidationErrors([]);
      setIsValidating(false);
    } catch (error) {
      console.error("Error submitting variant form:", error);
      setIsValidating(false);
      toast.error("Failed to save variant. Please try again.");
    }
  };

  // Add variant type-value pair
  const addVariantValue = () => {
    if (Object.keys(variantValues).length >= 5) {
      toast.error("Maximum of 5 variant types allowed per product");
      return;
    }

    // Find the first unused predefined type
    const usedTypes = Object.keys(variantValues).map(k => k.toLowerCase());
    const availableType = getAllVariantTypes().find(type =>
      !usedTypes.includes(type.name.toLowerCase())
    );

    const newKey = availableType ? availableType.name : `type_${Object.keys(variantValues).length + 1}`;
    setVariantValues(prev => ({ ...prev, [newKey]: "" }));
  };

  // Remove variant type-value pair
  const removeVariantValue = (key: string) => {
    setVariantValues(prev => {
      const newValues = { ...prev };
      delete newValues[key];
      return newValues;
    });
  };

  // Update variant type key with validation
  const updateVariantType = (oldKey: string, newKey: string) => {
    if (oldKey === newKey) return;

    // Validate new key
    const trimmedKey = newKey.trim();
    if (trimmedKey.length > 50) {
      toast.error("Variant type name cannot exceed 50 characters");
      return;
    }

    // Check for duplicate keys (case-insensitive)
    const existingKeys = Object.keys(variantValues)
      .filter(k => k !== oldKey)
      .map(k => k.trim().toLowerCase());

    if (existingKeys.includes(trimmedKey.toLowerCase())) {
      toast.error("Variant type name already exists");
      return;
    }

    setVariantValues(prev => {
      const newValues = { ...prev };
      const value = newValues[oldKey];
      delete newValues[oldKey];
      newValues[trimmedKey] = value;
      return newValues;
    });
  };

  // Update variant value with validation
  const updateVariantValue = (key: string, value: string) => {
    const trimmedValue = value.trim();
    if (trimmedValue.length > 50) {
      toast.error("Variant value cannot exceed 50 characters");
      return;
    }

    setVariantValues(prev => ({ ...prev, [key]: trimmedValue }));
  };

  if (isLoading) {
    return <SkeletonForm />;
  }

  return (
    <div className="space-y-6">
      {/* Validation Errors Display */}
      {validationErrors.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <div className="font-medium">Please fix the following errors:</div>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-6">
        <div className="space-y-4">
            <AnimatePresence mode="wait">
              <motion.div
                key="variant-name"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
              >
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                    Variant Name
                  </Label>
                  <div className="relative">
                    <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" />
                    <Input
                      placeholder="e.g., Red Large, 64GB Blue, Cotton Medium"
                      className="pl-10"
                      value={form.watch("variant_name") || ""}
                      onChange={(e) => form.setValue("variant_name", e.target.value)}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    A descriptive name for this variant combination
                  </p>
                </div>
              </motion.div>

              <motion.div
                key="variant-values"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                      Variant Properties
                    </label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addVariantValue}
                      disabled={Object.keys(variantValues).length >= 5}
                      className="h-8 px-2 text-xs"
                    >
                      <Plus className="h-3.5 w-3.5 mr-1" />
                      Add Property
                    </Button>
                  </div>

                  <div className="space-y-2">
                    {Object.entries(variantValues).map(([key, value]) => {
                      const predefinedValues = getPredefinedOptionsForType(key);

                      return (
                        <motion.div
                          key={key}
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="flex gap-2 items-center"
                        >
                          {/* Variant Type Combobox */}
                          <Popover
                            open={openTypeCombobox === key}
                            onOpenChange={(open) => setOpenTypeCombobox(open ? key : null)}
                          >
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                role="combobox"
                                aria-expanded={openTypeCombobox === key}
                                className="flex-1 justify-between bg-white dark:bg-black border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-900"
                              >
                                {key ? getAllVariantTypes().find((type) => type.name === key)?.display_name || key : "Select type..."}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[300px] p-0 bg-white dark:bg-black border-neutral-200 dark:border-neutral-700">
                              <Command className="bg-white dark:bg-black">
                                <CommandInput placeholder="Search variant type..." className="h-9 bg-white dark:bg-black border-none focus:ring-0 focus:ring-offset-0 focus:border-transparent focus:outline-none" />
                                <CommandList className="max-h-[200px] bg-white dark:bg-black">
                                  <CommandEmpty className="bg-white dark:bg-black">No variant type found.</CommandEmpty>
                                  <CommandGroup className="bg-white dark:bg-black">
                                    {getAllVariantTypes().map((type) => (
                                      <CommandItem
                                        key={type.name}
                                        value={type.name}
                                        onSelect={(currentValue) => {
                                          updateVariantType(key, currentValue);
                                          setOpenTypeCombobox(null);
                                        }}
                                        className="bg-white dark:bg-black hover:bg-neutral-100 dark:hover:bg-neutral-800 cursor-pointer"
                                      >
                                        <div className="flex flex-col flex-1">
                                          <span className="font-medium">{type.display_name}</span>
                                          {type.description && (
                                            <span className="text-xs text-muted-foreground">
                                              {type.description}
                                            </span>
                                          )}
                                        </div>
                                        <Check
                                          className={cn(
                                            "ml-auto h-4 w-4",
                                            key === type.name ? "opacity-100" : "opacity-0"
                                          )}
                                        />
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>

                          {/* Variant Value Input - Combobox for colors, text input for others */}
                          {key.toLowerCase() === 'color' && predefinedValues.length > 0 ? (
                            <Popover
                              open={openValueCombobox === key}
                              onOpenChange={(open) => setOpenValueCombobox(open ? key : null)}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  aria-expanded={openValueCombobox === key}
                                  className="flex-1 justify-between bg-white dark:bg-black border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-900"
                                >
                                  {value ? predefinedValues.find((option) => option.value === value)?.display_value || value : "Select color..."}
                                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-[300px] p-0 bg-white dark:bg-black border-neutral-200 dark:border-neutral-700">
                                <Command className="bg-white dark:bg-black">
                                  <CommandInput placeholder="Search color..." className="h-9 bg-white dark:bg-black border-none focus:ring-0 focus:ring-offset-0 focus:border-transparent focus:outline-none" />
                                  <CommandList className="max-h-[200px] bg-white dark:bg-black">
                                    <CommandEmpty className="bg-white dark:bg-black">No color found.</CommandEmpty>
                                    <CommandGroup className="bg-white dark:bg-black">
                                      {predefinedValues.map((option) => (
                                        <CommandItem
                                          key={option.value}
                                          value={option.value}
                                          onSelect={(currentValue) => {
                                            updateVariantValue(key, currentValue);
                                            setOpenValueCombobox(null);
                                          }}
                                          className="bg-white dark:bg-black hover:bg-neutral-100 dark:hover:bg-neutral-800 cursor-pointer"
                                        >
                                          <div className="flex items-center gap-2 flex-1">
                                            {option.color_code && (
                                              <div
                                                className="w-4 h-4 rounded-full border border-neutral-300"
                                                style={{ backgroundColor: option.color_code }}
                                              />
                                            )}
                                            <span>{option.display_value}</span>
                                          </div>
                                          <Check
                                            className={cn(
                                              "ml-auto h-4 w-4",
                                              value === option.value ? "opacity-100" : "opacity-0"
                                            )}
                                          />
                                        </CommandItem>
                                      ))}
                                    </CommandGroup>
                                  </CommandList>
                                </Command>
                              </PopoverContent>
                            </Popover>
                          ) : (
                            <Input
                              type="text"
                              placeholder={`Enter ${key.toLowerCase()}...`}
                              value={value || ""}
                              onChange={(e) => updateVariantValue(key, e.target.value)}
                              className="flex-1 bg-white dark:bg-black border-neutral-200 dark:border-neutral-700"
                            />
                          )}

                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeVariantValue(key)}
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </motion.div>
                      );
                    })}

                    {Object.keys(variantValues).length === 0 && (
                      <div className="text-sm text-neutral-500 text-center py-4 border-2 border-dashed border-neutral-200 dark:border-neutral-700 rounded-lg">
                        No variant properties added yet. Click &quot;Add Property&quot; to start.
                      </div>
                    )}
                  </div>

                  <p className="text-sm text-muted-foreground">
                    Add up to 5 variant properties (e.g., color: red, size: large)
                  </p>
                </div>
              </motion.div>

              <motion.div
                key="pricing"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
                className="grid grid-cols-1 md:grid-cols-2 gap-4"
              >
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                    Base Price (₹)
                  </Label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    value={form.watch("base_price") || ""}
                    onChange={(e) => form.setValue("base_price", e.target.value ? parseFloat(e.target.value) : undefined)}
                  />
                  <p className="text-sm text-muted-foreground">
                    Leave empty to use product&apos;s base price
                  </p>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                    Discounted Price (₹)
                  </Label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    value={form.watch("discounted_price") || ""}
                    onChange={(e) => form.setValue("discounted_price", e.target.value ? parseFloat(e.target.value) : undefined)}
                  />
                  <p className="text-sm text-muted-foreground">
                    Optional discounted price for this variant
                  </p>
                </div>
              </motion.div>

              <motion.div
                key="availability"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
              >
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <Label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                      Available for Purchase
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Toggle to make this variant available to customers
                    </p>
                  </div>
                  <Switch
                    checked={form.watch("is_available") || false}
                    onCheckedChange={(checked) => form.setValue("is_available", checked)}
                    className="data-[state=checked]:bg-primary"
                  />
                </div>
              </motion.div>

              <motion.div
                key="images"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
              >
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                    Variant Images
                  </label>
                  <ProductMultiImageUpload
                    images={images}
                    featuredImageIndex={featuredImageIndex}
                    disabled={isSubmitting}
                    maxImages={5}
                    onAddImage={addImageSlot}
                    onRemoveImage={removeImage}
                    onSetFeatured={setAsFeatured}
                    onFileSelect={handleFileSelect}
                    errorDisplay={imageErrorDisplay}
                  />
                  <p className="text-sm text-muted-foreground">
                    Add images specific to this variant. If no images are added, the product&apos;s main images will be used.
                  </p>
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Form Actions */}
            <div className="flex gap-3 pt-4 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  Cancel
                </Button>
              )}
              <Button
                type="button"
                disabled={
                  isSubmitting ||
                  isValidating ||
                  Object.keys(variantValues).length === 0 ||
                  validationErrors.length > 0
                }
                className="flex-1 bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]"
                onClick={() => {
                  const formData = form.getValues();
                  handleFormSubmit(formData);
                }}
              >
                {isSubmitting || isValidating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isValidating ? "Validating..." : isEdit ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  <>
                    <Package className="mr-2 h-4 w-4" />
                    {isEdit ? "Update Variant" : "Create Variant"}
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

      {/* Image Crop Dialog */}
      <ProductImageCropDialog
        imgSrc={imageToCrop?.dataUrl || null}
        onCropComplete={handleCropComplete}
        onClose={handleCropDialogClose}
        isOpen={!!imageToCrop}
      />
    </div>
  );
}

function SkeletonForm() {
  return (
    <div className="space-y-6">
      {/* Variant Name Skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-10 w-full rounded-md" />
        <Skeleton className="h-3 w-48" />
      </div>

      {/* Variant Properties Skeleton */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-8 w-24 rounded-md" />
        </div>
        <div className="space-y-2">
          <div className="flex gap-2">
            <Skeleton className="h-10 flex-1 rounded-md" />
            <Skeleton className="h-10 flex-1 rounded-md" />
            <Skeleton className="h-8 w-8 rounded-md" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-10 flex-1 rounded-md" />
            <Skeleton className="h-10 flex-1 rounded-md" />
            <Skeleton className="h-8 w-8 rounded-md" />
          </div>
        </div>
        <Skeleton className="h-3 w-64" />
      </div>

      {/* Pricing Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Skeleton className="h-5 w-20" />
          <Skeleton className="h-10 w-full rounded-md" />
          <Skeleton className="h-3 w-40" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-5 w-28" />
          <Skeleton className="h-10 w-full rounded-md" />
          <Skeleton className="h-3 w-44" />
        </div>
      </div>

      {/* Availability Skeleton */}
      <div className="flex items-center justify-between rounded-lg border p-4">
        <div className="space-y-1">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-3 w-56" />
        </div>
        <Skeleton className="h-6 w-12 rounded-full" />
      </div>

      {/* Images Skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-5 w-24" />
        <div className="flex items-start gap-4">
          <Skeleton className="h-32 w-32 rounded-lg" />
          <div className="flex-1">
            <Skeleton className="h-10 w-full rounded-md" />
            <Skeleton className="h-3 w-full mt-2" />
          </div>
        </div>
        <Skeleton className="h-3 w-80" />
      </div>

      {/* Actions Skeleton */}
      <div className="flex gap-3 pt-4 border-t">
        <Skeleton className="h-10 flex-1 rounded-md" />
        <Skeleton className="h-10 flex-1 rounded-md" />
      </div>
    </div>
  );
}
