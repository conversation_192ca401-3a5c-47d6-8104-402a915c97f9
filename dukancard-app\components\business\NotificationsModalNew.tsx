import React, { useCallback, useState, useRef, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  RefreshControl,
  ScrollView,
} from 'react-native';
import { X, Bell } from 'lucide-react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useRouter } from 'expo-router';
import { useNotifications } from '@/contexts/NotificationContext';
import { ActivityData } from '@/lib/services/activityService';
import { ActivityItem } from './ActivityItem';

export const NotificationsModal: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();

  const {
    notifications,
    unreadCount,
    loading,
    refreshing,
    isModalVisible,
    hideModal,
    refreshNotifications,
    markAsRead,
    markAllAsRead
  } = useNotifications();

  // Track which unread notifications have been viewed in this session
  const [viewedUnreadIds, setViewedUnreadIds] = useState<Set<string>>(new Set());
  // Snapshot of notifications when modal first opens (for section determination)
  const [notificationsSnapshot, setNotificationsSnapshot] = useState<ActivityData[]>([]);
  const scrollViewRef = useRef<ScrollView>(null);

  // Capture snapshot when modal opens
  useEffect(() => {
    if (isModalVisible) {
      setViewedUnreadIds(new Set());
      // Take a snapshot of notifications with their current read status
      setNotificationsSnapshot([...notifications]);
    }
  }, [isModalVisible]);

  // Check if all initially unread notifications have been viewed and mark them as read
  useEffect(() => {
    // Get notifications that were unread when modal opened (from snapshot)
    const initiallyUnreadNotifications = notificationsSnapshot.filter(n => !n.is_read);
    const initiallyUnreadIds = initiallyUnreadNotifications.map(n => n.id);

    // If we have initially unread notifications and all of them have been viewed
    if (initiallyUnreadIds.length > 0 && initiallyUnreadIds.every(id => viewedUnreadIds.has(id))) {
      // Mark all as read after a short delay to ensure user has actually seen them
      const timer = setTimeout(() => {
        markAllAsRead();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [viewedUnreadIds, notificationsSnapshot, markAllAsRead]);

  // Function to mark a notification as viewed (when it comes into view)
  const markAsViewed = useCallback((notificationId: string) => {
    // Only track notifications that were initially unread when modal opened
    const wasInitiallyUnread = notificationsSnapshot.find(n => n.id === notificationId)?.is_read === false;
    if (wasInitiallyUnread) {
      setViewedUnreadIds(prev => new Set([...prev, notificationId]));
    }
  }, [notificationsSnapshot]);

  // Handle activity click navigation
  const handleActivityPress = useCallback((activity: ActivityData) => {
    // Mark as read when clicked (if unread)
    if (!activity.is_read) {
      markAsRead(activity.id);
    }

    // Navigate to business public card if this is a business activity with slug
    const businessSlug = activity.user_profile?.business_slug;
    if (businessSlug) {
      // Close modal first
      hideModal();

      // Navigate to business public card
      router.push(`/business/${businessSlug}`);
    }
    // For customer activities, no navigation action
  }, [markAsRead, hideModal, router]);

  // Separate notifications into new and old based on INITIAL read status when modal opened
  const newNotifications = notifications.filter(notification => {
    const snapshotNotification = notificationsSnapshot.find(n => n.id === notification.id);
    return snapshotNotification?.is_read === false;
  });
  const oldNotifications = notifications.filter(notification => {
    const snapshotNotification = notificationsSnapshot.find(n => n.id === notification.id);
    return snapshotNotification?.is_read === true;
  });

  // Render section header
  const renderSectionHeader = (title: string) => (
    <View style={[styles.sectionHeader, { backgroundColor: isDark ? '#111827' : '#f9fafb' }]}>
      <Text style={[styles.sectionTitle, { color: isDark ? '#f3f4f6' : '#374151' }]}>
        {title}
      </Text>
    </View>
  );



  // Render notifications with sections
  const renderNotificationSections = () => {
    const sections = [];

    // Add new notifications section
    if (newNotifications.length > 0) {
      sections.push(
        <View key="new-section">
          {renderSectionHeader('New')}
          {newNotifications.map((notification) => (
            <ActivityItem
              key={notification.id}
              activity={notification}
              onPress={() => handleActivityPress(notification)}
              onView={markAsViewed}
            />
          ))}
        </View>
      );
    }

    // Add old notifications section
    if (oldNotifications.length > 0) {
      sections.push(
        <View key="old-section">
          {renderSectionHeader('Earlier')}
          {oldNotifications.map((notification) => (
            <ActivityItem
              key={notification.id}
              activity={notification}
              onPress={() => handleActivityPress(notification)}
              onView={markAsViewed}
            />
          ))}
        </View>
      );
    }

    return sections;
  };

  // Render footer for infinite scroll
  const renderFooter = () => {
    if (!loading) return null;
    
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#D4AF37" />
        <Text style={[styles.footerText, { color: isDark ? '#9ca3af' : '#6b7280' }]}>
          Loading more activities...
        </Text>
      </View>
    );
  };

  // Render loading skeleton
  const renderLoadingSkeleton = () => (
    <View style={styles.skeletonContainer}>
      {Array.from({ length: 5 }).map((_, index) => (
        <View key={index} style={[styles.skeletonItem, { backgroundColor: isDark ? '#1f2937' : '#f3f4f6' }]}>
          <View style={[styles.skeletonAvatar, { backgroundColor: isDark ? '#374151' : '#e5e7eb' }]} />
          <View style={styles.skeletonContent}>
            <View style={[styles.skeletonLine, styles.skeletonLineTitle, { backgroundColor: isDark ? '#374151' : '#e5e7eb' }]} />
            <View style={[styles.skeletonLine, styles.skeletonLineSubtitle, { backgroundColor: isDark ? '#374151' : '#e5e7eb' }]} />
            <View style={[styles.skeletonLine, styles.skeletonLineTime, { backgroundColor: isDark ? '#374151' : '#e5e7eb' }]} />
          </View>
        </View>
      ))}
    </View>
  );

  // Render empty state
  const renderEmpty = () => (
    <View style={styles.emptyState}>
      <Bell size={48} color={isDark ? '#6b7280' : '#9ca3af'} />
      <Text style={[styles.emptyTitle, { color: isDark ? '#f3f4f6' : '#374151' }]}>
        No activities yet
      </Text>
      <Text style={[styles.emptyDescription, { color: isDark ? '#9ca3af' : '#6b7280' }]}>
        When customers interact with your business, you&apos;ll see their activities here.
      </Text>
    </View>
  );

  return (
    <Modal
      visible={isModalVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={hideModal}
    >
      <View style={[styles.container, { backgroundColor: isDark ? '#000000' : '#FFFFFF' }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: isDark ? '#374151' : '#e5e7eb' }]}>
          <View style={styles.headerLeft}>
            <Bell size={24} color="#D4AF37" />
            <Text style={[styles.headerTitle, { color: isDark ? '#f3f4f6' : '#374151' }]}>
              Notifications
            </Text>
            {unreadCount > 0 && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadBadgeText}>{unreadCount}</Text>
              </View>
            )}
          </View>
          
          <TouchableOpacity style={styles.closeButton} onPress={hideModal}>
            <X size={24} color={isDark ? '#f3f4f6' : '#374151'} />
          </TouchableOpacity>
        </View>

        {/* Activities List */}
        {refreshing && notifications.length === 0 ? (
          renderLoadingSkeleton()
        ) : notifications.length === 0 ? (
          renderEmpty()
        ) : (
          <FlatList
            data={[{ type: 'sections' }]} // Dummy data to render sections
            renderItem={() => (
              <View>
                {renderNotificationSections()}
              </View>
            )}
            keyExtractor={() => 'sections'}
            ListFooterComponent={renderFooter}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={refreshNotifications}
                colors={['#D4AF37']}
                tintColor="#D4AF37"
              />
            }
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  unreadBadge: {
    backgroundColor: '#ef4444',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
    minWidth: 20,
    alignItems: 'center',
  },
  unreadBadgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  footerLoader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  footerText: {
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  // Skeleton styles
  skeletonContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  skeletonItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 4,
    borderRadius: 8,
    gap: 12,
  },
  skeletonAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  skeletonContent: {
    flex: 1,
    gap: 6,
  },
  skeletonLine: {
    borderRadius: 4,
  },
  skeletonLineTitle: {
    height: 16,
    width: '80%',
  },
  skeletonLineSubtitle: {
    height: 14,
    width: '60%',
  },
  skeletonLineTime: {
    height: 12,
    width: '40%',
  },
  // Section styles
  sectionHeader: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
});

// Export with alias for backward compatibility
export const NotificationsModalNew = NotificationsModal;
