import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { useRouter } from 'expo-router';
import { useNetworkStatus } from '@/lib/utils/networkStatus';
import { OfflineBanner } from '@/components/ui/OfflineComponents';
import { useAuth } from '@/contexts/AuthContext';
import UnifiedBottomNavigation from '@/components/shared/navigation/UnifiedBottomNavigation';

// Import screen components
import DiscoverScreen from '@/components/shared/screens/DiscoverScreen';
import CustomerFeedScreen from './index';
import CustomerProfileScreen from './profile';

export default function CustomerTabLayout() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const networkStatus = useNetworkStatus();
  const { user, profileStatus } = useAuth();
  const [activeTab, setActiveTab] = useState('home');

  // Get customer name from profile
  const customerName = profileStatus?.roleStatus?.hasCustomerProfile
    ? user?.user_metadata?.name || user?.email?.split('@')[0] || 'Valued Customer'
    : 'Valued Customer';

  const renderActiveScreen = () => {
    switch (activeTab) {
      case 'home':
        return <CustomerFeedScreen />;
      case 'discover':
        return <DiscoverScreen userName={customerName} showNotifications={true} />;
      case 'profile':
        return <CustomerProfileScreen />;
      default:
        return <CustomerFeedScreen />;
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <View style={styles.container}>
      {/* Offline Banner */}
      <OfflineBanner visible={!networkStatus.isConnected} />

      <View style={styles.screenContainer}>
        {renderActiveScreen()}
      </View>

      <UnifiedBottomNavigation
        activeTab={activeTab}
        onTabChange={handleTabChange}
        showQRScanner={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  screenContainer: {
    flex: 1,
  },
});
