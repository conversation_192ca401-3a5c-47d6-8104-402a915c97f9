"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { 
  CreditCard, 
  Star, 
  CheckCircle, 
  DollarSign, 
  Percent, 
  Calendar, 
  Clock, 
  Award,
  Zap
} from "lucide-react";

interface FloatingPricingElementsProps {
  minimal?: boolean;
}

export default function FloatingPricingElements({ minimal = false }: FloatingPricingElementsProps) {
  const [isClient, setIsClient] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    setIsClient(true);
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Define floating elements with responsive positioning
  const elements = [
    {
      id: 1,
      icon: CreditCard,
      top: "10%",
      left: "5%",
      size: 24,
      color: "var(--brand-gold)",
      duration: 8,
      delay: 0,
    },
    {
      id: 2,
      icon: Star,
      top: "15%",
      right: "8%",
      size: 20,
      color: "var(--brand-gold)",
      duration: 7,
      delay: 0.5,
    },
    {
      id: 3,
      icon: CheckCircle,
      bottom: "20%",
      left: "7%",
      size: 22,
      color: "rgb(59, 130, 246)", // blue
      duration: 9,
      delay: 1,
    },
    {
      id: 4,
      icon: DollarSign,
      bottom: "25%",
      right: "10%",
      size: 26,
      color: "var(--brand-gold)",
      duration: 8.5,
      delay: 1.5,
    },
    {
      id: 5,
      icon: Percent,
      top: "40%",
      left: "3%",
      size: 18,
      color: "rgb(59, 130, 246)", // blue
      duration: 7.5,
      delay: 2,
    },
    {
      id: 6,
      icon: Calendar,
      top: "45%",
      right: "4%",
      size: 20,
      color: "rgb(139, 92, 246)", // purple
      duration: 9.5,
      delay: 2.5,
    },
    {
      id: 7,
      icon: Clock,
      bottom: "10%",
      left: "15%",
      size: 16,
      color: "rgb(139, 92, 246)", // purple
      duration: 8,
      delay: 3,
    },
    {
      id: 8,
      icon: Award,
      bottom: "15%",
      right: "15%",
      size: 22,
      color: "var(--brand-gold)",
      duration: 9,
      delay: 3.5,
    },
    {
      id: 9,
      icon: Zap,
      top: "30%",
      left: "12%",
      size: 18,
      color: "var(--brand-gold)",
      duration: 7,
      delay: 4,
    },
  ];

  // For minimal mode, show fewer elements
  const displayElements = minimal ? elements.filter(e => [1, 2, 4, 6, 8].includes(e.id)) : elements;

  if (!isClient) return null;
  if (isMobile && minimal) return null;

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {displayElements.map((element) => {
        const Icon = element.icon;
        return (
          <motion.div
            key={element.id}
            className="absolute"
            style={{
              top: element.top,
              left: element.left,
              right: element.right,
              bottom: element.bottom,
              color: element.color,
              opacity: 0.2,
            }}
            animate={{
              y: [0, -15, 0],
              x: [0, 10, 0],
              rotate: [0, 10, 0],
              scale: [1, 1.1, 1],
              opacity: [0.15, 0.25, 0.15],
            }}
            transition={{
              duration: element.duration,
              delay: element.delay,
              repeat: Infinity,
              repeatType: "mirror",
              ease: "easeInOut",
            }}
          >
            <Icon size={element.size} />
          </motion.div>
        );
      })}
    </div>
  );
}
