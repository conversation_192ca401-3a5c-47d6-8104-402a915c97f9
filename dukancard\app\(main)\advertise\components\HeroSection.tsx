"use client";

import { ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { motion, Variants } from "framer-motion";

// Define the Particle interface
interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  color: string;
  duration: number;
  delay: number;
}

export default function HeroSection() {
  const [particles, setParticles] = useState<Particle[]>([]);

  // Generate random particles for animation
  useEffect(() => {
    const generateParticles = () => {
      const newParticles: Particle[] = [];
      const colors = [
        "var(--brand-gold)",
        "#3b82f6",
        "#8b5cf6",
        "rgba(var(--brand-gold-rgb), 0.7)",
      ];

      for (let i = 0; i < 30; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * 6 + 2,
          color: colors[Math.floor(Math.random() * colors.length)],
          duration: Math.random() * 10 + 10,
          delay: Math.random() * 5,
        });
      }
      setParticles(newParticles);
    };

    generateParticles();
  }, []);

  const scrollToContact = () => {
    const contactSection = document.getElementById("contact-section");
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Animation variants
  const sectionVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: i * 0.1,
      },
    }),
  };

  const particleVariants: Variants = {
    animate: (particle: Particle) => ({
      x: [0, particle.id % 2 === 0 ? 10 : -10, 0],
      y: [0, particle.id % 3 === 0 ? 15 : -15, 0],
      transition: {
        duration: particle.duration,
        delay: particle.delay,
        repeat: Infinity,
        repeatType: "reverse" as const, // Explicitly set as literal type
        ease: "easeInOut",
      },
    }),
  };

  const underlineVariants: Variants = {
    hidden: { width: 0 },
    visible: {
      width: "100%",
      transition: {
        duration: 0.8,
        delay: 0.5,
      },
    },
  };

  return (
    <motion.section
      className="relative w-full overflow-hidden pt-20 pb-16 md:pt-28 md:pb-24"
      initial="hidden"
      animate="visible"
      variants={sectionVariants}
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        {/* Gradient background */}
        <motion.div
          className="absolute -top-20 -left-20 w-96 h-96 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/15 rounded-full blur-3xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.5 }}
          transition={{ duration: 1 }}
        />
        <motion.div
          className="absolute -bottom-20 -right-20 w-80 h-80 bg-blue-500/5 dark:bg-blue-500/10 rounded-full blur-3xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.6 }}
          transition={{ duration: 1, delay: 0.2 }}
        />

        {/* Animated particles */}
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              backgroundColor: particle.color,
            }}
            custom={particle}
            variants={particleVariants}
            animate="animate"
          />
        ))}
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div custom={0} variants={itemVariants}>
            <span className="inline-flex items-center rounded-full border border-neutral-200 dark:border-neutral-800 px-4 py-1 text-sm font-medium text-[var(--brand-gold)]">
              Grow Your Business
            </span>
          </motion.div>

          <motion.h1
            className="text-4xl md:text-6xl font-bold text-foreground mb-6"
            custom={1}
            variants={itemVariants}
          >
            Advertise with{" "}
            <span className="text-[var(--brand-gold)] relative">
              Dukancard
              {/* Animated underline */}
              <motion.div
                className="absolute -bottom-1 left-0 h-1 bg-gradient-to-r from-[var(--brand-gold)]/30 via-[var(--brand-gold)] to-[var(--brand-gold)]/30 rounded-full"
                variants={underlineVariants}
              />
            </span>
          </motion.h1>

          <motion.p
            className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto"
            custom={2}
            variants={itemVariants}
          >
            Reach potential customers in specific localities across India with our targeted advertising platform. Promote your business to the right audience at the right time.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center"
            custom={3}
            variants={itemVariants}
          >
            <div className="relative group">
              {/* Button glow effect */}
              <motion.div
                className="absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/60 to-[var(--brand-gold)]/80 rounded-full blur-md"
                initial={{ opacity: 0.7 }}
                whileHover={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              />
              <Button
                onClick={scrollToContact}
                className="relative bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-6 py-6 rounded-full"
              >
                <motion.span
                  className="flex items-center"
                  whileHover={{ x: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  Contact Us Now
                  <ArrowRight className="ml-2 h-4 w-4" />
                </motion.span>
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}