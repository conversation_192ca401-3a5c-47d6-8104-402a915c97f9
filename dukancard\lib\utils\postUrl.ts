/**
 * Utility functions for post URL generation and handling
 */

/**
 * Generate a complete URL for a single post page
 * @param postId - The ID of the post
 * @returns Complete URL for the post
 */
export function generatePostUrl(postId: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in';
  return `${baseUrl}/post/${postId}`;
}

/**
 * Generate a relative path for a single post page
 * @param postId - The ID of the post
 * @returns Relative path for the post
 */
export function generatePostPath(postId: string): string {
  return `/post/${postId}`;
}

/**
 * Extract post ID from a post URL
 * @param url - The post URL
 * @returns Post ID if valid, null otherwise
 */
export function extractPostIdFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    
    // Expected format: /post/[postId]
    if (pathParts.length >= 3 && pathParts[1] === 'post') {
      const postId = pathParts[2];
      return postId && postId.trim() !== '' ? postId : null;
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting post ID from URL:', error);
    return null;
  }
}

/**
 * Validate if a string is a valid post ID format
 * @param postId - The post ID to validate
 * @returns True if valid, false otherwise
 */
export function isValidPostId(postId: string): boolean {
  if (!postId || typeof postId !== 'string') {
    return false;
  }
  
  // Basic validation - should be a non-empty string
  // You can add more specific validation based on your post ID format
  return postId.trim().length > 0;
}

/**
 * Generate sharing text for social media
 * @param postId - The ID of the post
 * @param authorName - Optional author name
 * @returns Formatted sharing text
 */
export function generateSharingText(postId: string, authorName?: string): string {
  const postUrl = generatePostUrl(postId);
  
  if (authorName) {
    return `Check out this post by ${authorName} on Dukancard: ${postUrl}`;
  }
  
  return `Check out this post on Dukancard: ${postUrl}`;
}
