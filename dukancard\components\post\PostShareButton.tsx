'use client';

import { useState } from 'react';
import { Share2, Check } from 'lucide-react';
import { toast } from 'sonner';
import { generatePostUrl } from '@/lib/utils/postUrl';

interface PostShareButtonProps {
  postId: string;
  className?: string;
}

/**
 * Share button component for posts with copy-to-clipboard functionality
 */
export default function PostShareButton({ postId, className = '' }: PostShareButtonProps) {
  const [copied, setCopied] = useState(false);

  const handleShare = async () => {
    try {
      const postUrl = generatePostUrl(postId);
      
      // Try to use native Web Share API if available
      if (navigator.share) {
        await navigator.share({
          title: 'Check out this post on Dukancard',
          url: postUrl,
        });
        return;
      }

      // Fallback to clipboard API
      await navigator.clipboard.writeText(postUrl);
      setCopied(true);
      toast.success('Post link copied to clipboard!');
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000);
      
    } catch (error) {
      console.error('Error sharing post:', error);
      toast.error('Failed to share post');
    }
  };

  return (
    <button
      onClick={handleShare}
      className={`
        inline-flex items-center justify-center
        px-3 py-2 
        bg-blue-600 hover:bg-blue-700 
        text-white text-sm font-medium 
        rounded-lg transition-all duration-200
        shadow-sm hover:shadow-md
        ${copied ? 'bg-green-600 hover:bg-green-700' : ''}
        ${className}
      `}
      title="Share this post"
    >
      {copied ? (
        <>
          <Check className="w-4 h-4 mr-1" />
          Copied!
        </>
      ) : (
        <>
          <Share2 className="w-4 h-4 mr-1" />
          Share
        </>
      )}
    </button>
  );
}
