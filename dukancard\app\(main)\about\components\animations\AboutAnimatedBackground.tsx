"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";

interface AboutAnimatedBackgroundProps {
  variant?: "gold" | "blue" | "purple" | "gradient";
  intensity?: "low" | "medium" | "high";
}

export default function AboutAnimatedBackground({
  variant = "gold",
  intensity = "medium",
}: AboutAnimatedBackgroundProps) {
  const [isClient, setIsClient] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Only render on client side and detect mobile
  useEffect(() => {
    setIsClient(true);
    setIsMobile(window.innerWidth < 768);

    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Define intensity values for light/dark mode
  const getIntensityValue = () => {
    switch (intensity) {
      case "low":
        return { light: 5, dark: 10 };
      case "high":
        return { light: 15, dark: 30 };
      case "medium":
      default:
        return { light: 10, dark: 20 };
    }
  };

  // Define background colors based on variant
  const getBgColors = () => {
    const intensityValue = getIntensityValue();
    
    switch (variant) {
      case "gold":
        return {
          primary: `bg-[var(--brand-gold)]/${intensityValue.light} dark:bg-[var(--brand-gold)]/${intensityValue.dark}`,
          secondary: "bg-blue-500/5 dark:bg-blue-500/10",
        };
      case "blue":
        return {
          primary: `bg-blue-500/${intensityValue.light} dark:bg-blue-500/${intensityValue.dark}`,
          secondary: "bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10",
        };
      case "purple":
        return {
          primary: `bg-purple-500/${intensityValue.light} dark:bg-purple-500/${intensityValue.dark}`,
          secondary: "bg-blue-500/5 dark:bg-blue-500/10",
        };
      case "gradient":
      default:
        return {
          primary: `bg-gradient-to-br from-[var(--brand-gold)]/${intensityValue.light} to-blue-500/${intensityValue.light} dark:from-[var(--brand-gold)]/${intensityValue.dark} dark:to-blue-500/${intensityValue.dark}`,
          secondary: "bg-transparent",
        };
    }
  };

  const { primary, secondary } = getBgColors();

  // Don't render anything on server
  if (!isClient) return null;

  return (
    <div className="absolute inset-0 overflow-hidden -z-10">
      {/* Primary blob */}
      <motion.div
        className={`absolute rounded-full blur-3xl opacity-70 ${primary}`}
        style={{
          width: isMobile ? "70%" : "50%",
          height: isMobile ? "70%" : "50%",
          top: "10%",
          right: "5%",
        }}
        animate={{
          opacity: [0.6, 0.8, 0.6],
          scale: [1, 1.05, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
        }}
      />

      {/* Secondary blob */}
      <motion.div
        className={`absolute rounded-full blur-3xl opacity-60 ${secondary}`}
        style={{
          width: isMobile ? "60%" : "40%",
          height: isMobile ? "60%" : "40%",
          bottom: "10%",
          left: "5%",
        }}
        animate={{
          opacity: [0.5, 0.7, 0.5],
          scale: [1, 1.05, 1],
        }}
        transition={{
          duration: 7,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 1,
        }}
      />

      {/* Floating particles */}
      {Array.from({ length: isMobile ? 3 : 5 }).map((_, index) => (
        <motion.div
          key={`particle-${index}`}
          className={`absolute rounded-full ${
            index % 2 === 0
              ? "bg-[var(--brand-gold)]/20 dark:bg-[var(--brand-gold)]/30"
              : "bg-blue-500/20 dark:bg-blue-500/30"
          }`}
          style={{
            width: `${Math.random() * 10 + 5}px`,
            height: `${Math.random() * 10 + 5}px`,
            left: `${Math.random() * 80 + 10}%`,
            top: `${Math.random() * 80 + 10}%`,
          }}
          animate={{
            y: [0, -20, 0],
            x: [0, 10, 0],
            opacity: [0.5, 0.8, 0.5],
          }}
          transition={{
            duration: Math.random() * 5 + 5,
            repeat: Infinity,
            repeatType: "reverse",
            delay: Math.random() * 2,
          }}
        />
      ))}
    </div>
  );
}
