// Define available card textures
export type CardTexture = {
  id: string;
  name: string;
  description: string;
  path: string;
  category: "paper" | "premium" | "modern";
  darkModeOpacity: number;
  lightModeOpacity: number;
  cssClass?: string; // Optional CSS class for static textures
};

// Array of available textures
export const cardTextures: CardTexture[] = [
  {
    id: "linen-paper",
    name: "Linen Paper",
    description: "Classic linen texture with subtle cross-hatching",
    path: "/textures/linen-paper.svg",
    category: "paper",
    darkModeOpacity: 0.3,
    lightModeOpacity: 0.2,
  },
  {
    id: "cotton-paper",
    name: "Cotton Paper",
    description: "Soft, fibrous cotton paper texture",
    path: "/textures/cotton-paper.svg",
    category: "paper",
    darkModeOpacity: 0.3,
    lightModeOpacity: 0.2,
  },
  {
    id: "recycled-paper",
    name: "Recycled Paper",
    description: "Eco-friendly recycled paper with small flecks",
    path: "/textures/recycled-paper.svg",
    category: "paper",
    darkModeOpacity: 0.3,
    lightModeOpacity: 0.2,
  },
  {
    id: "laid-paper",
    name: "Laid Paper",
    description: "Traditional laid paper with horizontal lines",
    path: "/textures/laid-paper.svg",
    category: "paper",
    darkModeOpacity: 0.3,
    lightModeOpacity: 0.2,
  },
  {
    id: "marble",
    name: "Marble",
    description: "Elegant marble texture with subtle veining",
    path: "/textures/marble.svg",
    category: "premium",
    darkModeOpacity: 0.3,
    lightModeOpacity: 0.2,
  },
  {
    id: "brushed-metal",
    name: "Brushed Metal",
    description: "Sophisticated brushed metal finish",
    path: "/textures/brushed-metal.svg",
    category: "premium",
    darkModeOpacity: 0.3,
    lightModeOpacity: 0.2,
  },
  {
    id: "subtle-dots",
    name: "Subtle Dots",
    description: "Modern pattern with subtle dot grid",
    path: "/textures/subtle-dots.svg",
    category: "modern",
    darkModeOpacity: 0.3,
    lightModeOpacity: 0.2,
  },
  {
    id: "geometric",
    name: "Geometric",
    description: "Contemporary geometric pattern",
    path: "/textures/geometric.svg",
    category: "modern",
    darkModeOpacity: 0.3,
    lightModeOpacity: 0.2,
  },
  {
    id: "texture-png",
    name: "Classic Texture",
    description: "Original texture from the application",
    path: "/texture.png",
    category: "paper",
    darkModeOpacity: 0.3,
    lightModeOpacity: 0.2,
  },
  {
    id: "none",
    name: "No Texture",
    description: "Clean look without any texture",
    path: "",
    category: "modern",
    darkModeOpacity: 0,
    lightModeOpacity: 0,
  },
];

// Function to get a texture by ID
export const getTextureById = (id: string): CardTexture => {
  return cardTextures.find((texture) => texture.id === id) || cardTextures[0];
};

// Default texture ID
export const DEFAULT_TEXTURE_ID = "linen-paper";
