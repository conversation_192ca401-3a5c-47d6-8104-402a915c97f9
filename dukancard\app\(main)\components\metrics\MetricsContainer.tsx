"use client";

import { motion } from "framer-motion";
import MetricsDisplay from "../MetricsDisplay";

interface MetricsContainerProps {
  likes: number;
  subscribers: number;
  rating: number;
  views?: number;
}

export default function MetricsContainer({
  likes,
  subscribers,
  rating,
  views,
}: MetricsContainerProps) {
  return (
    <motion.section
      className="relative w-full py-4 overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Clean container with no background */}
      <div className="relative w-full px-2 sm:px-4">
        <MetricsDisplay
          likes={likes}
          subscribers={subscribers}
          rating={rating}
          views={views}
        />
      </div>
    </motion.section>
  );
}
