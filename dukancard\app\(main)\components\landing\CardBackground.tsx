"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { useIsMobile } from "@/hooks/use-mobile";

interface CardBackgroundProps {
  className?: string;
}

export default function CardBackground({ className = "" }: CardBackgroundProps) {
  const [isClient, setIsClient] = useState(false);
  const isMobile = useIsMobile();

  // Generate floating orbs data
  const [orbs, setOrbs] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    delay: number;
    duration: number;
    color: string;
  }>>([]);

  useEffect(() => {
    setIsClient(true);

    // Generate random orbs
    const orbCount = isMobile ? 4 : 6;
    const newOrbs = Array.from({ length: orbCount }).map((_, index) => {
      return {
        id: index,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * 100 + 50,
        delay: Math.random() * 2,
        duration: Math.random() * 10 + 15,
        color: index % 2 === 0 ? "var(--brand-gold)" : "#8B5CF6"
      };
    });

    setOrbs(newOrbs);
  }, [isMobile]);

  if (!isClient) return null;

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {/* Premium product advertisement-style background */}

      {/* Main centered glow - reduced intensity */}
      <motion.div
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] bg-[var(--brand-gold)]/8 dark:bg-[var(--brand-gold)]/12 rounded-full blur-[120px]"
        initial={{ opacity: 0.3, scale: 0.9 }}
        animate={{
          opacity: [0.3, 0.4, 0.3],
          scale: [0.9, 1.05, 0.9]
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut"
        }}
      />

      {/* Secondary glow for depth and dimension - reduced intensity */}
      <motion.div
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[400px] h-[400px] bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/15 rounded-full blur-[80px]"
        initial={{ opacity: 0.2, scale: 0.8 }}
        animate={{
          opacity: [0.2, 0.3, 0.2],
          scale: [0.8, 0.95, 0.8]
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
          delay: 2
        }}
      />

      {/* Professional studio lighting effects - reduced intensity */}
      <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[70%] h-[50%] bg-white/5 dark:bg-white/3 blur-[100px] opacity-40"></div>
      <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-[70%] h-[40%] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/8 blur-[100px] opacity-40"></div>

      {/* Floating orbs - more natural and space-like */}
      {orbs.map((orb) => (
        <motion.div
          key={`orb-${orb.id}`}
          className="absolute rounded-full"
          style={{
            width: `${orb.size}px`,
            height: `${orb.size}px`,
            background: `radial-gradient(circle, ${orb.color}15 0%, transparent 80%)`,
            left: `${orb.x}%`,
            top: `${orb.y}%`,
            filter: "blur(50px)",
          }}
          initial={{ opacity: 0.1 }}
          animate={{
            x: [0, orb.id % 2 === 0 ? 20 : -20, 0],
            y: [0, orb.id % 3 === 0 ? -20 : 20, 0],
            opacity: [0.1, 0.2, 0.1],
            scale: [1, 1.05, 1]
          }}
          transition={{
            duration: orb.duration,
            delay: orb.delay,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
      ))}

      {/* Very subtle pattern overlay */}
      <div className="absolute inset-0 bg-[url('/decorative/subtle-pattern.svg')] bg-repeat opacity-3 dark:opacity-5"></div>

      {/* Premium product highlight effect - reduced intensity */}
      <motion.div
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[350px] h-[450px] bg-gradient-to-b from-white/5 via-transparent to-white/5 dark:from-white/8 dark:to-white/8 rounded-full blur-[60px]"
        animate={{
          opacity: [0.3, 0.4, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut"
        }}
      />

      {/* Subtle vignette effect for depth - reduced intensity */}
      <div className="absolute inset-0 bg-radial-gradient-to-transparent from-transparent to-black/10 dark:to-black/15 opacity-30"></div>
    </div>
  );
}
