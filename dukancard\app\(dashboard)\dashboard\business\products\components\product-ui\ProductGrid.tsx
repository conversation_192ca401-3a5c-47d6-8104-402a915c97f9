"use client";

import { motion } from "framer-motion";
import { Loader2, Edit, Trash2, Package } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Image from "next/image";
import Link from "next/link";
import { viewTransitionVariants, itemVariants } from "../../types";
import { useProducts } from "../../context/ProductsContext";
import ProductEmptyState from "./ProductEmptyState";

export default function ProductGrid() {
  const {
    products,
    isLoading,
    currentPage,
    isPending,
    deletingProductId,
    setDeletingProductId,
    getProductStatusBadge
  } = useProducts();

  if (!isLoading && products.length === 0) {
    return <ProductEmptyState view="grid" />;
  }

  return (
    <motion.div
      key="grid-view"
      variants={viewTransitionVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6"
    >
      {products.map((product, index) => (
        <motion.div
          key={product.id}
          variants={itemVariants}
          custom={index}
          className={`
            rounded-xl bg-white dark:bg-black overflow-hidden shadow-md transition-all duration-300
            ${isPending && deletingProductId === product.id ? 'opacity-50 pointer-events-none' : ''}
          `}
          whileHover={{
            scale: 1.02,
            y: -5,
            transition: { duration: 0.3 }
          }}
        >
          <div className="relative w-full overflow-hidden">
            {/* Image */}
            <div className="relative w-full aspect-square bg-neutral-100 dark:bg-neutral-800">
              {product.image_url ? (
                <Image
                  src={product.image_url}
                  alt={product.name ?? "Product image"}
                  className="object-cover w-full h-full transition-transform hover:scale-105"
                  fill
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-neutral-400 dark:text-neutral-500">
                  <Package className="w-12 h-12" />
                </div>
              )}

              {/* Featured badge - removed as it's not in the ProductServiceData type */}

              {/* Status badge */}
              <div className="absolute top-2 right-2">
                {getProductStatusBadge(product.is_available)}
              </div>

              {/* Variant count badge */}
              {product.variant_count > 0 && (
                <div className="absolute top-2 left-2">
                  <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                    {product.variant_count} variant{product.variant_count > 1 ? 's' : ''}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="absolute bottom-0 left-0 right-0 p-2 flex justify-end space-x-1 bg-gradient-to-t from-black/50 to-transparent">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link href={`/dashboard/business/products/edit/${product.id}`}>
                        <Button
                          variant="secondary"
                          size="icon"
                          disabled={isPending}
                          className="h-7 w-7 bg-white/90 hover:bg-white text-blue-600"
                        >
                          <Edit className="h-3.5 w-3.5" />
                        </Button>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent className="bg-neutral-800 text-white text-xs">
                      <p>Edit Item</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="secondary"
                        size="icon"
                        onClick={() => setDeletingProductId(product.id!)}
                        disabled={isPending || deletingProductId === product.id}
                        className="h-7 w-7 bg-white/90 hover:bg-white text-red-600"
                      >
                        {deletingProductId === product.id && isPending ? (
                          <Loader2 className="h-3.5 w-3.5 animate-spin" />
                        ) : (
                          <Trash2 className="h-3.5 w-3.5" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent className="bg-neutral-800 text-white text-xs">
                      <p>Delete Item</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            {/* Content */}
            <div className="px-3 pt-2 pb-3">
              <h3 className="font-semibold text-sm text-neutral-800 dark:text-neutral-100 truncate">
                {product.name}
              </h3>

              <p className="text-xs text-neutral-500 dark:text-neutral-400 line-clamp-2 h-8 mt-1">
                {product.description || "No description available"}
              </p>

              {/* Variant info */}
              {product.variant_count > 0 && (
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                    {product.variant_count} variant{product.variant_count > 1 ? 's' : ''}
                  </span>
                  <span className="text-xs text-neutral-400">•</span>
                  <span className="text-xs text-green-600 dark:text-green-400">
                    {product.available_variant_count} available
                  </span>
                </div>
              )}

              <div className="flex justify-between items-center gap-2 mt-2">
                <div className="flex items-baseline space-x-2">
                  <span className="font-semibold text-sm text-neutral-800 dark:text-neutral-100">
                    {product.discounted_price?.toLocaleString("en-IN", {
                      style: "currency",
                      currency: "INR",
                    }) ?? "-"}
                  </span>

                  {product.discounted_price && product.base_price && product.discounted_price < product.base_price && (
                    <span className="text-xs text-neutral-500 dark:text-neutral-400 line-through">
                      {product.base_price.toLocaleString("en-IN", {
                        style: "currency",
                        currency: "INR",
                      })}
                    </span>
                  )}
                </div>

                <div className="font-normal text-xs bg-neutral-100 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 px-2 py-0.5 rounded-full">
                  {product.product_type}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      ))}

      {isLoading && currentPage > 1 && (
        <div className="col-span-full flex justify-center py-4">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
        </div>
      )}
    </motion.div>
  );
}
