"use client";

import { ReactNode, ButtonHTMLAttributes, useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useTheme } from "next-themes";
import { ArrowRight } from "lucide-react";

interface EnhancedGlowButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  className?: string;
  variant?: "default" | "outline" | "primary";
  size?: "default" | "sm" | "lg";
  showArrow?: boolean;
  roundedFull?: boolean;
}

export default function EnhancedGlowButton({
  children,
  className = "",
  variant = "primary",
  size = "default",
  showArrow = false,
  roundedFull = false,
  ...props
}: EnhancedGlowButtonProps) {
  const { resolvedTheme } = useTheme();
  const [isHovered, setIsHovered] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Use useEffect to detect client-side rendering
  useEffect(() => {
    setMounted(true);
  }, []);

  // Default to light theme for server rendering
  const isDark = mounted ? resolvedTheme === "dark" : false;

  // Only render animations on the client to avoid hydration mismatch
  const AnimatedGlow = () => {
    if (!mounted) return null;

    return (
      <motion.div
        className={cn(
          "absolute -inset-0.5",
          roundedFull ? "rounded-full" : "rounded-md",
          "bg-gradient-to-r from-[var(--brand-gold)]/40 to-[var(--brand-gold)]/60",
          isDark ? "blur-sm" : "blur-[2px]",
          "max-w-full w-auto"
        )}
        initial={{ opacity: 0.5 }}
        animate={{
          opacity: [0.5, 0.7, 0.5],
          boxShadow: isDark
            ? ["0 0 10px 1px rgba(var(--brand-gold-rgb), 0.2)", "0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)", "0 0 10px 1px rgba(var(--brand-gold-rgb), 0.2)"]
            : ["0 0 5px 1px rgba(var(--brand-gold-rgb), 0.1)", "0 0 10px 2px rgba(var(--brand-gold-rgb), 0.2)", "0 0 5px 1px rgba(var(--brand-gold-rgb), 0.1)"]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          repeatType: "loop",
          ease: "easeInOut"
        }}
      />
    );
  };

  // Use a consistent class string for both server and client rendering
  const staticGlowClasses = cn(
    "absolute -inset-0.5",
    roundedFull ? "rounded-full" : "rounded-md",
    "bg-gradient-to-r from-[var(--brand-gold)]/40 to-[var(--brand-gold)]/60",
    "blur-[2px]", // Use a consistent blur for server rendering
    "max-w-full w-auto"
  );

  return (
    <div className="relative group inline-flex max-w-full w-auto">
      {/* Static glow effect with consistent styling for server and client */}
      <div
        className={staticGlowClasses}
        style={{ opacity: 0.5 }}
      />

      {/* Animated glow effect only on client */}
      {mounted && <AnimatedGlow />}

      {/* Button rendering */}
      <div className="relative max-w-full w-full sm:w-auto">
        <Button
          variant={variant === "primary" ? "default" : variant}
          size={size}
          className={cn(
            "relative z-10 font-medium overflow-hidden max-w-full w-full sm:w-auto",
            roundedFull ? "rounded-full" : "rounded-md",
            variant === "primary" && "bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black dark:text-neutral-900",
            variant === "outline" && "border border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/5",
            className
          )}
          onMouseEnter={() => mounted && setIsHovered(true)}
          onMouseLeave={() => mounted && setIsHovered(false)}
          {...props}
        >
          {/* Contained shimmer effect */}
          <span className="absolute inset-0 w-full h-full overflow-hidden">
            <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out pointer-events-none" />
          </span>

          {/* Button content with arrow */}
          <div className="flex items-center justify-center gap-2 relative z-10">
            <span>{children}</span>
            {showArrow && (
              <div style={{ transform: mounted && isHovered ? 'translateX(5px)' : 'translateX(0px)', transition: 'transform 0.3s' }}>
                <ArrowRight className="w-4 h-4" />
              </div>
            )}
          </div>
        </Button>
      </div>
    </div>
  );
}
