"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { UseFormReturn } from "react-hook-form";
import { BusinessCardData } from "../../schema";
import { getPincodeDetails } from "@/lib/actions/location";

interface UsePincodeDetailsOptions {
  form: UseFormReturn<BusinessCardData>;
  initialPincode?: string;
  initialLocality?: string;
}

export function usePincodeDetails({
  form,
  initialPincode,
  initialLocality
}: UsePincodeDetailsOptions) {
  const [isPincodeLoading, setIsPincodeLoading] = useState(false);
  const [availableLocalities, setAvailableLocalities] = useState<string[]>([]);

  // Pincode change handler
  const handlePincodeChange = useCallback(async (pincode: string) => {
    if (pincode.length !== 6) return;

    setIsPincodeLoading(true);
    setAvailableLocalities([]);

    // Reset form fields
    form.setValue("locality", "");
    form.setValue("city", "");
    form.setValue("state", "");

    const result = await getPincodeDetails(pincode);
    setIsPincodeLoading(false);

    if (result.error) {
      toast.error(result.error);
    } else if (result.city && result.state && result.localities) {
      // Set city and state
      form.setValue("city", result.city, { shouldValidate: true });
      form.setValue("state", result.state, { shouldValidate: true });

      // Update localities
      setAvailableLocalities(result.localities);

      // If only one locality, auto-select it
      if (result.localities.length === 1) {
        form.setValue("locality", result.localities[0], {
          shouldValidate: true,
          shouldDirty: false // Don't mark as dirty on auto-fill
        });
      }

      toast.success("City and State auto-filled. Please select your locality.");
    }
  }, [form]);

  // Effect to fetch localities on initial load if pincode exists
  useEffect(() => {
    if (!initialPincode || initialPincode.length !== 6) return;

    const fetchAndValidateLocalities = async (pincode: string) => {
      setIsPincodeLoading(true);
      setAvailableLocalities([]);

      try {
        const result = await getPincodeDetails(pincode);

        if (result.error) {
          toast.error(`Failed to fetch details for pincode ${pincode}: ${result.error}`);
          setAvailableLocalities([]);
        } else if (result.city && result.state && result.localities) {
          // Set city/state
          form.setValue("city", result.city, { shouldValidate: true });
          form.setValue("state", result.state, { shouldValidate: true });
          setAvailableLocalities(result.localities);

          if (initialLocality) {
            const savedLocalityLower = initialLocality.trim().toLowerCase();
            const isValid = result.localities.some(
              (loc) => loc.trim().toLowerCase() === savedLocalityLower
            );

            if (!isValid) {
              // Saved locality is invalid for this pincode
              toast.warning(
                `Saved locality "${initialLocality}" is not valid for pincode ${pincode}. Please re-select.`
              );
              form.setValue("locality", "", {
                shouldValidate: true,
                shouldDirty: false // Don't mark as dirty on validation correction
              });
            }
          } else if (result.localities.length === 1) {
            // If no locality saved, but only one option, pre-select it
            form.setValue("locality", result.localities[0], {
              shouldValidate: true,
              shouldDirty: false // Don't mark as dirty on auto-fill
            });
          }
        } else {
          setAvailableLocalities([]);
          toast.warning(`No localities found for pincode ${pincode}.`);
          if (initialLocality) {
            form.setValue("locality", "", {
              shouldValidate: true,
              shouldDirty: false // Don't mark as dirty on validation correction
            });
          }
        }
      } catch (error) {
        console.error("Error fetching pincode details:", error);
        toast.error("An unexpected error occurred while fetching pincode details.");
        setAvailableLocalities([]);
        if (initialLocality) {
          form.setValue("locality", "", {
            shouldValidate: true,
            shouldDirty: false // Don't mark as dirty on validation correction
          });
        }
      } finally {
        setIsPincodeLoading(false);
      }
    };

    fetchAndValidateLocalities(initialPincode);
  }, [initialPincode, initialLocality, form]);

  return {
    isPincodeLoading,
    availableLocalities,
    handlePincodeChange
  };
}
