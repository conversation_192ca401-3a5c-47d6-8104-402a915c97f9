"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowRight, CreditCard, Globe, ShieldCheck,
  Store, Users, Smartphone, Bell, BarChart3, Palette
} from "lucide-react";
import { useRouter } from "next/navigation";
import SectionBackground from "../SectionBackground";
import FeatureCard from "./FeatureCard";
import MobileFeatureCarousel from "./MobileFeatureCarousel";

// Enhanced feature data with additional details
const features = [
  {
    icon: CreditCard,
    title: "Digital Business Cards",
    description:
      "Create stunning digital cards that showcase your business information, services, and brand identity.",
    details:
      "Your digital business card includes customizable sections for contact information, business hours, location with map integration, social media links, and a professional bio. Upload your logo, cover image, and profile photo to create a cohesive brand experience.",
  },
  {
    icon: Store,
    title: "Digital Storefront",
    description:
      "Set up a mini digital storefront to display your products and services with pricing and details.",
    details:
      "Showcase up to 50 products or services with detailed descriptions, high-quality images, pricing information, and availability status. Organize items into categories for easy browsing and highlight featured offerings to attract customer attention.",
  },
  {
    icon: Globe,
    title: "Web Presence",
    description:
      "Get a dedicated URL for your digital card that you can share across platforms and print materials.",
    details:
      "Your business receives a custom URL (yourbusiness.dukancard.in) that's easy to remember and share. Include it on business cards, brochures, social media profiles, and email signatures to drive traffic to your digital presence.",
  },
  {
    icon: Users,
    title: "Customer Engagement",
    description:
      "Connect with customers through integrated messaging and feedback collection tools.",
    details:
      "Enable direct messaging so customers can inquire about products or services. Collect feedback through customizable forms, and build a subscriber list to send updates about new offerings, promotions, or business news.",
  },
  {
    icon: Smartphone,
    title: "Mobile Optimized",
    description:
      "Ensure your business looks great on any device with fully responsive design and mobile-first approach.",
    details:
      "Our platform automatically optimizes your digital card for viewing on smartphones, tablets, and desktop computers. Fast loading times and touch-friendly interfaces ensure a seamless experience for customers regardless of how they access your business information.",
  },
  {
    icon: Bell,
    title: "Notification System",
    description:
      "Keep customers informed about updates, new products, or business announcements through push notifications.",
    details:
      "Send timely updates to subscribers when you add new products, change business hours, or make important announcements. Schedule notifications in advance or send them instantly to maximize engagement and drive repeat business.",
  },
  {
    icon: ShieldCheck,
    title: "Secure Platform",
    description:
      "Keep your business information secure with our enterprise-grade security protocols.",
    details:
      "We implement industry-leading security measures including end-to-end encryption, secure authentication, regular security audits, and compliance with data protection regulations to ensure your business and customer information remains protected.",
  },
  {
    icon: BarChart3,
    title: "Analytics Dashboard",
    description:
      "Track engagement, views, and customer interactions with comprehensive analytics.",
    details:
      "Gain valuable insights into customer behavior with detailed analytics on page views, click-through rates, popular products, geographic distribution of visitors, and engagement metrics. Use this data to refine your offerings and marketing strategies.",
  },
  {
    icon: Palette,
    title: "Customizable Design",
    description:
      "Personalize your digital card with custom colors, fonts, and layouts to match your brand.",
    details:
      "Choose from multiple design templates and then customize colors, typography, button styles, and layout options to create a unique digital presence that aligns perfectly with your existing brand identity and stands out from competitors.",
  },
];

export default function FeaturesSection() {
  const router = useRouter();
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile devices
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  return (
    <section className="py-10 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto relative">
      {/* Simple background */}
      <div className="absolute inset-0 -z-10">
        <SectionBackground variant="gold" intensity="low" />
      </div>

      <div className="text-center mb-12 md:mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Everything You Need for a{" "}
          <span className="text-[var(--brand-gold)]">Digital Presence</span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Dukancard provides powerful tools to help businesses of all sizes
          establish a strong digital footprint.
        </p>
      </div>

      {/* Mobile Feature Carousel - only visible on mobile */}
      {isMobile && (
        <div className="md:hidden mb-8">
          <MobileFeatureCarousel features={features} />
        </div>
      )}

      {/* Desktop/Tablet Grid Layout - hidden on mobile */}
      <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
        {features.map((feature, index) => (
          <FeatureCard
            key={index}
            icon={feature.icon}
            title={feature.title}
            description={feature.description}
            index={index}
          />
        ))}
      </div>

      <div className="flex items-center justify-center mt-12 md:mt-16">
        <Button
          className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] cursor-pointer px-8 py-6 rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-300 relative z-10"
          onClick={() => router.push("/features")}
        >
          <span>View All Features</span>
          <ArrowRight className="w-5 h-5 ml-2" />
        </Button>
      </div>
    </section>
  );
}
