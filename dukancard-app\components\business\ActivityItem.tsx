import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { Heart, Users, Star, User } from 'lucide-react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { ActivityData } from '@/lib/services/activityService';
import { formatDistanceToNow } from 'date-fns';

interface ActivityItemProps {
  activity: ActivityData;
  onPress?: () => void;
  onView?: (activityId: string) => void;
}

export const ActivityItem: React.FC<ActivityItemProps> = ({ activity, onPress, onView }) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Call onView when component mounts (comes into view)
  React.useEffect(() => {
    if (onView) {
      onView(activity.id);
    }
  }, [activity.id, onView]);

  // Get activity icon and color based on type
  const getActivityIcon = () => {
    switch (activity.activity_type) {
      case 'like':
        return { Icon: Heart, color: '#ef4444' }; // red
      case 'subscribe':
        return { Icon: Users, color: '#3b82f6' }; // blue
      case 'rating':
        return { Icon: Star, color: '#D4AF37' }; // gold
      case 'visit':
        return { Icon: User, color: '#8b5cf6' }; // purple
      default:
        return { Icon: User, color: '#6b7280' }; // gray
    }
  };

  // Ensure user_profile exists
  const userProfile = activity.user_profile || {};

  // Determine if the user is a business or customer
  const isBusiness = userProfile.is_business || false;

  // Get the appropriate name and avatar
  // For business users: name field contains business_name, avatar_url contains logo_url
  // For customer users: name field contains customer name, avatar_url contains avatar
  const displayName = userProfile.name;
  const avatarUrl = userProfile.avatar_url;

  // Get activity message based on type (matching Next.js format)
  const getActivityMessage = () => {
    const userName = displayName || 'Someone';

    switch (activity.activity_type) {
      case 'like':
        return `${userName} liked your business`;
      case 'subscribe':
        return `${userName} subscribed to your business`;
      case 'rating':
        return `${userName} rated your business ${activity.rating_value}/5`;
      case 'visit':
        return `${userName} visited your business card`;
      default:
        return `${userName} interacted with your business`;
    }
  };

  // Get user avatar or initials (matching Next.js approach)
  const getUserAvatar = () => {
    if (avatarUrl) {
      return (
        <Image
          source={{ uri: avatarUrl }}
          style={[
            styles.avatar,
            {
              borderColor: isBusiness ? '#D4AF37' : (isDark ? '#374151' : '#e5e7eb'),
              borderWidth: isBusiness ? 2 : 1
            }
          ]}
        />
      );
    }

    // Fallback to initials
    const initials = displayName
      ?.split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2) || '?';

    return (
      <View style={[
        styles.avatarFallback,
        {
          backgroundColor: isBusiness
            ? (isDark ? '#D4AF37' + '30' : '#D4AF37' + '20')
            : (isDark ? '#374151' : '#f3f4f6'),
          borderColor: isBusiness ? '#D4AF37' : (isDark ? '#4b5563' : '#d1d5db'),
          borderWidth: isBusiness ? 2 : 1
        }
      ]}>
        <Text style={[
          styles.avatarInitials,
          {
            color: isBusiness
              ? '#D4AF37'
              : (isDark ? '#f3f4f6' : '#374151')
          }
        ]}>
          {initials}
        </Text>
      </View>
    );
  };

  // Check if user can navigate (business users only, matching Next.js approach)
  const canNavigate = isBusiness && userProfile.business_slug;

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (error) {
      return 'Recently';
    }
  };

  const { Icon, color } = getActivityIcon();

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: isDark ? '#111827' : '#ffffff',
          borderColor: activity.is_read 
            ? (isDark ? '#374151' : '#e5e7eb')
            : '#D4AF37', // Gold border for unread
          borderWidth: activity.is_read ? 1 : 2,
        }
      ]}
      onPress={canNavigate ? onPress : undefined}
      disabled={!canNavigate}
      activeOpacity={canNavigate ? 0.7 : 1}
    >
      {/* Unread indicator dot */}
      {!activity.is_read && (
        <View style={styles.unreadDot} />
      )}

      {/* User Avatar */}
      <View style={styles.avatarContainer}>
        {getUserAvatar()}
      </View>

      {/* Activity Content */}
      <View style={styles.content}>
        <View style={styles.messageContainer}>
          <Text style={[
            styles.message,
            { 
              color: isDark ? '#f3f4f6' : '#374151',
              fontWeight: activity.is_read ? '400' : '600'
            }
          ]}>
            {getActivityMessage()}
          </Text>
          
          {/* Activity Type Icon */}
          <View style={[styles.activityIcon, { backgroundColor: `${color}20` }]}>
            <Icon size={16} color={color} />
          </View>
        </View>

        {/* Timestamp */}
        <Text style={[
          styles.timestamp,
          { color: isDark ? '#9ca3af' : '#6b7280' }
        ]}>
          {formatTimestamp(activity.created_at)}
        </Text>

        {/* Business navigation indicator */}
        {canNavigate && (
          <Text style={[
            styles.navigationHint,
            { color: '#D4AF37' }
          ]}>
            Tap to view business card
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    position: 'relative',
  },
  unreadDot: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ef4444',
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 2,
  },
  avatarFallback: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarInitials: {
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  message: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
    marginRight: 8,
  },
  activityIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timestamp: {
    fontSize: 12,
    marginBottom: 2,
  },
  navigationHint: {
    fontSize: 11,
    fontStyle: 'italic',
  },
});
