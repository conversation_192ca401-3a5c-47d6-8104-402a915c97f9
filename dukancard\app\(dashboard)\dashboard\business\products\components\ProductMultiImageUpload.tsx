"use client";

import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FormDescription } from "@/components/ui/form";
import {
  // UploadCloud,
  Plus,
  Star,
  StarIcon,
  ImageIcon,
  Trash2,
} from "lucide-react";
import { ProductImage } from "./hooks/useProductMultiImageUpload";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";
import { toast } from "sonner";

interface ProductMultiImageUploadProps {
  images: ProductImage[];
  featuredImageIndex: number;
  disabled?: boolean;
  maxImages?: number;
  onAddImage: () => void;
  onRemoveImage: (_index: number) => void;
  onSetFeatured: (_index: number) => void;
  onFileSelect: (_file: File, _index: number) => void;
  errorDisplay?: string | null;
}

export default function ProductMultiImageUpload({
  images,
  featuredImageIndex,
  disabled = false,
  maxImages = 5,
  onAddImage,
  onRemoveImage,
  onSetFeatured,
  onFileSelect,
  errorDisplay,
}: ProductMultiImageUploadProps) {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const file = event.target.files?.[0];
    if (file) {
      onFileSelect(file, index);
    }
    event.target.value = "";
  };

  // Handle clicking on the entire card to trigger file upload
  const handleCardClick = (index: number) => {
    if (disabled) return;
    const fileInput = document.getElementById(`product-image-${index}`) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  };

  // Drag and drop handlers
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>, index?: number) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
    if (typeof index === 'number') {
      setDragOverIndex(index);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // Only set dragging to false if we're leaving the entire component
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragging(false);
      setDragOverIndex(null);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, index?: number) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) {
      setIsDragging(true);
    }
    if (typeof index === 'number') {
      setDragOverIndex(index);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    setDragOverIndex(null);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        toast.error("Please drop a valid image file (JPG, PNG, WebP, or GIF).");
        return;
      }

      // Validate file size (15MB)
      if (file.size > 15 * 1024 * 1024) {
        toast.error("Image too large", {
          description: "Please select an image smaller than 15MB"
        });
        return;
      }

      onFileSelect(file, index);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      transition: {
        duration: 0.2
      }
    }
  };

  const buttonVariants = {
    rest: { scale: 1 },
    hover: { scale: 1.1 },
    tap: { scale: 0.95 }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <div className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2 flex justify-between items-center">
          <span>Product Images ({images.length}/{maxImages})</span>
          <div className="flex gap-2">
            {images.length < maxImages && (
              <motion.div
                initial="rest"
                whileHover="hover"
                whileTap="tap"
                variants={buttonVariants}
              >
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={onAddImage}
                  disabled={disabled || images.length >= maxImages}
                  className="h-8 px-2 text-xs"
                >
                  <Plus className="h-3.5 w-3.5 mr-1" />
                  Add Image
                </Button>
              </motion.div>
            )}
          </div>
        </div>

        {/* Unified Grid Layout for All Images */}
        <motion.div
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 md:gap-4"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <AnimatePresence mode="popLayout">
            {/* Map all images including featured */}
            {images.map((image, index) => {
              const isFeatured = index === featuredImageIndex;
              const isHovered = hoveredIndex === index;
              const isDraggedOver = dragOverIndex === index;

              return (
                <motion.div
                  key={image.id}
                  layout
                  variants={itemVariants}
                  exit="exit"
                  onHoverStart={() => setHoveredIndex(index)}
                  onHoverEnd={() => setHoveredIndex(null)}
                  onDragEnter={(e) => handleDragEnter(e, index)}
                  onDragLeave={handleDragLeave}
                  onDragOver={(e) => handleDragOver(e, index)}
                  onDrop={(e) => handleDrop(e, index)}
                  onClick={() => handleCardClick(index)}
                  className={cn(
                    "relative border rounded-lg overflow-hidden group transition-all duration-200 cursor-pointer",
                    isFeatured
                      ? "border-amber-500 dark:border-amber-400 border-2 col-span-2 sm:col-span-1 md:col-span-1 order-first"
                      : "border-neutral-200 dark:border-neutral-700",
                    isDraggedOver && "border-amber-500 dark:border-amber-400 border-2 bg-amber-50 dark:bg-amber-950/20",
                    !disabled && "hover:border-amber-400 dark:hover:border-amber-500"
                  )}
                >
                  {/* Image Preview */}
                  <div className={cn(
                    "aspect-square bg-neutral-100 dark:bg-neutral-800 relative transition-all duration-200",
                    isDraggedOver && "bg-amber-50 dark:bg-amber-950/20"
                  )}>
                    {image.previewUrl ? (
                      <div className="w-full h-full">
                        <Image
                          src={image.previewUrl}
                          alt={`Product image ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-full h-full flex flex-col items-center justify-center text-neutral-400 dark:text-neutral-500 p-2">
                        <motion.div
                          animate={{
                            rotate: isHovered ? 10 : 0,
                            scale: isDraggedOver ? 1.1 : 1
                          }}
                          transition={{ duration: 0.3 }}
                        >
                          <ImageIcon className={cn(
                            "mb-1 transition-colors duration-200",
                            isFeatured ? "h-10 w-10" : "h-8 w-8",
                            isDraggedOver && "text-amber-500"
                          )} />
                        </motion.div>
                        <span className={cn(
                          "text-center transition-colors duration-200",
                          isFeatured ? "text-sm" : "text-xs",
                          isDraggedOver && "text-amber-500"
                        )}>
                          {isDraggedOver
                            ? "Drop image here"
                            : isFeatured
                              ? "Featured Image"
                              : `Add image ${index + 1}`
                          }
                        </span>
                        {!isDraggedOver && (
                          <span className={cn(
                            "text-center mt-1 opacity-60",
                            isFeatured ? "text-xs" : "text-[10px]"
                          )}>
                            Click or drag & drop
                          </span>
                        )}
                      </div>
                    )}

                    {/* Featured Badge - Always visible for featured image */}
                    {isFeatured && (
                      <motion.div
                        className="absolute top-2 left-2 bg-amber-500 text-white rounded-full p-1.5"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{
                          type: "spring",
                          stiffness: 500,
                          damping: 15
                        }}
                      >
                        <StarIcon className="h-4 w-4" />
                      </motion.div>
                    )}

                    {/* Delete Button - Top Right - Show for all cards except the only remaining card */}
                    {(image.previewUrl || images.length > 1) && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{
                          opacity: isHovered ? 1 : 0,
                          scale: isHovered ? 1 : 0.8
                        }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-2 right-2 z-10 md:opacity-0 md:group-hover:opacity-100"
                      >
                        <motion.div
                          variants={buttonVariants}
                          initial="rest"
                          whileHover="hover"
                          whileTap="tap"
                        >
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              onRemoveImage(index);
                            }}
                            disabled={disabled}
                            className={cn(
                              "rounded-full",
                              isFeatured ? "h-7 w-7" : "h-6 w-6"
                            )}
                          >
                            <Trash2 className={isFeatured ? "h-3.5 w-3.5" : "h-3 w-3"} />
                          </Button>
                        </motion.div>
                      </motion.div>
                    )}

                    {/* Set as Featured Button - Top Left (only for non-featured images) */}
                    {!isFeatured && image.previewUrl && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{
                          opacity: isHovered ? 1 : 0,
                          scale: isHovered ? 1 : 0.8
                        }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-2 left-2 z-10 md:opacity-0 md:group-hover:opacity-100"
                      >
                        <motion.div
                          variants={buttonVariants}
                          initial="rest"
                          whileHover="hover"
                          whileTap="tap"
                        >
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              onSetFeatured(index);
                            }}
                            disabled={disabled}
                            className="h-6 w-6 rounded-full bg-amber-500 text-white hover:bg-amber-600 border-none"
                          >
                            <Star className="h-3 w-3" />
                          </Button>
                        </motion.div>
                      </motion.div>
                    )}

                    {/* Hidden file input for the entire card */}
                    <Input
                      id={`product-image-${index}`}
                      type="file"
                      accept="image/jpeg,image/png,image/webp,image/gif"
                      onChange={(e) => handleFileChange(e, index)}
                      className="hidden"
                      disabled={disabled}
                    />
                  </div>
                </motion.div>
              );
            })}

            {/* Add Image Button - Only show if we have room for more images and already have at least one image */}
            {images.length < maxImages && images.some(img => img.previewUrl) && (
              <motion.button
                key="add-button"
                variants={itemVariants}
                whileHover={{
                  scale: 1.05,
                  borderColor: "var(--amber-500)"
                }}
                whileTap={{ scale: 0.95 }}
                type="button"
                onClick={onAddImage}
                disabled={disabled}
                className={cn(
                  "aspect-square border-2 border-dashed rounded-lg flex flex-col items-center justify-center transition-colors",
                  "border-neutral-200 dark:border-neutral-700 text-neutral-400 dark:text-neutral-500",
                  "hover:border-amber-500 dark:hover:border-amber-500"
                )}
              >
                <motion.div
                  animate={{ y: [0, -5, 0] }}
                  transition={{
                    repeat: Infinity,
                    repeatType: "loop",
                    duration: 2,
                    repeatDelay: 1
                  }}
                >
                  <Plus className="h-8 w-8 mb-1" />
                </motion.div>
                <span className="text-xs">Add Image</span>
              </motion.button>
            )}
          </AnimatePresence>
        </motion.div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400">
          Upload up to {maxImages} images (JPG, PNG, WebP, or GIF, max 15MB each). Click on any image card or drag & drop files to upload.
          Images will be automatically cropped to square (1:1) aspect ratio. Select one image as featured to display in listings.
        </FormDescription>

        {errorDisplay && (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-xs text-red-500 dark:text-red-400 mt-1"
          >
            {errorDisplay}
          </motion.p>
        )}
      </motion.div>
    </div>
  );
}
