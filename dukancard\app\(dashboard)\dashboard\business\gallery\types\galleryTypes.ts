import { GalleryImage } from "../types";

export interface GalleryPageClientProps {
  initialImages: GalleryImage[];
  userPlan: string;
  businessName: string;
}

export interface DragState {
  isDragging: boolean;
  activeId: string | null;
}

export interface ReorderState {
  orderedImages: GalleryImage[];
  hasUnsavedChanges: boolean;
  isSavingOrder: boolean;
  isReordering: boolean;
}

export interface UploadState {
  isUploading: boolean;
  selectedFile: File | null;
  previewUrl: string | null;
  uploadDialogOpen: boolean;
}

export interface DeleteState {
  isDeleting: boolean;
  selectedImage: GalleryImage | null;
  deleteDialogOpen: boolean;
}

export interface LightboxState {
  lightboxImage: string | null;
}
