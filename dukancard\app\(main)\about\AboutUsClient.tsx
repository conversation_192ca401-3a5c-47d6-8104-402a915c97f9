"use client";

import { useRef, useState, useEffect } from "react";
import { motion } from "framer-motion";
import SectionDivider from "../components/landing/SectionDivider";

// Import our new components
import AboutHeroSection from "./components/AboutHeroSection";
import MissionVisionSection from "./components/MissionVisionSection";
import StorySection from "./components/StorySection";
import CoreValuesSection from "./components/CoreValuesSection";
import MilestonesSection from "./components/MilestonesSection";
import AboutCTASection from "./components/AboutCTASection";

export default function AboutPageClient() {
  const [isLoaded, setIsLoaded] = useState(false);
  const pageRef = useRef<HTMLDivElement>(null);

  // Handle initial animation on page load
  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div
      ref={pageRef}
      className="min-h-screen bg-white dark:bg-black text-black dark:text-white overflow-hidden"
    >
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoaded ? 1 : 0 }}
        transition={{ duration: 0.5 }}
        className="w-full pt-16"
      >
        {/* Hero Section */}
        <AboutHeroSection />

        {/* Section Divider */}
        <SectionDivider variant="gold" />

        {/* Mission & Vision Section */}
        <MissionVisionSection />

        {/* Section Divider */}
        <SectionDivider variant="blue" />

        {/* Story Section */}
        <StorySection />

        {/* Section Divider */}
        <SectionDivider variant="purple" />

        {/* Core Values Section */}
        <CoreValuesSection />

        {/* Section Divider */}
        <SectionDivider variant="gold" />

        {/* Milestones Section */}
        <MilestonesSection />

        {/* Section Divider */}
        <SectionDivider variant="blue" />

        {/* CTA Section */}
        <AboutCTASection />
      </motion.div>
    </div>
  );
}
