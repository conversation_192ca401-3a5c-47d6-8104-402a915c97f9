"use client";

import React, { createContext, useContext, useState, useTransition, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { debounce } from "lodash";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle } from "lucide-react";
import {
  ProductFilters,
  ProductSortBy,
  getProductServices,
  deleteProductService
} from "../actions";
import { ProductsContextType, ITEMS_PER_PAGE } from "../types";
import { ProductWithVariantInfo } from "@/types/products";

// Create the context
const ProductsContext = createContext<ProductsContextType | undefined>(undefined);

// Props for the provider
interface ProductsProviderProps {
  children: React.ReactNode;
  initialData: ProductWithVariantInfo[];
  initialCount: number;
  planLimit: number;
  initialError?: string;
}

// Provider component
export function ProductsProvider({
  children,
  initialData,
  initialCount,
  planLimit,
  initialError,
}: ProductsProviderProps) {
  // State management
  const [products, setProducts] = useState<ProductWithVariantInfo[]>(initialData);
  const [totalCount, setTotalCount] = useState<number>(initialCount);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isPending, startTransition] = useTransition();
  const [viewType, setViewType] = useState<"table" | "grid">("table");

  // Filter & Sort State
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [filterAvailable, setFilterAvailable] = useState<boolean | undefined>(undefined);
  const [sortBy, setSortBy] = useState<ProductSortBy>("created_desc");
  const [_isLoadingFilters, setIsLoadingFilters] = useState(true);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Modal State
  const [deletingProductId, setDeletingProductId] = useState<string | null>(null);

  // Derived state
  const canAddMore = planLimit === Infinity || totalCount < planLimit;
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  // Initial fetch error
  useEffect(() => {
    if (initialError) {
      toast.error(`Failed to load initial products: ${initialError}`);
    }
  }, [initialError]);

  // --- Data Fetching ---
  const debouncedSetSearch = debounce((value: string) => {
    setDebouncedSearchTerm(value);
  }, 500);

  useEffect(() => {
    debouncedSetSearch(searchTerm);
    return () => debouncedSetSearch.cancel();
  }, [searchTerm, debouncedSetSearch]);

  // Fetch products based on current state
  const fetchProducts = useCallback(
    async (page: number, replace: boolean = false) => {
      setIsLoading(true);
      const currentFilters: ProductFilters = {
        searchTerm: debouncedSearchTerm || undefined,
        filterAvailable,
      };

      try {
        const result = await getProductServices(
          page,
          ITEMS_PER_PAGE,
          currentFilters,
          sortBy
        );

        if (result.data) {
          setProducts((prev) =>
            replace ? result.data! : [...prev, ...result.data!]
          );
          setCurrentPage(page);
          if (result.count !== undefined) {
            setTotalCount(result.count);
          }
        } else if (result.error) {
          toast.error(`Failed to load products: ${result.error}`);
        }
      } catch (error) {
        toast.error("An unexpected error occurred while loading products.");
        console.error("Error fetching products:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [debouncedSearchTerm, filterAvailable, sortBy]
  );

  // Effect to refetch data when filters or sort change
  useEffect(() => {
    setCurrentPage(1);
    fetchProducts(1, true);
  }, [debouncedSearchTerm, filterAvailable, sortBy, fetchProducts]);

  // Effect to initialize data
  useEffect(() => {
    const initializeData = async () => {
      setIsLoadingFilters(true);

      try {
        setSortBy("created_desc");
      } catch (error) {
        console.error("Error during initialization:", error);
      } finally {
        setIsLoadingFilters(false);
        setIsInitialLoading(false);
      }
    };

    initializeData();
  }, []);

  // --- Handlers ---
  const loadMoreProducts = () => {
    if (isLoading || currentPage >= totalPages) return;
    fetchProducts(currentPage + 1, false);
  };

  // These functions are no longer needed as we're using dedicated pages
  const handleAddNew = () => {
    // This is kept for backward compatibility but no longer used
    return;
  };

  const handleDeleteConfirm = async () => {
    if (!deletingProductId) return;

    startTransition(async () => {
      try {
        const result = await deleteProductService(deletingProductId);
        if (result.success) {
          toast.success("Item deleted successfully.");
          setProducts((prev) => prev.filter((p) => p.id !== deletingProductId));
          setTotalCount((prev) => prev - 1);
        } else {
          toast.error(result.error || "Failed to delete item.");
        }
      } catch (error) {
        toast.error("An unexpected error occurred during deletion.");
        console.error("Delete operation failed:", error);
      } finally {
        setDeletingProductId(null);
      }
    });
  };

  // This function is no longer needed as we're using dedicated pages for add/edit
  const handleSave = async () => {
    // This is kept for backward compatibility but no longer used
    return;
  };

  // Helper to generate filter status text
  const getFilterStatusText = () => {
    const activeFilters = [];
    if (debouncedSearchTerm)
      activeFilters.push(`Search: "${debouncedSearchTerm}"`);
    if (filterAvailable !== undefined)
      activeFilters.push(filterAvailable ? "Available" : "Unavailable");
    return activeFilters.length > 0
      ? ` | ${activeFilters.join(", ")}`
      : "";
  };

  // Get product status badge
  const getProductStatusBadge = (isAvailable: boolean) => {
    return isAvailable ? (
      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        Available
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800">
        <XCircle className="w-3 h-3 mr-1" />
        Unavailable
      </Badge>
    );
  };

  // Context value
  const contextValue: ProductsContextType = {
    // Data
    products,
    totalCount,
    currentPage,
    totalPages,
    planLimit,
    canAddMore,

    // UI State
    viewType,
    setViewType,
    isLoading,
    isPending,
    isInitialLoading,

    // Filter State
    searchTerm,
    setSearchTerm,
    filterAvailable,
    setFilterAvailable,
    sortBy,
    setSortBy,

    // Modal State
    deletingProductId,
    setDeletingProductId,

    // Actions
    loadMoreProducts,
    handleAddNew,
    handleDeleteConfirm,
    handleSave,
    getFilterStatusText,
    getProductStatusBadge,
  };

  return (
    <ProductsContext.Provider value={contextValue}>
      {children}
    </ProductsContext.Provider>
  );
}

// Custom hook to use the context
export function useProducts() {
  const context = useContext(ProductsContext);
  if (context === undefined) {
    throw new Error("useProducts must be used within a ProductsProvider");
  }
  return context;
}
