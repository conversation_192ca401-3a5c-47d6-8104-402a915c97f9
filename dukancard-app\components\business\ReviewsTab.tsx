import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal } from 'react-native';
import { Star, SortAsc } from 'lucide-react-native';
import { BusinessReview, BusinessCardData, ReviewSortOption } from '../../lib/services/businessCardDataService';
import { LoadingSpinner } from '../shared/ui/LoadingSpinner';
import { formatIndianNumberShort } from '@/lib/utils';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface ReviewsTabProps {
  reviews: BusinessReview[];
  reviewStats: BusinessCardData['reviewStats'] | null;
  isDark: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  sortBy: ReviewSortOption;
  onLoadMore: () => void;
  onSort: (sortBy: ReviewSortOption) => void;
}

export default function ReviewsTab({
  reviews,
  reviewStats,
  isDark,
  loadingMore,
  hasMore,
  sortBy,
  onLoadMore,
  onSort,
}: ReviewsTabProps) {
  const styles = createPublicCardViewStyles(isDark);
  const [showSortModal, setShowSortModal] = useState(false);

  // Handle sort selection
  const handleSortSelect = (newSortBy: ReviewSortOption) => {
    onSort(newSortBy);
    setShowSortModal(false);
  };

  // Get sort label
  const getSortLabel = (sort: ReviewSortOption) => {
    switch (sort) {
      case 'newest': return 'Newest First';
      case 'oldest': return 'Oldest First';
      case 'highest_rating': return 'Highest Rating';
      case 'lowest_rating': return 'Lowest Rating';
      default: return 'Newest First';
    }
  };

  return (
    <View style={styles.section}>
      {reviewStats && (
        <View style={styles.reviewStatsContainer}>
          <View style={styles.ratingOverview}>
            <Text style={styles.averageRating}>{reviewStats.averageRating.toFixed(1)}</Text>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  size={16}
                  color={star <= reviewStats.averageRating ? '#f39c12' : '#ddd'}
                  fill={star <= reviewStats.averageRating ? '#f39c12' : 'transparent'}
                />
              ))}
            </View>
            <Text style={styles.totalReviews}>({formatIndianNumberShort(reviewStats.totalReviews)} reviews)</Text>
          </View>
        </View>
      )}

      {/* Sort Button */}
      {reviews && reviews.length > 0 && (
        <View style={styles.sortContainer}>
          <TouchableOpacity
            style={styles.sortButton}
            onPress={() => setShowSortModal(true)}
          >
            <SortAsc color={isDark ? '#D4AF37' : '#D4AF37'} size={20} />
            <Text style={[styles.sortButtonText, { color: isDark ? '#FFFFFF' : '#000000' }]}>
              Sort by: {getSortLabel(sortBy)}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {reviews && reviews.length > 0 ? (
        <View>
          {/* Reviews List */}
          {reviews.map((item) => (
            <View key={item.id} style={styles.reviewCard}>
              <View style={styles.reviewHeader}>
                <View style={styles.reviewerInfo}>
                  <View style={styles.reviewerAvatar}>
                    <Text style={styles.reviewerInitial}>
                      {item.customer_name?.charAt(0).toUpperCase() || 'U'}
                    </Text>
                  </View>
                  <View>
                    <Text style={styles.reviewerName}>
                      {item.customer_name || 'Anonymous'}
                    </Text>
                    <View style={styles.reviewStars}>
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          size={12}
                          color={star <= item.rating ? '#f39c12' : '#ddd'}
                          fill={star <= item.rating ? '#f39c12' : 'transparent'}
                        />
                      ))}
                    </View>
                  </View>
                </View>
                <Text style={styles.reviewDate}>
                  {new Date(item.created_at).toLocaleDateString()}
                </Text>
              </View>
              {item.review_text && (
                <Text style={styles.reviewText}>{item.review_text}</Text>
              )}
            </View>
          ))}

          {/* Loading indicator for infinite scroll */}
          {loadingMore && hasMore && (
            <View style={styles.loadMoreContainer}>
              <LoadingSpinner size="small" />
            </View>
          )}

          {/* Invisible trigger for infinite scroll */}
          {hasMore && !loadingMore && (
            <View
              style={styles.infiniteScrollTrigger}
              onLayout={() => {
                // Trigger load more when this view becomes visible
                setTimeout(() => {
                  onLoadMore();
                }, 100);
              }}
            />
          )}
        </View>
      ) : (
        <Text style={styles.emptyText}>No reviews available</Text>
      )}

      {/* Sort Modal */}
      <Modal
        visible={showSortModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowSortModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowSortModal(false)}
        >
          <View style={styles.sortModal}>
            <Text style={styles.sortModalTitle}>Sort Reviews</Text>

            {/* Date Section */}
            <View style={styles.sortSection}>
              <Text style={styles.sortSectionTitle}>Date</Text>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'newest' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('newest')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'newest' && styles.sortOptionTextSelected]}>
                  Newest First
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'oldest' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('oldest')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'oldest' && styles.sortOptionTextSelected]}>
                  Oldest First
                </Text>
              </TouchableOpacity>
            </View>

            {/* Rating Section */}
            <View style={styles.sortSection}>
              <Text style={styles.sortSectionTitle}>Rating</Text>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'highest_rating' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('highest_rating')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'highest_rating' && styles.sortOptionTextSelected]}>
                  Highest Rating
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'lowest_rating' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('lowest_rating')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'lowest_rating' && styles.sortOptionTextSelected]}>
                  Lowest Rating
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}
