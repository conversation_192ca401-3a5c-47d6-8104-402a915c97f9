"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Store } from "lucide-react";
import { useRouter } from "next/navigation";
import SectionBackground from "../SectionBackground";

export default function CTASection() {
  const router = useRouter();

  return (
    <section className="py-10 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto relative">
      {/* Simple background */}
      <div className="absolute inset-0 -z-10">
        <SectionBackground variant="gradient" intensity="medium" />
      </div>

      <div className="bg-white/90 dark:bg-black/60 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 rounded-2xl p-8 md:p-12 relative overflow-hidden shadow-xl">
        {/* Static background gradients */}
        <div className="absolute -right-20 -top-20 w-64 h-64 bg-gradient-to-br from-[var(--brand-gold)]/20 to-amber-500/10 dark:from-[var(--brand-gold)]/30 dark:to-amber-500/20 rounded-full blur-3xl"></div>
        <div className="absolute -left-20 -bottom-20 w-64 h-64 bg-gradient-to-br from-blue-500/10 to-purple-500/10 dark:from-blue-500/20 dark:to-purple-500/20 rounded-full blur-3xl"></div>

        <div className="relative z-10 max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-5xl font-bold text-foreground mb-6 relative">
            Ready to{" "}
            <span className="text-[var(--brand-gold)] relative inline-block">
              Elevate
              {/* Static underline */}
              <div className="absolute -bottom-1 left-0 h-1 w-full bg-gradient-to-r from-[var(--brand-gold)]/30 via-[var(--brand-gold)] to-[var(--brand-gold)]/30 rounded-full"></div>
            </span>{" "}
            Your Digital Presence?
          </h2>

          {/* Paragraph */}
          <div className="relative">
            <p className="text-base sm:text-lg text-neutral-600 dark:text-neutral-300 mb-8 sm:mb-10 relative z-10 max-w-2xl mx-auto">
              Join thousands of businesses that have transformed their digital
              identity with Dukancard. Get started today and receive a 30-day
              premium trial.
            </p>
          </div>

          {/* Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center mb-6 sm:mb-8">
            <div className="w-full sm:w-auto relative group">
              {/* Button glow effect */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/60 to-[var(--brand-gold)]/80 rounded-full opacity-70 blur-md group-hover:opacity-100 transition-opacity duration-300"></div>

              <Button
                size="lg"
                className="cursor-pointer bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] hover:from-[var(--brand-gold-light)] hover:to-[var(--brand-gold)] text-[var(--brand-gold-foreground)] px-6 sm:px-8 py-6 rounded-full font-semibold flex gap-2 items-center shadow-md hover:shadow-lg transition-all duration-300 w-full sm:w-auto relative z-10"
                onClick={() => router.push("/login")}
              >
                <span>Create Your Card</span>
                <ArrowRight className="w-5 h-5" />
              </Button>
            </div>

            <div className="w-full sm:w-auto relative group">
              {/* Button subtle glow effect */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-full opacity-0 blur-md group-hover:opacity-70 transition-opacity duration-300"></div>

              <Button
                size="lg"
                variant="outline"
                className="cursor-pointer border-2 border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 px-6 sm:px-8 py-6 rounded-full font-semibold flex gap-2 items-center w-full sm:w-auto backdrop-blur-sm relative z-10"
                onClick={() => router.push("/discover")}
              >
                <span>Discover Stores</span>
                <Store className="w-5 h-5" />
              </Button>
            </div>
          </div>

          <div className="text-neutral-500 dark:text-neutral-400 text-sm relative inline-block">
            <div className="relative">
              <span>Start building your digital presence in minutes.</span>
              {/* Static underline */}
              <div className="absolute bottom-0 left-0 right-0 h-px w-full bg-gradient-to-r from-transparent via-[var(--brand-gold)]/30 to-transparent"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
