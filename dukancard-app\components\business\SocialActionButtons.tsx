import React from 'react';
import { View, Text, TouchableOpacity, Linking } from 'react-native';
import { Instagram, Facebook } from 'lucide-react-native';
import { BusinessDiscoveryData } from '../../lib/services/businessDiscovery';
import { Toast } from '../../lib/utils/toast';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface SocialActionButtonsProps {
  businessData: BusinessDiscoveryData;
  isDark: boolean;
}

export default function SocialActionButtons({ businessData, isDark }: SocialActionButtonsProps) {
  const styles = createPublicCardViewStyles(isDark);

  const handleInstagramPress = () => {
    if (businessData.instagram_url) {
      Linking.openURL(businessData.instagram_url).catch(() => {
        Toast.show('Unable to open Instagram', 'error');
      });
    }
  };

  const handleFacebookPress = () => {
    if (businessData.facebook_url) {
      Linking.openURL(businessData.facebook_url).catch(() => {
        Toast.show('Unable to open Facebook', 'error');
      });
    }
  };

  const hasAnyButton = businessData.instagram_url || businessData.facebook_url;

  if (!hasAnyButton) {
    return null;
  }

  return (
    <View style={styles.actionButtons}>
      {businessData.instagram_url && (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#E4405F' }]}
          onPress={handleInstagramPress}
        >
          <Instagram color="#fff" size={20} />
          <Text style={styles.actionButtonText}>Instagram</Text>
        </TouchableOpacity>
      )}

      {businessData.facebook_url && (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#1877F2' }]}
          onPress={handleFacebookPress}
        >
          <Facebook color="#fff" size={20} />
          <Text style={styles.actionButtonText}>Facebook</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}
