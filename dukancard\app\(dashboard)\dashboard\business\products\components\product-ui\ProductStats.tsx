"use client";

import { ShoppingBag, CheckCircle, XCircle } from "lucide-react";
import { CardContent } from "@/components/ui/card";
import { useProducts } from "../../context/ProductsContext";

export default function ProductStats() {
  const { products, totalCount, planLimit } = useProducts();

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-5 relative z-10">
      <div className="overflow-hidden rounded-xl shadow-md bg-white dark:bg-neutral-900 border border-yellow-200 dark:border-yellow-800/50">
        <CardContent className="p-2 sm:p-3 md:p-4 lg:p-5">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5 sm:space-y-1">
              <p className="text-xs sm:text-sm font-medium text-yellow-600 dark:text-yellow-400">Total Products</p>
              <div className="relative">
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-neutral-800 dark:text-neutral-100">
                  {totalCount}
                </h3>
                <div
                  className="absolute inset-0 blur-sm opacity-50 z-0"
                  style={{ backgroundColor: "rgba(234, 179, 8, 0.3)" }}
                />
              </div>
            </div>
            <div className="p-2 sm:p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/50">
              <ShoppingBag className="h-4 sm:h-5 md:h-6 w-4 sm:w-5 md:w-6 text-primary" />
            </div>
          </div>
          <div className="mt-3 sm:mt-4">
            <div className="flex justify-between items-center text-xs sm:text-sm mb-1 text-neutral-600 dark:text-neutral-400">
              <span>
                {planLimit === Infinity
                  ? `${totalCount} of Unlimited`
                  : `${totalCount} of ${planLimit}`}
              </span>
              <span>
                {planLimit === Infinity
                  ? "Unlimited"
                  : `${Math.round((totalCount / planLimit) * 100)}%`}
              </span>
            </div>
            <div className="w-full bg-yellow-200 dark:bg-yellow-800 rounded-full h-1.5">
              <div
                className="h-1.5 rounded-full bg-yellow-600 dark:bg-yellow-400 transition-all duration-300"
                style={{
                  width: planLimit === Infinity
                    ? "10%" // Show a small fixed percentage for unlimited plans
                    : `${Math.min(100, Math.round((totalCount / planLimit) * 100))}%`
                }}
              ></div>
            </div>
          </div>
        </CardContent>
      </div>

      <div className="overflow-hidden rounded-xl shadow-md bg-white dark:bg-neutral-900 border border-green-200 dark:border-green-800/50">
        <CardContent className="p-2 sm:p-3 md:p-4 lg:p-5">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5 sm:space-y-1">
              <p className="text-xs sm:text-sm font-medium text-green-600 dark:text-green-400">Available Products</p>
              <div className="relative">
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-neutral-800 dark:text-neutral-100">
                  {products.filter(p => p.is_available).length}
                </h3>
                <div
                  className="absolute inset-0 blur-sm opacity-50 z-0"
                  style={{ backgroundColor: "rgba(34, 197, 94, 0.3)" }}
                />
              </div>
            </div>
            <div className="p-2 sm:p-3 rounded-full bg-green-100 dark:bg-green-900/50">
              <CheckCircle className="h-4 sm:h-5 md:h-6 w-4 sm:w-5 md:w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <p className="text-xs sm:text-sm text-green-600/80 dark:text-green-400/80 mt-3 sm:mt-4">
            {products.filter(p => p.is_available).length > 0
              ? `${Math.round((products.filter(p => p.is_available).length / products.length) * 100)}% of products active`
              : "No active products"}
          </p>
        </CardContent>
      </div>

      <div className="overflow-hidden rounded-xl shadow-md bg-white dark:bg-neutral-900 border border-red-200 dark:border-red-800/50">
        <CardContent className="p-2 sm:p-3 md:p-4 lg:p-5">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5 sm:space-y-1">
              <p className="text-xs sm:text-sm font-medium text-red-600 dark:text-red-400">Unavailable Products</p>
              <div className="relative">
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-neutral-800 dark:text-neutral-100">
                  {products.filter(p => !p.is_available).length}
                </h3>
                <div
                  className="absolute inset-0 blur-sm opacity-50 z-0"
                  style={{ backgroundColor: "rgba(239, 68, 68, 0.3)" }}
                />
              </div>
            </div>
            <div className="p-2 sm:p-3 rounded-full bg-red-100 dark:bg-red-900/50">
              <XCircle className="h-4 sm:h-5 md:h-6 w-4 sm:w-5 md:w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
          <p className="text-xs sm:text-sm text-red-600/80 dark:text-red-400/80 mt-3 sm:mt-4">
            {products.filter(p => !p.is_available).length > 0
              ? `${Math.round((products.filter(p => !p.is_available).length / products.length) * 100)}% of products inactive`
              : "All products are active"}
          </p>
        </CardContent>
      </div>
    </div>
  );
}
