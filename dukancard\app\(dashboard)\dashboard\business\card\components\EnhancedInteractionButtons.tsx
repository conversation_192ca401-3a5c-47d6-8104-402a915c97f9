"use client";

import { useState, useEffect } from "react";
import { useInView } from "react-intersection-observer";
import { toast } from "sonner";
import {
  Heart,
  UserPlus,
  UserMinus,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

// Glow effect component using simple styling
const GlowEffect = ({ color }: { color: string }) => {
  return (
    <div
      className="absolute inset-0 rounded-full blur-md pointer-events-none opacity-50"
      style={{ backgroundColor: color }}
    />
  );
};

// Pulse effect component using simple styling
const PulseEffect = ({ color }: { color: string }) => {
  return (
    <div
      className="absolute inset-0 rounded-full pointer-events-none"
      style={{
        boxShadow: `0 0 0 2px ${color}`,
      }}
    />
  );
};

interface EnhancedInteractionButtonsProps {
  hasLiked: boolean;
  isSubscribed: boolean;
  isLoadingInteraction: boolean;
  onLike: () => void;
  onUnlike: () => void;
  onSubscribe: () => void;
  onUnsubscribe: () => void;
  isAuthenticated: boolean;
  isOwnBusiness?: boolean;
  isCurrentUserBusiness?: boolean;
  themeColor?: string;
}

export default function EnhancedInteractionButtons({
  hasLiked,
  isSubscribed,
  isLoadingInteraction,
  onLike,
  onUnlike,
  onSubscribe,
  onUnsubscribe,
  isAuthenticated,
  isOwnBusiness = false,
  // Removed unused parameter: isCurrentUserBusiness
}: EnhancedInteractionButtonsProps) {
  const [prevLiked, setPrevLiked] = useState(hasLiked);
  const [prevSubscribed, setPrevSubscribed] = useState(isSubscribed);

  const { ref } = useInView({
    triggerOnce: true,
    threshold: 0.5,
  });

  // Detect changes in liked/subscribed state
  useEffect(() => {
    if (hasLiked !== prevLiked) {
      setPrevLiked(hasLiked);
    }
  }, [hasLiked, prevLiked]);

  useEffect(() => {
    if (isSubscribed !== prevSubscribed) {
      setPrevSubscribed(isSubscribed);
    }
  }, [isSubscribed, prevSubscribed]);

  const handleLikeClick = () => {
    if (isOwnBusiness) {
      toast.error("You cannot like your own business card", {
        description: "Business owners cannot like their own cards",
        position: "top-center",
      });
      return;
    }

    if (!isAuthenticated) {
      window.location.href = `/login?message=Please log in to like this business card&redirect=${encodeURIComponent(
        window.location.pathname.substring(1)
      )}`;
      return;
    }

    if (hasLiked) {
      onUnlike();
    } else {
      onLike();
    }
  };

  const handleSubscribeClick = () => {
    if (isOwnBusiness) {
      toast.error("You cannot subscribe to your own business card", {
        description: "Business owners cannot subscribe to their own cards",
        position: "top-center",
      });
      return;
    }



    if (!isAuthenticated) {
      window.location.href = `/login?message=Please log in to subscribe to this business card&redirect=${encodeURIComponent(
        window.location.pathname.substring(1)
      )}`;
      return;
    }

    if (isSubscribed) {
      onUnsubscribe();
    } else {
      onSubscribe();
    }
  };

  return (
    <div
      ref={ref}
      className="absolute top-16 right-4 flex flex-col space-y-3 z-20"
    >
      {/* Like Button */}
      <div className="relative">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className={cn(
                  "h-10 w-10 rounded-full border-2 bg-white/95 dark:bg-neutral-800/95 backdrop-blur-sm shadow-lg relative overflow-hidden transition-all duration-300",
                  hasLiked
                    ? "text-red-500 border-red-500 hover:border-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                    : "text-[--theme-color] border-[--theme-color-50] hover:border-[--theme-color] hover:bg-[--theme-color-5]"
                )}
                onClick={handleLikeClick}
                disabled={isLoadingInteraction}
                aria-label={hasLiked ? "Unlike" : "Like"}
              >
                {isLoadingInteraction ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <Heart
                    className={cn("h-5 w-5", hasLiked && "fill-current")}
                  />
                )}

                {/* Background effects */}
                {hasLiked && <GlowEffect color="rgba(239, 68, 68, 0.5)" />}
                {hasLiked && <PulseEffect color="rgba(239, 68, 68, 0.7)" />}
              </Button>
            </TooltipTrigger>
            {(!isAuthenticated || isOwnBusiness) && (
              <TooltipContent
                side="right"
                className="bg-white dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 text-xs p-2 shadow-md border border-neutral-200 dark:border-neutral-700"
              >
                {isOwnBusiness ? (
                  <div className="flex flex-col gap-1">
                    <p className="font-medium text-red-500 dark:text-red-400">
                      You cannot like your own business card
                    </p>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400">
                      Business owners cannot like their own cards
                    </p>
                  </div>
                ) : (
                  <p>Please log in to like this business card</p>
                )}
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Subscribe Button */}
      <div className="relative">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className={cn(
                  "h-10 w-10 rounded-full border-2 bg-white/95 dark:bg-neutral-800/95 backdrop-blur-sm shadow-lg relative overflow-hidden transition-all duration-300",
                  isSubscribed
                    ? "text-blue-500 border-blue-500 hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                    : "text-[--theme-color] border-[--theme-color-50] hover:border-[--theme-color] hover:bg-[--theme-color-5]"
                )}
                onClick={handleSubscribeClick}
                disabled={isLoadingInteraction}
                aria-label={isSubscribed ? "Unsubscribe" : "Subscribe"}
              >
                {isLoadingInteraction ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  isSubscribed ? (
                    <UserMinus className="h-5 w-5" />
                  ) : (
                    <UserPlus className="h-5 w-5" />
                  )
                )}

                {/* Background effects */}
                {isSubscribed && <GlowEffect color="rgba(59, 130, 246, 0.5)" />}
                {isSubscribed && <PulseEffect color="rgba(59, 130, 246, 0.7)" />}
              </Button>
            </TooltipTrigger>
            {(!isAuthenticated || isOwnBusiness) && (
              <TooltipContent
                side="right"
                className="bg-white dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 text-xs p-2 shadow-md border border-neutral-200 dark:border-neutral-700"
              >
                {isOwnBusiness ? (
                  <div className="flex flex-col gap-1">
                    <p className="font-medium text-red-500 dark:text-red-400">
                      You cannot subscribe to your own business card
                    </p>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400">
                      Business owners cannot subscribe to their own cards
                    </p>
                  </div>
                ) : (
                  <p>Please log in to subscribe to this business card</p>
                )}
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
}
