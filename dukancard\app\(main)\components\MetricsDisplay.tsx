"use client";

import { useEffect, useState, useRef, useCallback } from "react";
import { motion, useAnimation } from "framer-motion";
import { Heart, Users, Star, Eye } from "lucide-react";
import { formatIndianNumberShort } from "@/lib/utils";

interface MetricsDisplayProps {
  likes: number;
  subscribers: number;
  rating: number;
  views?: number;
}

export default function MetricsDisplay({
  likes,
  subscribers,
  rating,
  views = 125000,
}: MetricsDisplayProps) {
  // State for animated counters
  const [animatedLikes, setAnimatedLikes] = useState(0);
  const [animatedSubscribers, setAnimatedSubscribers] = useState(0);
  const [animatedViews, setAnimatedViews] = useState(0);
  const [animatedRating, setAnimatedRating] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);

  // Intersection Observer setup
  const containerRef = useRef<HTMLDivElement>(null);
  const controls = useAnimation();

  // Format large numbers with Indian number system (lakhs, crores, etc.)
  const formatNumber = (num: number) => {
    return formatIndianNumberShort(num);
  };

  // Function to animate counters - wrapped in useCallback to prevent recreation on each render
  const animateCounters = useCallback(() => {
    const duration = 2000; // 2 seconds
    const framesPerSecond = 60;
    const totalFrames = (duration / 1000) * framesPerSecond;

    let frame = 0;
    const timer = setInterval(() => {
      const progress = frame / totalFrames;
      const easeOutProgress = 1 - Math.pow(1 - progress, 3); // Cubic ease out

      setAnimatedLikes(Math.floor(likes * easeOutProgress));
      setAnimatedSubscribers(Math.floor(subscribers * easeOutProgress));
      setAnimatedViews(Math.floor(views * easeOutProgress));
      setAnimatedRating(parseFloat((rating * easeOutProgress).toFixed(1)));

      frame++;
      if (frame > totalFrames) {
        clearInterval(timer);
        setAnimatedLikes(likes);
        setAnimatedSubscribers(subscribers);
        setAnimatedViews(views);
        setAnimatedRating(rating);
      }
    }, 1000 / framesPerSecond);

    return timer;
  }, [likes, subscribers, views, rating]);

  // Setup intersection observer to detect when component is fully in view
  useEffect(() => {
    if (!containerRef.current) return;

    const currentRef = containerRef.current; // Store ref in a variable for cleanup

    const observer = new IntersectionObserver(
      ([entry]) => {
        // Only trigger animation when element is 100% in view
        if (entry.isIntersecting && entry.intersectionRatio === 1) {
          controls.start("visible");

          if (!hasAnimated) {
            animateCounters();
            setHasAnimated(true);
          }
        }
      },
      { threshold: 1.0 } // 1.0 means 100% of the element must be in view
    );

    observer.observe(currentRef);

    return () => {
      observer.unobserve(currentRef);
    };
  }, [controls, hasAnimated, animateCounters]);

  return (
    <div
      ref={containerRef}
      className="grid grid-cols-2 sm:grid-cols-4 gap-3 w-full max-w-4xl mx-auto py-6 px-4"
    >
      <MetricCard
        icon={
          <Heart className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]" />
        }
        label="Likes"
        value={formatNumber(animatedLikes)}
        controls={controls}
        delay={0}
      />

      <MetricCard
        icon={
          <Users className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]" />
        }
        label="Subscribers"
        value={formatNumber(animatedSubscribers)}
        controls={controls}
        delay={0.1}
      />

      <MetricCard
        icon={
          <Star className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]" />
        }
        label="Rating"
        value={animatedRating.toFixed(1)}
        controls={controls}
        delay={0.2}
      />

      <MetricCard
        icon={
          <Eye className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-gold)]" />
        }
        label="Views"
        value={formatNumber(animatedViews)}
        controls={controls}
        delay={0.3}
      />
    </div>
  );
}

interface MetricCardProps {
  icon: React.ReactNode;
  label: string;
  value: string;
  controls: ReturnType<typeof useAnimation>;
  delay: number;
}

function MetricCard({ icon, label, value, controls, delay }: MetricCardProps) {
  return (
    <motion.div
      className="rounded-lg p-2 sm:p-3 md:p-4 bg-white/90 dark:bg-black/40 backdrop-blur-sm border border-[var(--brand-gold)]/20 flex flex-col items-center justify-center text-center shadow-sm"
      variants={{
        hidden: { opacity: 0, y: 10 },
        visible: {
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.3,
            delay: delay,
          },
        },
      }}
      initial="hidden"
      animate={controls}
      whileHover={{
        scale: 1.05,
        boxShadow:
          "0 0 15px rgba(var(--brand-gold-rgb), 0.2), 0 0 5px rgba(var(--brand-gold-rgb), 0.1)",
        transition: { duration: 0.2 },
      }}
    >
      <div
        className="mb-1 sm:mb-2 text-[var(--brand-gold)] opacity-90"
        style={{
          filter:
            "drop-shadow(0 0 2px rgba(var(--brand-gold-rgb), 0.3)) drop-shadow(0 0 1px rgba(var(--brand-gold-rgb), 0.2))",
        }}
      >
        {icon}
      </div>
      <div className="text-base sm:text-xl md:text-2xl font-bold text-[var(--brand-gold)] mb-1 glow-text dark:glow-text-stronger">
        {value}
      </div>
      <div className="text-[10px] sm:text-xs text-neutral-600 dark:text-neutral-400">
        {label}
      </div>
    </motion.div>
  );
}
