"use client";

import { useEffect, useState } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { motion } from "framer-motion";

interface PlanBackgroundProps {
  isPremium?: boolean;
}

export default function PlanBackground({ isPremium = false }: PlanBackgroundProps) {
  const [isClient, setIsClient] = useState(false);
  const isMobile = useIsMobile();
  const [circuitNodes, setCircuitNodes] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    delay: number;
    duration: number;
  }>>([]);

  // Reduce the number of particles on mobile for better performance
  const particleCount = isClient && isMobile ? 10 : 20;

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Generate the random elements only on the client side
  useEffect(() => {
    if (!isClient) return;

    // Generate random circuit nodes
    const nodes = Array.from({ length: isMobile ? 4 : 8 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: isMobile ? 1 + Math.random() * 1.5 : 1.5 + Math.random() * 2.5,
      delay: Math.random() * 2,
      duration: 2 + Math.random() * 3,
    }));

    setCircuitNodes(nodes);
  }, [isClient, isMobile]);

  // Define primary and secondary colors based on plan type
  const primaryColor = isPremium ? "rgb(59, 130, 246)" : "rgb(59, 130, 246)";
  const secondaryColor = isPremium ? "rgb(99, 102, 241)" : "rgb(99, 102, 241)";
  const primaryRgb = isPremium ? "59, 130, 246" : "59, 130, 246";
  const secondaryRgb = isPremium ? "99, 102, 241" : "99, 102, 241";

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none z-0">
      {/* Gradient background */}
      {isClient && (
        <motion.div
          className="absolute inset-0 opacity-20 dark:opacity-15"
          style={{
            background: `radial-gradient(circle at 50% 50%,
              ${primaryColor} 0%,
              rgba(${primaryRgb}, 0.3) 25%,
              rgba(${primaryRgb}, 0.1) 50%,
              rgba(${secondaryRgb}, 0.1) 75%,
              rgba(${secondaryRgb}, 0.05) 100%)`,
            filter: isMobile ? "blur(60px)" : "blur(80px)",
            transformOrigin: "center",
          }}
          animate={{ opacity: 0.2 }}
          whileInView={{ opacity: 0.25 }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
      )}

      {/* Flowing particles */}
      {isClient && (
        <div className="absolute inset-0">
          {Array.from({ length: particleCount }).map((_, index) => {
            const width = isMobile ? Math.random() * 2 + 1 : Math.random() * 3 + 1;
            const height = isMobile ? Math.random() * 2 + 1 : Math.random() * 3 + 1;
            const left = Math.random() * 100;
            const top = Math.random() * 100;
            const duration = Math.random() * 10 + 10;
            const delay = Math.random() * 5;

            // Random movement coordinates for animation
            const xOffset = Math.random() * 50 - 25;
            const yOffset = Math.random() * 50 - 25;

            return (
              <motion.div
                key={`particle-${index}`}
                className="absolute rounded-full"
                style={{
                  width: `${width}px`,
                  height: `${height}px`,
                  left: `${left}%`,
                  top: `${top}%`,
                  background: index % 2 === 0 ? primaryColor : secondaryColor,
                }}
                initial={{ x: 0, y: 0, opacity: 0.7 }}
                animate={{ x: xOffset, y: yOffset, opacity: 1 }}
                transition={{
                  duration: duration,
                  delay: delay,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
              />
            );
          })}
        </div>
      )}

      {/* Circuit nodes */}
      {isClient && (
        <svg
          className="absolute inset-0 w-full h-full"
          xmlns="http://www.w3.org/2000/svg"
        >
          <defs>
            <filter id="plan-glow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur stdDeviation="2" result="blur" />
              <feComposite
                in="SourceGraphic"
                in2="blur"
                operator="over"
                result="glow"
              />
            </filter>
          </defs>

          {circuitNodes.map((node) => (
            <motion.circle
              key={`node-${node.id}`}
              cx={`${node.x}%`}
              cy={`${node.y}%`}
              r={node.size}
              fill={node.id % 2 === 0 ? primaryColor : secondaryColor}
              filter="url(#plan-glow)"
              initial={{ opacity: 0.5, r: node.size }}
              animate={{ opacity: 1, r: node.size * 1.2 }}
              transition={{
                duration: node.duration,
                delay: node.delay,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut"
              }}
            />
          ))}
        </svg>
      )}
    </div>
  );
}
