"use client";

import { But<PERSON> } from "@/components/ui/button";

interface BillingToggleProps {
  billingCycle: "monthly" | "yearly";
  setBillingCycle: (_cycle: "monthly" | "yearly") => void;
}

export default function BillingToggle({
  billingCycle,
  setBillingCycle,
}: BillingToggleProps) {
  return (
    <div className="relative inline-flex">
      {/* Background glow effect */}
      <div className="absolute -inset-1 bg-gradient-to-r from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/30 rounded-full blur-md" />

      <div className="space-x-2 bg-white/80 dark:bg-black/50 backdrop-blur-sm p-1.5 rounded-full border border-neutral-200/50 dark:border-neutral-800/50 inline-flex shadow-md relative z-10">
        <Button
          onClick={() => setBillingCycle("monthly")}
          variant={billingCycle === "monthly" ? "default" : "ghost"}
          size="sm"
          className={`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
            billingCycle === "monthly"
              ? "bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md"
              : "text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"
          }`}
        >
          <span className={billingCycle === "monthly" ? "font-medium" : "font-normal"}>
            Monthly
          </span>
        </Button>
        <Button
          onClick={() => setBillingCycle("yearly")}
          variant={billingCycle === "yearly" ? "default" : "ghost"}
          size="sm"
          className={`cursor-pointer px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 flex items-center gap-2 ${
            billingCycle === "yearly"
              ? "bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] shadow-md"
              : "text-neutral-600 dark:text-neutral-400 hover:text-foreground hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50"
          }`}
        >
          <span className={billingCycle === "yearly" ? "font-medium" : "font-normal"}>
            Yearly
          </span>
          <span className={`text-xs px-1.5 py-0.5 rounded-full ${billingCycle === "yearly" ? "bg-white/90 text-[var(--brand-gold)]" : "bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400"}`}>
            Save 20%
          </span>
        </Button>
      </div>
    </div>
  );
}
