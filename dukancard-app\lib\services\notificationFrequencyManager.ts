import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabaseAdmin } from '@/lib/utils/supabaseAdmin';

interface NotificationFrequencySettings {
  dailyLimit: number;
  minInterval: number; // milliseconds
  batchDelay: number; // milliseconds
  quietHours: {
    start: number; // hour (0-23)
    end: number; // hour (0-23)
  };
  enableBatching: boolean;
  enableIntelligentTiming: boolean;
}

interface UserEngagementData {
  userId: string;
  peakHours: number[]; // hours when user is most active
  timezone: string;
  lastActiveTime: number;
  avgSessionDuration: number;
  preferredNotificationTypes: string[];
}

interface NotificationBatch {
  userId: string;
  notifications: Array<{
    type: string;
    data: any;
    scheduledTime: number;
  }>;
  batchedAt: number;
}

class NotificationFrequencyManager {
  private static instance: NotificationFrequencyManager;
  private readonly DEFAULT_SETTINGS: NotificationFrequencySettings = {
    dailyLimit: 5, // Max 5 notifications per day (following best practices)
    minInterval: 30 * 60 * 1000, // 30 minutes between similar notifications
    batchDelay: 5 * 60 * 1000, // 5 minutes to batch similar notifications
    quietHours: {
      start: 22, // 10 PM
      end: 8,    // 8 AM
    },
    enableBatching: true,
    enableIntelligentTiming: true,
  };

  static getInstance(): NotificationFrequencyManager {
    if (!NotificationFrequencyManager.instance) {
      NotificationFrequencyManager.instance = new NotificationFrequencyManager();
    }
    return NotificationFrequencyManager.instance;
  }

  /**
   * Check if a notification should be sent based on frequency controls
   */
  async shouldSendNotification(
    userId: string,
    notificationType: string,
    priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium'
  ): Promise<boolean> {
    try {
      const settings = await this.getUserSettings(userId);
      const now = Date.now();

      // Always allow urgent notifications
      if (priority === 'urgent') {
        return true;
      }

      // Check quiet hours
      if (this.isQuietHours(now, settings.quietHours)) {
        return priority === 'high'; // Only high priority during quiet hours
      }

      // Check daily limit
      const dailyCount = await this.getDailyNotificationCount(userId);
      if (dailyCount >= settings.dailyLimit) {
        return priority === 'high'; // Only high priority if over daily limit
      }

      // Check minimum interval for same type
      const lastSent = await this.getLastNotificationTime(userId, notificationType);
      if (lastSent && (now - lastSent) < settings.minInterval) {
        return false;
      }

      // Check user engagement patterns for intelligent timing
      if (settings.enableIntelligentTiming) {
        const isOptimalTime = await this.isOptimalSendTime(userId, now);
        if (!isOptimalTime && priority === 'low') {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error checking notification frequency:', error);
      return true; // Default to allowing notification
    }
  }

  /**
   * Record a sent notification for frequency tracking
   */
  async recordSentNotification(
    userId: string,
    notificationType: string,
    priority: string = 'medium'
  ): Promise<void> {
    try {
      const now = Date.now();
      const today = new Date().toDateString();
      
      // Update daily count
      const key = `notification_count_${userId}_${today}`;
      const currentCount = await AsyncStorage.getItem(key);
      const newCount = currentCount ? parseInt(currentCount) + 1 : 1;
      await AsyncStorage.setItem(key, newCount.toString());

      // Update last sent time for type
      const typeKey = `last_notification_${userId}_${notificationType}`;
      await AsyncStorage.setItem(typeKey, now.toString());

      // Store in database for analytics
      await supabaseAdmin
        .from('notification_analytics')
        .insert({
          user_id: userId,
          notification_type: notificationType,
          priority,
          sent_at: new Date().toISOString(),
          date: today,
        })
        .select()
        .single();

    } catch (error) {
      console.error('Error recording sent notification:', error);
    }
  }

  /**
   * Get user-specific notification settings
   */
  private async getUserSettings(userId: string): Promise<NotificationFrequencySettings> {
    try {
      const key = `notification_settings_${userId}`;
      const stored = await AsyncStorage.getItem(key);
      
      if (stored) {
        const settings = JSON.parse(stored);
        return { ...this.DEFAULT_SETTINGS, ...settings };
      }
      
      return this.DEFAULT_SETTINGS;
    } catch (error) {
      console.error('Error getting user settings:', error);
      return this.DEFAULT_SETTINGS;
    }
  }

  /**
   * Update user notification settings
   */
  async updateUserSettings(
    userId: string,
    settings: Partial<NotificationFrequencySettings>
  ): Promise<void> {
    try {
      const currentSettings = await this.getUserSettings(userId);
      const newSettings = { ...currentSettings, ...settings };
      
      const key = `notification_settings_${userId}`;
      await AsyncStorage.setItem(key, JSON.stringify(newSettings));
    } catch (error) {
      console.error('Error updating user settings:', error);
    }
  }

  /**
   * Check if current time is within quiet hours
   */
  private isQuietHours(timestamp: number, quietHours: { start: number; end: number }): boolean {
    const date = new Date(timestamp);
    const hour = date.getHours();
    
    if (quietHours.start < quietHours.end) {
      // Same day quiet hours (e.g., 14:00 - 18:00)
      return hour >= quietHours.start && hour < quietHours.end;
    } else {
      // Overnight quiet hours (e.g., 22:00 - 08:00)
      return hour >= quietHours.start || hour < quietHours.end;
    }
  }

  /**
   * Get daily notification count for user
   */
  private async getDailyNotificationCount(userId: string): Promise<number> {
    try {
      const today = new Date().toDateString();
      const key = `notification_count_${userId}_${today}`;
      const count = await AsyncStorage.getItem(key);
      return count ? parseInt(count) : 0;
    } catch (error) {
      console.error('Error getting daily notification count:', error);
      return 0;
    }
  }

  /**
   * Get last notification time for specific type
   */
  private async getLastNotificationTime(userId: string, type: string): Promise<number | null> {
    try {
      const key = `last_notification_${userId}_${type}`;
      const time = await AsyncStorage.getItem(key);
      return time ? parseInt(time) : null;
    } catch (error) {
      console.error('Error getting last notification time:', error);
      return null;
    }
  }

  /**
   * Check if current time is optimal for sending notifications
   */
  private async isOptimalSendTime(userId: string, timestamp: number): Promise<boolean> {
    try {
      // Get user engagement data
      const engagementData = await this.getUserEngagementData(userId);
      
      if (!engagementData) {
        return true; // Default to allowing if no data
      }

      const hour = new Date(timestamp).getHours();
      
      // Check if current hour is in user's peak hours
      return engagementData.peakHours.includes(hour);
    } catch (error) {
      console.error('Error checking optimal send time:', error);
      return true;
    }
  }

  /**
   * Get user engagement data for intelligent timing
   */
  private async getUserEngagementData(userId: string): Promise<UserEngagementData | null> {
    try {
      // This would typically come from analytics data
      // For now, return default peak hours (9 AM - 6 PM)
      return {
        userId,
        peakHours: [9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
        timezone: 'Asia/Kolkata',
        lastActiveTime: Date.now(),
        avgSessionDuration: 15 * 60 * 1000, // 15 minutes
        preferredNotificationTypes: ['like', 'subscribe', 'rating'],
      };
    } catch (error) {
      console.error('Error getting user engagement data:', error);
      return null;
    }
  }

  /**
   * Batch similar notifications to reduce frequency
   */
  async batchNotifications(
    userId: string,
    notifications: Array<{ type: string; data: any }>
  ): Promise<string> {
    try {
      const settings = await this.getUserSettings(userId);
      
      if (!settings.enableBatching) {
        return 'batching_disabled';
      }

      const batchKey = `notification_batch_${userId}`;
      const now = Date.now();
      
      // Get existing batch
      const existingBatch = await AsyncStorage.getItem(batchKey);
      let batch: NotificationBatch;
      
      if (existingBatch) {
        batch = JSON.parse(existingBatch);
        
        // Check if batch is too old
        if (now - batch.batchedAt > settings.batchDelay) {
          // Send existing batch and start new one
          await this.sendBatchedNotifications(batch);
          batch = this.createNewBatch(userId, notifications, now);
        } else {
          // Add to existing batch
          batch.notifications.push(...notifications.map(n => ({
            ...n,
            scheduledTime: now + settings.batchDelay,
          })));
        }
      } else {
        // Create new batch
        batch = this.createNewBatch(userId, notifications, now);
      }
      
      await AsyncStorage.setItem(batchKey, JSON.stringify(batch));
      
      // Schedule batch processing
      setTimeout(() => {
        this.processBatch(userId);
      }, settings.batchDelay);
      
      return 'batched';
    } catch (error) {
      console.error('Error batching notifications:', error);
      return 'error';
    }
  }

  /**
   * Create a new notification batch
   */
  private createNewBatch(
    userId: string,
    notifications: Array<{ type: string; data: any }>,
    timestamp: number
  ): NotificationBatch {
    return {
      userId,
      notifications: notifications.map(n => ({
        ...n,
        scheduledTime: timestamp,
      })),
      batchedAt: timestamp,
    };
  }

  /**
   * Process a notification batch
   */
  private async processBatch(userId: string): Promise<void> {
    try {
      const batchKey = `notification_batch_${userId}`;
      const batchData = await AsyncStorage.getItem(batchKey);
      
      if (batchData) {
        const batch: NotificationBatch = JSON.parse(batchData);
        await this.sendBatchedNotifications(batch);
        await AsyncStorage.removeItem(batchKey);
      }
    } catch (error) {
      console.error('Error processing batch:', error);
    }
  }

  /**
   * Send batched notifications
   */
  private async sendBatchedNotifications(batch: NotificationBatch): Promise<void> {
    try {
      // Group notifications by type
      const grouped = batch.notifications.reduce((acc, notification) => {
        if (!acc[notification.type]) {
          acc[notification.type] = [];
        }
        acc[notification.type].push(notification);
        return acc;
      }, {} as Record<string, any[]>);

      // Send grouped notifications
      for (const [type, notifications] of Object.entries(grouped)) {
        if (notifications.length === 1) {
          // Send single notification
          // Implementation would call PushNotificationService
        } else {
          // Send batched notification
          const message = this.createBatchedMessage(type, notifications.length);
          // Implementation would call PushNotificationService with batched message
        }
      }
    } catch (error) {
      console.error('Error sending batched notifications:', error);
    }
  }

  /**
   * Create a batched notification message
   */
  private createBatchedMessage(type: string, count: number): { title: string; body: string } {
    switch (type) {
      case 'like':
        return {
          title: '❤️ New Likes!',
          body: `You received ${count} new likes on your business`,
        };
      case 'subscribe':
        return {
          title: '🔔 New Subscribers!',
          body: `${count} people subscribed to your business`,
        };
      case 'rating':
        return {
          title: '⭐ New Ratings!',
          body: `You received ${count} new ratings`,
        };
      default:
        return {
          title: '🔔 New Activity',
          body: `You have ${count} new activities`,
        };
    }
  }

  /**
   * Clean up old frequency data
   */
  async cleanupOldData(): Promise<void> {
    try {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      
      // Clean up old daily counts
      const keys = await AsyncStorage.getAllKeys();
      const oldKeys = keys.filter(key => 
        key.startsWith('notification_count_') && 
        new Date(key.split('_').pop() || '') < sevenDaysAgo
      );
      
      if (oldKeys.length > 0) {
        await AsyncStorage.multiRemove(oldKeys);
      }
      
      console.log('Old notification frequency data cleaned up');
    } catch (error) {
      console.error('Error cleaning up old data:', error);
    }
  }
}

export default NotificationFrequencyManager.getInstance();
