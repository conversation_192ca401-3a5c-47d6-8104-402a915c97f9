/**
 * Utility functions for the React Native app
 * Includes number formatting, currency formatting, and other common utilities
 */

/**
 * Formats a number using the Indian numbering system with short notations.
 * Supports: <PERSON> (Thousand), <PERSON> (Lakh), <PERSON><PERSON> (Crore), <PERSON><PERSON> (Arab), <PERSON><PERSON> (Kharab), <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, etc.
 * Examples:
 *   1_200 -> "1.2K"
 *   1_20_000 -> "1.2L"
 *   1_20_00_000 -> "1.2Cr"
 *   1_20_00_00_000 -> "1.2Ar"
 *   1_20_00_00_00_000 -> "1.2Khar"
 *   1_20_00_00_00_00_000 -> "1.2Neel"
 *   1_20_00_00_00_00_00_000 -> "1.2Padma"
 *   1_20_00_00_00_00_00_00_000 -> "1.2Shankh"
 */
export function formatIndianNumberShort(num: number): string {
  if (num === null || num === undefined || isNaN(num)) return "0";
  const absNum = Math.abs(num);

  // Indian units and their values
  const units = [
    { value: 1e5, symbol: "L" }, // Lakh
    { value: 1e7, symbol: "Cr" }, // Crore
    { value: 1e9, symbol: "Ar" }, // Arab
    { value: 1e11, symbol: "Khar" }, // Kharab
    { value: 1e13, symbol: "Neel" }, // Neel
    { value: 1e15, symbol: "Padma" }, // Padma
    { value: 1e17, symbol: "Shankh" }, // Shankh
  ];

  // For thousands (K), use western style for sub-lakh
  if (absNum < 1e5) {
    if (absNum >= 1e3) {
      return (num / 1e3).toFixed(1).replace(/\.0$/, "") + "K";
    }
    return num.toString();
  }

  // Find the largest unit that fits
  for (let i = units.length - 1; i >= 0; i--) {
    if (absNum >= units[i].value) {
      return (
        (num / units[i].value).toFixed(1).replace(/\.0$/, "") + units[i].symbol
      );
    }
  }

  // Fallback (should not reach here)
  return num.toString();
}

/**
 * Formats a currency amount with the appropriate currency symbol
 * @param amount The amount to format
 * @param currency The currency code (e.g., INR, USD)
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  currency: string = "INR"
): string {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return "Invalid amount";
  }

  try {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  } catch {
    // Catch any error without using the error variable
    // Fallback in case of invalid currency code
    return `${currency} ${amount.toFixed(2)}`;
  }
}

/**
 * Formats a string to title case (first letter of each word capitalized)
 * @param text The text to format
 * @returns The text in title case
 */
export function toTitleCase(text: string): string {
  if (!text) return "";

  return text
    .toLowerCase()
    .replace(/\b\w/g, (char) => char.toUpperCase());
}

/**
 * Formats a date in a user-friendly format with Indian Standard Time (IST)
 * @param date The date to format
 * @param includeTime Whether to include time in the formatted string
 * @returns Formatted date string in IST
 */
export function formatDate(date: Date, includeTime: boolean = false): string {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return "Invalid date";
  }

  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "long",
    day: "numeric",
    timeZone: "Asia/Kolkata", // Explicitly set timezone to IST
  };

  if (includeTime) {
    options.hour = "2-digit";
    options.minute = "2-digit";
    options.hour12 = true;
  }

  return date.toLocaleString("en-IN", options);
}

/**
 * Masks an email address for privacy
 * @param email The email to mask
 * @returns Masked email string
 */
export function maskEmail(email: string): string {
  if (!email || !email.includes("@")) return email;
  
  const [username, domain] = email.split("@");
  if (username.length <= 2) return email;
  
  const maskedUsername = username[0] + "*".repeat(username.length - 2) + username[username.length - 1];
  return `${maskedUsername}@${domain}`;
}

/**
 * Masks a phone number for privacy
 * @param phone The phone number to mask
 * @returns Masked phone number string
 */
export function maskPhoneNumber(phone: string): string {
  if (!phone) return phone;
  
  const digits = phone.replace(/\D/g, "");
  if (digits.length < 6) return phone;
  
  const lastFour = digits.slice(-4);
  const masked = "*".repeat(digits.length - 4) + lastFour;
  
  // Preserve original formatting if possible
  return phone.replace(/\d/g, (digit, index) => {
    const digitIndex = phone.slice(0, index + 1).replace(/\D/g, "").length - 1;
    return digitIndex < digits.length - 4 ? "*" : digit;
  });
}

/**
 * Cleans phone number from auth context (removes +91 prefix if present)
 * @param phone The phone number from auth
 * @returns Cleaned phone number
 */
export function cleanPhoneFromAuth(phone: string): string {
  if (!phone) return "";
  
  // Remove +91 prefix if present
  if (phone.startsWith("+91")) {
    return phone.slice(3);
  }
  
  return phone;
}
