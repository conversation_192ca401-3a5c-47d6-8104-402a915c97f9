"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { motion } from "framer-motion";
import { Users, UserCheck } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn, formatIndianNumberShort } from "@/lib/utils";
import {
  SubscriptionSearch,
  SubscriptionPagination,
  SubscriptionListSkeleton,
  SubscriptionList,
  SubscriptionData
} from "@/app/components/shared/subscriptions";
import { SubscriberWithProfile, BusinessFollowingWithProfile } from "../actions";

interface BusinessSubscriptionsPageClientProps {
  initialSubscribers: SubscriberWithProfile[];
  subscribersCount: number;
  subscribersCurrentPage: number;
  initialFollowing: BusinessFollowingWithProfile[];
  followingCount: number;
  followingCurrentPage: number;
  searchTerm: string;
  activeTab: string;
}

export default function BusinessSubscriptionsPageClient({
  initialSubscribers,
  subscribersCount,
  subscribersCurrentPage,
  initialFollowing,
  followingCount,
  followingCurrentPage,
  searchTerm,
  activeTab,
}: BusinessSubscriptionsPageClientProps) {
  const router = useRouter();
  const _searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  // Reset loading state when component receives new data
  useEffect(() => {
    setIsLoading(false);
  }, [initialSubscribers, initialFollowing]);

  // Handle tab change
  const handleTabChange = useCallback((tab: string) => {
    setIsLoading(true);
    const params = new URLSearchParams();
    params.set('tab', tab);
    params.set('page', '1'); // Reset to page 1 on tab change
    router.push(`/dashboard/business/subscriptions?${params.toString()}`);
  }, [router]);

  // Handle search (only for following tab)
  const handleSearch = useCallback((term: string) => {
    if (activeTab !== 'following') return; // Only allow search for following tab

    setIsLoading(true);
    const params = new URLSearchParams();
    params.set('tab', activeTab);
    if (term) {
      params.set('search', term);
    }
    params.set('page', '1'); // Reset to page 1 on new search
    router.push(`/dashboard/business/subscriptions?${params.toString()}`);
  }, [router, activeTab]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setIsLoading(true);
    const params = new URLSearchParams();
    params.set('tab', activeTab);
    // Only include search term for following tab
    if (activeTab === 'following' && searchTerm) {
      params.set('search', searchTerm);
    }
    params.set('page', page.toString());
    router.push(`/dashboard/business/subscriptions?${params.toString()}`);
  }, [router, activeTab, searchTerm]);

  // Transform data for the shared components
  const transformedSubscribers: SubscriptionData[] = useMemo(() => {
    return initialSubscribers.map(sub => ({
      id: sub.id,
      profile: sub.profile
    })).filter(sub => sub.profile !== null) as SubscriptionData[];
  }, [initialSubscribers]);

  const transformedFollowing: SubscriptionData[] = useMemo(() => {
    return initialFollowing.map(sub => ({
      id: sub.id,
      profile: sub.business_profiles ? {
        id: sub.business_profiles.id,
        name: sub.business_profiles.business_name,
        slug: sub.business_profiles.business_slug,
        logo_url: sub.business_profiles.logo_url,
        city: sub.business_profiles.city,
        state: sub.business_profiles.state,
        pincode: sub.business_profiles.pincode,
        address_line: sub.business_profiles.address_line,
        type: 'business' as const,
      } : null
    })).filter(sub => sub.profile !== null) as SubscriptionData[];
  }, [initialFollowing]);

  // Calculate pagination data
  const currentData = activeTab === 'subscribers' ? transformedSubscribers : transformedFollowing;
  const currentCount = activeTab === 'subscribers' ? subscribersCount : followingCount;
  const currentPage = activeTab === 'subscribers' ? subscribersCurrentPage : followingCurrentPage;
  const itemsPerPage = 10;
  const totalPages = Math.max(1, Math.ceil(currentCount / itemsPerPage));

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="relative space-y-6 max-w-6xl mx-auto"
    >
      <motion.div variants={itemVariants} className="mb-6 relative z-10">
        <Card className={cn(
          "border shadow-md transition-all duration-300 hover:shadow-lg",
          "bg-white dark:bg-black border-neutral-200 dark:border-neutral-800"
        )}>
          <CardHeader className="pb-4 border-b border-neutral-100 dark:border-neutral-800">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-500 dark:text-blue-400 self-start">
                <Users className="w-5 h-5" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Subscriptions
                </h3>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                  Manage your subscribers and following
                </p>
              </div>
            </div>

            {/* Tabs */}
            <div className="mt-4 flex space-x-1 border-b border-neutral-200 dark:border-neutral-800">
              <Button
                variant={activeTab === 'subscribers' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => handleTabChange('subscribers')}
                className={cn(
                  "rounded-b-none border-b-2 border-transparent",
                  activeTab === 'subscribers'
                    ? "border-b-[var(--brand-gold)] bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/20"
                    : "hover:bg-neutral-100 dark:hover:bg-neutral-800"
                )}
              >
                <Users className="w-4 h-4 mr-2" />
                Subscribers ({formatIndianNumberShort(subscribersCount)})
              </Button>
              <Button
                variant={activeTab === 'following' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => handleTabChange('following')}
                className={cn(
                  "rounded-b-none border-b-2 border-transparent",
                  activeTab === 'following'
                    ? "border-b-[var(--brand-gold)] bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/20"
                    : "hover:bg-neutral-100 dark:hover:bg-neutral-800"
                )}
              >
                <UserCheck className="w-4 h-4 mr-2" />
                Following ({formatIndianNumberShort(followingCount)})
              </Button>
            </div>

            {/* Search component - only show for following tab */}
            {activeTab === 'following' && (
              <div className="mt-4">
                <SubscriptionSearch
                  onSearch={handleSearch}
                  initialSearchTerm={searchTerm}
                  placeholder="Search businesses..."
                />
              </div>
            )}
          </CardHeader>

          <CardContent className="pt-4">
            {/* Count display - only show for following tab with search */}
            {activeTab === 'following' && searchTerm && !isLoading && (
              <div className="mb-4 text-sm text-neutral-500 dark:text-neutral-400">
                Found {formatIndianNumberShort(currentCount)} {currentCount === 1 ? 'result' : 'results'}
                {searchTerm ? ` matching "${searchTerm}"` : ''}
              </div>
            )}

            {/* Show skeleton loader when loading */}
            {isLoading ? (
              <SubscriptionListSkeleton />
            ) : (
              <>
                {/* Subscription List */}
                <SubscriptionList
                  initialSubscriptions={currentData}
                  showUnsubscribe={activeTab === 'following'}
                  emptyMessage={
                    activeTab === 'subscribers'
                      ? "No subscribers yet."
                      : "You haven't subscribed to any businesses yet."
                  }
                  emptyDescription={
                    activeTab === 'subscribers'
                      ? "When people subscribe to your business, they'll appear here."
                      : "Subscribe to businesses to receive updates and notifications."
                  }
                  showDiscoverButton={activeTab === 'following'}
                />

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-6">
                    <SubscriptionPagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
