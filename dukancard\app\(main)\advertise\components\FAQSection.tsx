"use client";

import { motion } from "framer-motion";
import {
  sectionFadeIn,
  itemFadeIn,
} from "@/app/(main)/components/landing/animations";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import SectionBackground from "@/app/(main)/components/SectionBackground";

export default function FAQSection() {
  const faqs = [
    {
      question: "What types of ads can I place on Dukancard?",
      answer:
        "Dukancard supports image-based advertisements that can be displayed to users in specific localities. Your ad will be shown to users browsing business cards and products in your target areas.",
    },
    {
      question: "How does locality-based targeting work?",
      answer:
        "Our targeting system allows you to select specific pincodes or localities where you want your ads to be displayed. This ensures your ads are shown to users in areas where your business operates or wants to expand.",
    },
    {
      question: "What are the pricing options for advertising?",
      answer:
        "Pricing depends on factors like the number of localities targeted, duration of the campaign, and expected impressions. Contact our advertising team for a customized quote based on your specific requirements.",
    },
    {
      question: "How long does it take for my ad to go live?",
      answer:
        "Once we receive your ad creative and payment, your ad can typically go live within 24-48 hours after our review process is complete.",
    },
    {
      question: "Can I change my ad or targeting during a campaign?",
      answer:
        "Yes, you can request changes to your ad creative or targeting parameters during your campaign. Contact our advertising team to discuss the changes you'd like to make.",
    },
    {
      question: "Do you provide reports on ad performance?",
      answer:
        "We're currently developing our analytics system for ad performance. In the initial phase, we'll provide manual reports on request. Our team is working on a comprehensive dashboard that will show impressions, views, and other metrics in future updates.",
    },
  ];

  return (
    <motion.section
      variants={sectionFadeIn}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      className="py-16 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto relative"
    >
      {/* Enhanced background with SectionBackground component */}
      <div className="absolute inset-0 -z-10">
        <SectionBackground variant="gradient" intensity="low" />
      </div>

      <motion.div
        variants={itemFadeIn}
        custom={0}
        className="text-center mb-12"
      >
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Frequently Asked{" "}
          <span className="text-[var(--brand-gold)]">Questions</span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Find answers to common questions about advertising with Dukancard.
        </p>
      </motion.div>

      <motion.div
        variants={itemFadeIn}
        custom={1}
        className="max-w-3xl mx-auto bg-white/90 dark:bg-black/60 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 rounded-2xl p-6 md:p-8 shadow-lg"
      >
        <Accordion type="single" collapsible className="w-full">
          {faqs.map((faq, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-left font-medium text-foreground">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-muted-foreground">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </motion.div>
    </motion.section>
  );
}
