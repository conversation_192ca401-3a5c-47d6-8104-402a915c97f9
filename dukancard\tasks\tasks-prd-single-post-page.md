# Tasks: Single Post Page Implementation (Next.js)

## Relevant Files

- `app/post/[postId]/page.tsx` - Main single post page component with server-side rendering ✅
- `app/post/[postId]/loading.tsx` - Loading UI for single post page ✅
- `app/post/[postId]/not-found.tsx` - 404 error page for non-existent posts ✅
- `components/post/SinglePostView.tsx` - Client component for single post display ✅
- `components/post/SinglePostView.test.tsx` - Unit tests for SinglePostView component
- `components/post/PostShareButton.tsx` - Share button component for posts ✅
- `components/post/PostShareButton.test.tsx` - Unit tests for PostShareButton component
- `lib/actions/posts/fetchSinglePost.ts` - Server action to fetch individual post data ✅
- `lib/actions/posts/fetchSinglePost.test.ts` - Unit tests for fetchSinglePost function
- `lib/utils/postUrl.ts` - Utility functions for post URL generation ✅
- `lib/utils/postUrl.test.ts` - Unit tests for postUrl utilities
- `components/feed/shared/ModernPostCard.tsx` - Updated to include navigation to single post pages ✅
- `components/post/BackNavigation.tsx` - Back navigation component for single post pages ✅
- `app/post/[postId]/error.tsx` - Error boundary for single post pages ✅

### Notes

- Unit tests should be placed alongside the code files they are testing
- Use `npx jest [optional/path/to/test/file]` to run tests
- Server components should handle data fetching and error states 
- Client components should handle user interactions and sharing

## Tasks

- [x] 1.0 Create Single Post Page Route Structure
  - [x] 1.1 Create `app/post/[postId]/page.tsx` with dynamic route parameter handling
  - [x] 1.2 Set up proper TypeScript interfaces for page props and params
  - [x] 1.3 Create `app/post/[postId]/loading.tsx` with post loading skeleton
  - [x] 1.4 Create `app/post/[postId]/not-found.tsx` for 404 error handling
  - [x] 1.5 Configure route metadata and SEO tags for single post pages
  - [ ] 1.6 Test dynamic routing with various post IDs

- [x] 2.0 Implement Post Data Fetching Logic
  - [x] 2.1 Create `lib/actions/posts/fetchSinglePost.ts` server action
  - [x] 2.2 Implement post fetching using unified_posts view with proper error handling
  - [x] 2.3 Add post validation to ensure post exists and is accessible
  - [x] 2.4 Implement proper TypeScript types for single post response
  - [x] 2.5 Add caching strategy for frequently accessed posts
  - [x] 2.6 Handle edge cases (deleted posts, invalid IDs, database errors)

- [x] 3.0 Build Single Post Display Components
  - [x] 3.1 Create `components/post/SinglePostView.tsx` client component
  - [x] 3.2 Integrate existing `ModernPostCard` component for consistent styling
  - [x] 3.3 Ensure all post data is displayed (content, image, author, timestamp, location)
  - [x] 3.4 Add proper responsive design for mobile and desktop
  - [x] 3.5 Implement proper image loading and error states
  - [x] 3.6 Add linked products display using existing ProductListItem component
  - [x] 3.7 Display mentioned businesses with proper formatting

- [x] 4.0 Add Post Sharing Functionality
  - [x] 4.1 Create `components/post/PostShareButton.tsx` component
  - [x] 4.2 Implement copy-to-clipboard functionality for post URLs
  - [x] 4.3 Add visual feedback (toast/notification) when URL is copied
  - [x] 4.4 Create `lib/utils/postUrl.ts` for URL generation utilities
  - [x] 4.5 Add proper error handling for sharing failures
  - [x] 4.6 Style share button to match existing design system

- [x] 5.0 Implement Navigation and Linking
  - [x] 5.1 Update existing post cards to link to single post pages
  - [x] 5.2 Modify `ModernPostCard` component to wrap content in clickable Link
  - [x] 5.3 Ensure proper navigation without breaking existing functionality
  - [x] 5.4 Add proper hover states and cursor styling for clickable posts
  - [ ] 5.5 Test navigation from various feed pages (business, customer, unified)
  - [x] 5.6 Implement proper back navigation or breadcrumbs

- [x] 6.0 Add Error Handling and Loading States
  - [x] 6.1 Implement comprehensive error boundaries for single post pages
  - [x] 6.2 Create proper loading skeletons that match post card structure
  - [x] 6.3 Add error messages for network failures, invalid post IDs
  - [x] 6.4 Implement retry mechanisms for failed post fetches
  - [x] 6.5 Add proper 404 handling with user-friendly messages
  - [ ] 6.6 Test error scenarios and edge cases thoroughly

- [ ] 7.0 Write Tests and Documentation
  - [ ] 7.1 Write unit tests for `fetchSinglePost` server action
  - [ ] 7.2 Write component tests for `SinglePostView` component
  - [ ] 7.3 Write tests for `PostShareButton` functionality
  - [ ] 7.4 Write integration tests for complete single post page flow
  - [ ] 7.5 Test URL generation and sharing utilities
  - [ ] 7.6 Add JSDoc comments and documentation for all new functions
  - [ ] 7.7 Update project README with single post page feature documentation
