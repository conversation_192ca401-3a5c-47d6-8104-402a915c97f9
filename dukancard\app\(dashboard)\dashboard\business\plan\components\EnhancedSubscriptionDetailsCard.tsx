"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card";
import { cn, formatDate } from "@/lib/utils";
import SubscriptionStatusBadge, {
  SubscriptionStatus,
} from "../components/SubscriptionStatusBadge";
import {
  Calendar,
  Clock,
  CreditCard,
  XCircle,
  Sparkles,
  Shield,
} from "lucide-react";

import FlipTimer from "../../components/FlipTimer";
import { motion } from "framer-motion";
import {
  SUBSCRIPTION_STATUS
} from "@/lib/razorpay/webhooks/handlers/utils";

interface EnhancedSubscriptionDetailsCardProps {
  status: SubscriptionStatus;
  razorpayStatus?: string | null | undefined;
  planName: string;
  planCycle: "monthly" | "yearly";
  startDate?: string | null;
  _endDate?: string | null;
  _nextBillingDate?: string | null;
  trialEndDate?: string | null;
  cancellationRequestedAt?: string | null;
  subscriptionId?: string | null | undefined;
  _lastPaymentDate?: string | null;
  _onCancelSubscription?: () => void;
  razorpayDates?: {
    current_start: string | null;
    current_end: string | null;
    charge_at: string | null;
    start_at: string | null;
    end_at: string | null;
    created_at: string | null;
    ended_at: string | null;
    change_scheduled_at: string | null;
  } | null;
}

export default function EnhancedSubscriptionDetailsCard({
  status,
  razorpayStatus,
  planName,
  planCycle,
  startDate,
  trialEndDate,
  cancellationRequestedAt,
  subscriptionId,
  razorpayDates,
}: EnhancedSubscriptionDetailsCardProps) {
  const now = new Date();
  // Don't show trial as active if the subscription is authenticated
  const isTrialActive =
    trialEndDate && new Date(trialEndDate) > now && status !== SUBSCRIPTION_STATUS.AUTHENTICATED;

  // Get subscription processing status from context (not currently used)
  // const { } = useSubscriptionProcessing();

  // Determine if the current plan is premium for enhanced styling
  const isPremiumPlan = planName === "Pro" || planName === "Enterprise";
  const isActiveOrAuthenticated =
    status === SUBSCRIPTION_STATUS.ACTIVE || status === SUBSCRIPTION_STATUS.AUTHENTICATED;

  return (
    <Card
      className={cn(
        "border shadow-sm transition-all duration-300 hover:shadow-md overflow-hidden h-full",
        isPremiumPlan && isActiveOrAuthenticated
          ? "bg-gradient-to-br from-white dark:from-neutral-900 to-[var(--brand-gold)]/5 dark:to-[var(--brand-gold)]/10 border-[var(--brand-gold)]/20 dark:border-[var(--brand-gold)]/10"
          : "bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-800"
      )}
    >
      {/* Premium glow effect for premium plans */}
      {isPremiumPlan && isActiveOrAuthenticated && (
        <div className="absolute inset-0 pointer-events-none">
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/5 to-[var(--brand-gold)]/20 rounded-xl opacity-50 blur-xl"
            animate={{
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </div>
      )}

      <CardHeader className="relative z-10">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <motion.div
              className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10"
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              {isPremiumPlan && isActiveOrAuthenticated ? (
                <motion.div
                  animate={{ rotate: [0, 10, 0, -10, 0] }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <Sparkles className="h-5 w-5 text-[var(--brand-gold)]" />
                </motion.div>
              ) : (
                <Shield className="h-5 w-5 text-primary" />
              )}
            </motion.div>
            <CardTitle>Subscription Details</CardTitle>
          </div>
          <SubscriptionStatusBadge status={status} />
        </div>
        <CardDescription className="mt-2">
          {/* Show Razorpay status if different from current status */}
          {razorpayStatus && status !== razorpayStatus && (
            <div className="flex items-center justify-between mt-1">
              <span className="text-xs text-muted-foreground">
                Razorpay Status: {razorpayStatus}
              </span>
            </div>
          )}
        </CardDescription>
      </CardHeader>

      <CardContent className="relative z-10">
        {status === "inactive" && !isTrialActive ? (
          <motion.div
            className="flex flex-col items-center justify-center text-center py-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <XCircle className="h-12 w-12 text-neutral-300 dark:text-neutral-700 mb-4" />
            </motion.div>
            <motion.div
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h3 className="text-lg font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                No Active Subscription
              </h3>
            </motion.div>
            <motion.div
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <p className="text-sm text-neutral-500 dark:text-neutral-400 max-w-md">
                You don&apos;t have an active subscription. Choose a plan below
                to unlock premium features for your business.
              </p>
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {/* Plan Information */}
            <motion.div
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-200">
                    {planName}
                  </h3>
                  <p className="text-sm text-neutral-500 dark:text-neutral-400">
                    {planCycle === "monthly" ? "Monthly" : "Yearly"} billing
                  </p>
                </div>
                {isPremiumPlan && isActiveOrAuthenticated && (
                  <motion.div
                    className="p-2 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-full"
                    whileHover={{ scale: 1.1 }}
                    animate={{
                      boxShadow: ["0 0 0px rgba(var(--brand-gold-rgb), 0.3)", "0 0 10px rgba(var(--brand-gold-rgb), 0.5)", "0 0 0px rgba(var(--brand-gold-rgb), 0.3)"]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <motion.div
                      animate={{ rotate: [0, 10, 0, -10, 0] }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Sparkles className="h-5 w-5 text-[var(--brand-gold)]" />
                    </motion.div>
                  </motion.div>
                )}
              </div>

              {/* Subscription details in a grid layout */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* Started On - for active, authenticated, pending subscriptions */}
                {startDate &&
                  (status === SUBSCRIPTION_STATUS.ACTIVE ||
                    status === SUBSCRIPTION_STATUS.AUTHENTICATED ||
                    status === SUBSCRIPTION_STATUS.PENDING) && (
                    <motion.div
                      className="flex flex-col space-y-1 p-3 rounded-lg bg-white/80 dark:bg-black/20 border border-green-100 dark:border-green-900/30"
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: 0.2 }}
                    >
                      <span className="text-xs text-muted-foreground">
                        Started On
                      </span>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-green-600 dark:text-green-400" />
                        <span className="text-sm font-semibold">
                          {formatDate(new Date(startDate))}
                        </span>
                      </div>
                    </motion.div>
                  )}

                {/* Subscription ID - for active, authenticated, pending subscriptions */}
                {subscriptionId &&
                  (status === SUBSCRIPTION_STATUS.ACTIVE ||
                    status === SUBSCRIPTION_STATUS.AUTHENTICATED ||
                    status === SUBSCRIPTION_STATUS.PENDING) && (
                    <motion.div
                      className="flex flex-col space-y-1 p-3 rounded-lg bg-white/80 dark:bg-black/20 border border-blue-100 dark:border-blue-900/30"
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: 0.3 }}
                    >
                      <span className="text-xs text-muted-foreground">
                        Subscription ID
                      </span>
                      <div className="flex items-center gap-2">
                        <CreditCard className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        <span className="text-sm font-medium truncate">
                          {subscriptionId.substring(0, 12)}...
                        </span>
                      </div>
                    </motion.div>
                  )}

                {/* Trial End Date - for trial subscriptions */}
                {isTrialActive && trialEndDate && (
                  <motion.div
                    className="flex flex-col space-y-1 p-3 rounded-lg bg-white/80 dark:bg-black/20 border border-amber-100 dark:border-amber-900/30 col-span-2"
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <span className="text-xs text-muted-foreground">
                      Trial Ends On
                    </span>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                      <span className="text-sm font-semibold">
                        {formatDate(new Date(trialEndDate))}
                      </span>
                    </div>
                  </motion.div>
                )}

                {/* Cancellation Requested - for cancelled subscriptions */}
                {cancellationRequestedAt && (
                  <motion.div
                    className="flex flex-col space-y-1 p-3 rounded-lg bg-white/80 dark:bg-black/20 border border-red-100 dark:border-red-900/30 col-span-2"
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <span className="text-xs text-muted-foreground">
                      Cancellation Requested
                    </span>
                    <div className="flex items-center gap-2">
                      <XCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
                      <span className="text-sm font-semibold">
                        {formatDate(new Date(cancellationRequestedAt))}
                      </span>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>



            {/* Cancellation Notice */}
            {cancellationRequestedAt && razorpayDates?.current_end && (
              <motion.div
                className="mt-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <div className="flex items-center gap-2 mb-2">
                  <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                  <span className="text-sm font-medium">
                    Subscription Ending
                  </span>
                </div>
                <div className="bg-white/80 dark:bg-black/20 p-3 rounded-lg border border-red-100 dark:border-red-900/30">
                  <div className="mb-2 text-sm">
                    Your subscription will end on{" "}
                    {formatDate(new Date(razorpayDates.current_end))}
                  </div>
                  <FlipTimer
                    endDate={razorpayDates.current_end}
                    label="Service ends in:"
                    tooltipText="Your subscription will end on this date."
                  />
                </div>
              </motion.div>
            )}


          </motion.div>
        )}
      </CardContent>

      {/* Premium footer with helpful information */}
      <CardFooter
        className={cn(
          "flex flex-col items-start gap-2 text-xs text-muted-foreground border-t pt-4 relative z-10",
          isPremiumPlan &&
            isActiveOrAuthenticated &&
            "bg-gradient-to-br from-transparent to-[var(--brand-gold)]/5 dark:to-[var(--brand-gold)]/10 border-[var(--brand-gold)]/10"
        )}
      >
        <div className="flex items-center gap-2">
          <motion.div
            className="flex items-center justify-center w-5 h-5 rounded-full bg-primary/10"
            whileHover={{ scale: 1.2 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <Shield className="w-4 h-4 text-primary" />
          </motion.div>
          <span>Your subscription is securely managed through Razorpay.</span>
        </div>
      </CardFooter>
    </Card>
  );
}
