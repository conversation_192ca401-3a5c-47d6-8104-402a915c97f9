"use client";

import { useState, useMemo } from "react";
import { motion } from "framer-motion";
import { User } from "lucide-react";
import { ProfileForm } from "../ProfileForm";
import AvatarUpload from "./AvatarUpload";
import AddressForm from "./AddressForm";

import ProfileRequirementDialog from "./ProfileRequirementDialog";
import { cn } from "@/lib/utils";

interface ProfilePageClientProps {
  initialName: string | null;
  initialAvatarUrl?: string | null;
  initialAddressData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  } | null;
  hasCompleteAddress?: boolean;
}

export default function ProfilePageClient({
  initialName,
  initialAvatarUrl,
  initialAddressData,
  hasCompleteAddress = false,
}: ProfilePageClientProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(initialAvatarUrl || undefined);

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  // Default theme color (similar to business card)
  const defaultThemeColor = "var(--brand-gold)";

  // Card style with CSS variables for theming
  const cardStyle = useMemo(() => ({
    "--theme-color": defaultThemeColor,
    "--theme-color-80": `${defaultThemeColor}CC`,
    "--theme-color-50": `${defaultThemeColor}80`,
    "--theme-color-30": `${defaultThemeColor}4D`,
    "--theme-color-20": `${defaultThemeColor}33`,
    "--theme-color-10": `${defaultThemeColor}1A`,
    "--theme-color-5": `${defaultThemeColor}0D`,
  }), []);

  return (
    <>
      {/* Profile Requirement Dialog */}
      <ProfileRequirementDialog
        hasCompleteAddress={hasCompleteAddress}
      />

      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="space-y-6"
      >
      <motion.div variants={itemVariants} className="mb-6">
        {/* Main card container - updated to match business card styling */}
        <motion.div
          className={cn(`
            relative w-full
            rounded-xl overflow-hidden
            transition-all duration-500
            bg-white dark:bg-black
            shadow-xl
            transform-gpu
            border border-neutral-200/80 dark:border-neutral-800/80
          `)}
          style={cardStyle as React.CSSProperties}
        >
          {/* Card background with subtle pattern */}
          <div
            className="absolute inset-0 pointer-events-none z-5"
            style={{
              backgroundImage: `url("/decorative/card-texture.svg")`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
              opacity: 0.6,
            }}
          ></div>

          {/* Card content */}
          <div className="relative z-10 p-4 sm:p-5 md:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-200/80 dark:border-neutral-800/80">
              <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
                <User className="w-4 sm:w-5 h-4 sm:h-5" />
              </div>
              <div className="flex-1">
                <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Profile Information
                </h3>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                  Update your personal details
                </p>
              </div>
            </div>

            <div className="flex flex-col gap-6">
              {/* Avatar Upload */}
              <div className="flex flex-col items-center mb-6">
                <AvatarUpload
                  initialAvatarUrl={avatarUrl}
                  userName={initialName}
                  onUpdateAvatar={(url) => setAvatarUrl(url)}
                />
              </div>

              {/* Profile Form */}
              <div className="mt-4">
                <ProfileForm initialName={initialName} />
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Address Form Section */}
      <motion.div variants={itemVariants} className="mb-6">
        <motion.div
          className={cn(`
            relative w-full
            rounded-xl overflow-hidden
            transition-all duration-500
            bg-white dark:bg-black
            shadow-xl
            transform-gpu
            border border-neutral-200/80 dark:border-neutral-800/80
          `)}
          style={cardStyle as React.CSSProperties}
        >
          {/* Card background with subtle pattern */}
          <div
            className="absolute inset-0 pointer-events-none z-5"
            style={{
              backgroundImage: `url("/decorative/card-texture.svg")`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
              opacity: 0.6,
            }}
          ></div>

          {/* Card content */}
          <div className="relative z-10 p-4 sm:p-5 md:p-6">
            <AddressForm initialData={initialAddressData || undefined} />
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
    </>
  );
}
