"use client";

import { useState, useEffect, useTransition } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Search, SortAsc, SortDesc } from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { BlogListItem, BlogSearchParams, PaginationInfo } from "@/lib/types/blog";
import { searchBlogs } from "@/lib/actions/blogs";
import { getPaginationInfo } from "@/lib/utils/pagination";
import BlogCard from "@/components/blog/BlogCard";
import BlogPagination, { PaginationInfo as PaginationInfoComponent } from "@/components/blog/BlogPagination";
import { BlogCardSkeleton } from "@/components/blog/BlogListingSkeleton";
import { useDebounce } from "@/lib/utils/debounce";

interface BlogListingClientProps {
  initialBlogs: BlogListItem[];
  initialTotalCount: number;
  initialPage: number;
  initialQuery: string;
  initialSort: 'newest' | 'oldest';
}

export default function BlogListingClient({
  initialBlogs,
  initialTotalCount,
  initialPage,
  initialQuery,
  initialSort,
}: BlogListingClientProps) {
  const [blogs, setBlogs] = useState<BlogListItem[]>(initialBlogs);
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [sortBy, setSortBy] = useState<"newest" | "oldest">(initialSort);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalCount, setTotalCount] = useState(initialTotalCount);
  const [isPending, startTransition] = useTransition();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  // Calculate pagination info
  const paginationInfo: PaginationInfo = getPaginationInfo(currentPage, totalCount);

  // Fetch blogs with server action
  const fetchBlogs = async (params: BlogSearchParams) => {
    try {
      const result = await searchBlogs(params);
      setBlogs(result.blogs);
      setTotalCount(result.total);
      setCurrentPage(result.page);
    } catch (error) {
      console.error("Error fetching blogs:", error);
    }
  };

  // Effect for search and sort changes
  useEffect(() => {
    if (debouncedSearchQuery !== initialQuery || sortBy !== initialSort) {
      startTransition(() => {
        fetchBlogs({
          query: debouncedSearchQuery,
          sort: sortBy,
          page: 1,
        });
      });
    }
  }, [debouncedSearchQuery, sortBy, initialQuery, initialSort]);

  // Handle page change
  const handlePageChange = (page: number) => {
    startTransition(() => {
      fetchBlogs({
        query: debouncedSearchQuery,
        sort: sortBy,
        page,
      });
    });
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setSearchQuery("");
    setSortBy("newest");
    startTransition(() => {
      fetchBlogs({
        query: "",
        sort: "newest",
        page: 1,
      });
    });
  };

  const activeFiltersCount = [
    searchQuery.trim() !== "",
    sortBy !== "newest"
  ].filter(Boolean).length;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-12"
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-4">
          <span className="text-[var(--brand-gold)]">Dukan</span>
          <span className="text-foreground">card</span>
          <span className="text-foreground"> Blog</span>
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Discover the latest insights, tips, and updates about digital business cards, 
          networking, and business growth.
        </p>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="mb-8"
      >
        <Card className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search blog posts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
                disabled={isPending}
              />
            </div>

            {/* Sort */}
            <Select
              value={sortBy}
              onValueChange={(value: "newest" | "oldest") => setSortBy(value)}
              disabled={isPending}
            >
              <SelectTrigger className="w-full md:w-40">
                <div className="flex items-center gap-2">
                  {sortBy === "newest" ? (
                    <SortDesc className="h-4 w-4" />
                  ) : (
                    <SortAsc className="h-4 w-4" />
                  )}
                  <SelectValue />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
              </SelectContent>
            </Select>

            {/* Clear Filters */}
            {activeFiltersCount > 0 && (
              <Button
                variant="outline"
                onClick={handleClearFilters}
                disabled={isPending}
                className="whitespace-nowrap"
              >
                Clear Filters
              </Button>
            )}
          </div>
        </Card>
      </motion.div>

      {/* Results Count */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="mb-6"
      >
        <PaginationInfoComponent paginationInfo={paginationInfo} />
      </motion.div>

      {/* Blog Grid */}
      <AnimatePresence mode="wait">
        {isPending ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
          >
            {Array.from({ length: 12 }).map((_, index) => (
              <BlogCardSkeleton key={index} />
            ))}
          </motion.div>
        ) : blogs.length > 0 ? (
          <motion.div
            key="blog-grid"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
          >
            {blogs.map((blog, index) => (
              <motion.div
                key={blog.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <BlogCard blog={blog} />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div
            key="no-results"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">📝</div>
            <h3 className="text-xl font-semibold mb-2">No blog posts found</h3>
            <p className="text-muted-foreground mb-4">
              Try adjusting your search terms or filters.
            </p>
            {activeFiltersCount > 0 && (
              <Button onClick={handleClearFilters} variant="outline" disabled={isPending}>
                Clear all filters
              </Button>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Pagination */}
      {blogs.length > 0 && paginationInfo.totalPages > 1 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-8"
        >
          <BlogPagination
            paginationInfo={paginationInfo}
            onPageChange={handlePageChange}
          />
        </motion.div>
      )}
    </div>
  );
}
