"use client";

import React, { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  AlertCircle,
  CreditCard,
  RefreshCw,
  CheckCircle2,
  XCircle
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface PaymentMethodEligibility {
  method: string;
  isEligible: boolean;
  details?: {
    [key: string]: unknown;
  };
}

interface EligiblePaymentMethodsCardProps {
  subscriptionId: string;
  onRefresh?: () => void;
}

export default function EligiblePaymentMethodsCard({
  subscriptionId,
  onRefresh
}: EligiblePaymentMethodsCardProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethodEligibility[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Fetch eligible payment methods
  const fetchEligiblePaymentMethods = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/subscription/${subscriptionId}/eligible-payment-methods`);

      if (!response.ok) {
        throw new Error("Failed to fetch eligible payment methods");
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || "Failed to fetch eligible payment methods");
      }

      setPaymentMethods(data.data || []);
    } catch (error) {
      console.error("Error fetching eligible payment methods:", error);
      setError(error instanceof Error ? error.message : "An unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  }, [subscriptionId]);

  // Fetch payment methods on mount
  useEffect(() => {
    fetchEligiblePaymentMethods();
  }, [fetchEligiblePaymentMethods]);

  // Handle refresh
  const handleRefresh = () => {
    fetchEligiblePaymentMethods();
    if (onRefresh) {
      onRefresh();
    }
  };

  // Get payment method icon and display name
  const getPaymentMethodInfo = (method: string) => {
    const lowerMethod = method.toLowerCase();

    if (lowerMethod.includes("card") || lowerMethod.includes("credit") || lowerMethod.includes("debit")) {
      return {
        icon: <CreditCard className="h-5 w-5" />,
        name: "Card Payments"
      };
    } else if (lowerMethod.includes("upi")) {
      return {
        icon: <Image src="/icons/upi-icon.svg" alt="UPI" width={20} height={20} className="h-5 w-5" />,
        name: "UPI Payments"
      };
    } else if (lowerMethod.includes("netbanking") || lowerMethod.includes("bank")) {
      return {
        icon: <Image src="/icons/bank-icon.svg" alt="Netbanking" width={20} height={20} className="h-5 w-5" />,
        name: "Netbanking"
      };
    } else if (lowerMethod.includes("wallet")) {
      return {
        icon: <Image src="/icons/wallet-icon.svg" alt="Wallet" width={20} height={20} className="h-5 w-5" />,
        name: "Wallet Payments"
      };
    } else if (lowerMethod.includes("emandate") || lowerMethod.includes("e-mandate")) {
      return {
        icon: <CreditCard className="h-5 w-5" />,
        name: "E-Mandate"
      };
    } else if (lowerMethod.includes("enach") || lowerMethod.includes("e-nach")) {
      return {
        icon: <CreditCard className="h-5 w-5" />,
        name: "E-NACH"
      };
    } else {
      return {
        icon: <CreditCard className="h-5 w-5" />,
        name: method
      };
    }
  };

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
        duration: 0.5,
      },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={cardVariants}
      className="w-full"
    >
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl">Eligible Payment Methods</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
          <CardDescription>
            Payment methods available for this subscription
          </CardDescription>
        </CardHeader>

        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="flex items-center gap-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-40" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-6 text-red-500">
              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
              <p>{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                className="mt-4"
              >
                Try Again
              </Button>
            </div>
          ) : paymentMethods.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              No payment method information available.
            </div>
          ) : (
            <div className="space-y-4">
              {paymentMethods.map((method, index) => {
                const methodInfo = getPaymentMethodInfo(method.method);

                return (
                  <React.Fragment key={method.method}>
                    {index > 0 && <Separator />}
                    <div className="flex items-start gap-4 py-2">
                      <div className="mt-1">{methodInfo.icon}</div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="text-base font-medium">{methodInfo.name}</h3>
                          {method.isEligible ? (
                            <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 px-2 py-0.5 rounded-full flex items-center gap-1">
                              <CheckCircle2 className="h-3 w-3" />
                              Available
                            </span>
                          ) : (
                            <span className="text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100 px-2 py-0.5 rounded-full flex items-center gap-1">
                              <XCircle className="h-3 w-3" />
                              Not Available
                            </span>
                          )}
                        </div>

                        {method.details && Object.keys(method.details).length > 0 && (
                          <div className="mt-2 text-sm text-muted-foreground">
                            <ul className="list-disc list-inside space-y-1">
                              {Object.entries(method.details).map(([key, value]) => (
                                <li key={key}>
                                  {key.replace(/_/g, ' ')}: {String(value)}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </React.Fragment>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
