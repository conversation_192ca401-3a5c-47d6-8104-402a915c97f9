"use client";

import { useState, useEffect, useRef } from "react";
import { ChevronLeft, ChevronRight, Star } from "lucide-react";
import { Button } from "@/components/ui/button";

interface Testimonial {
  name: string;
  business: string;
  quote: string;
  rating?: number;
}

interface TestimonialCarouselProps {
  testimonials: Testimonial[];
  autoScroll?: boolean;
  interval?: number;
}

export default function TestimonialCarousel({
  testimonials,
  autoScroll = true,
  interval = 8000, // Increased interval for better performance
}: TestimonialCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const autoScrollTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-scroll functionality with longer interval
  useEffect(() => {
    if (autoScroll && !isPaused) {
      autoScrollTimerRef.current = setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
      }, interval);
    }

    return () => {
      if (autoScrollTimerRef.current) {
        clearTimeout(autoScrollTimerRef.current);
      }
    };
  }, [currentIndex, autoScroll, interval, isPaused, testimonials.length]);

  // Navigation functions
  const goToNext = () => {
    if (autoScrollTimerRef.current) {
      clearTimeout(autoScrollTimerRef.current);
    }
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    setIsPaused(true);

    // Resume auto-scroll after 10 seconds of inactivity
    setTimeout(() => {
      setIsPaused(false);
    }, 10000);
  };

  const goToPrevious = () => {
    if (autoScrollTimerRef.current) {
      clearTimeout(autoScrollTimerRef.current);
    }
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
    setIsPaused(true);

    // Resume auto-scroll after 10 seconds of inactivity
    setTimeout(() => {
      setIsPaused(false);
    }, 10000);
  };

  const goToIndex = (index: number) => {
    if (autoScrollTimerRef.current) {
      clearTimeout(autoScrollTimerRef.current);
    }
    setCurrentIndex(index);
    setIsPaused(true);

    // Resume auto-scroll after 10 seconds of inactivity
    setTimeout(() => {
      setIsPaused(false);
    }, 10000);
  };

  return (
    <div className="relative w-full overflow-hidden py-4">
      {/* Carousel container */}
      <div className="relative w-full min-h-[280px] overflow-hidden rounded-xl">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full max-w-2xl mx-auto p-8 bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200 dark:border-neutral-800 shadow-md">
            {/* Quote mark */}
            <div className="text-[var(--brand-gold)]/20 dark:text-[var(--brand-gold)]/30 text-6xl font-serif absolute top-4 left-6">
              &quot;
            </div>

            {/* Testimonial content */}
            <div className="flex flex-col items-center text-center relative z-10">
              {/* Quote */}
              <p className="text-lg text-neutral-700 dark:text-neutral-300 mb-6 relative z-10">
                &quot;{testimonials[currentIndex].quote}&quot;
              </p>

              {/* Rating stars if available */}
              {testimonials[currentIndex].rating && (
                <div className="flex items-center gap-1 mb-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      size={16}
                      className={`${
                        i < (testimonials[currentIndex].rating || 0)
                          ? "text-[var(--brand-gold)] fill-[var(--brand-gold)]"
                          : "text-neutral-300 dark:text-neutral-600"
                      }`}
                    />
                  ))}
                </div>
              )}

              {/* Author info */}
              <div className="flex flex-col items-center">
                <h4 className="font-semibold text-lg">
                  {testimonials[currentIndex].name}
                </h4>
                <p className="text-neutral-500 dark:text-neutral-400 text-sm">
                  {testimonials[currentIndex].business}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation buttons */}
        <Button
          variant="ghost"
          size="icon"
          className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-black/50 backdrop-blur-sm rounded-full h-8 w-8 shadow-md z-10"
          onClick={goToPrevious}
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>

        <Button
          variant="ghost"
          size="icon"
          className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-black/50 backdrop-blur-sm rounded-full h-8 w-8 shadow-md z-10"
          onClick={goToNext}
        >
          <ChevronRight className="h-5 w-5" />
        </Button>
      </div>

      {/* Dots indicator */}
      <div className="flex justify-center mt-4 gap-1.5">
        {testimonials.map((_, index) => (
          <button
            key={index}
            className={`h-2 rounded-full transition-all duration-300 ${
              index === currentIndex
                ? "w-6 bg-[var(--brand-gold)]"
                : "w-2 bg-neutral-300 dark:bg-neutral-700"
            }`}
            onClick={() => goToIndex(index)}
            aria-label={`Go to testimonial ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
