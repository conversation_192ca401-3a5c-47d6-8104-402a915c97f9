"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Calendar, Flag, Users } from "lucide-react";

export default function MilestonesSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.1 });
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };
  
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    },
  };

  // Milestones data
  const milestones = [
    {
      year: "2024",
      title: "Founded",
      description:
        "Dukancard was conceptualized to bridge the digital divide for small businesses.",
      icon: <Calendar className="w-8 h-8 text-[var(--brand-gold)]" />,
    },
    {
      year: "2025",
      title: "Beta Launch",
      description:
        "First version launched with 500+ early adopters across India.",
      icon: <Flag className="w-8 h-8 text-[var(--brand-gold)]" />,
    },
    {
      year: "?",
      title: "10K Users",
      description:
        "Hoping to cross 10,000 active users with 85% retention rate.",
      icon: <Users className="w-8 h-8 text-[var(--brand-gold)]" />,
    },
  ];

  return (
    <section 
      ref={sectionRef}
      className="py-20 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto bg-muted/50 dark:bg-gradient-to-b dark:from-neutral-900/50 dark:to-black/50 rounded-3xl my-16"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-16"
      >
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Our <span className="text-[var(--brand-gold)]">Journey</span> So Far
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          From humble beginnings to empowering thousands of businesses across
          India.
        </p>
      </motion.div>

      <div className="relative">
        {/* Timeline line - visible on tablet and desktop */}
        <div className="hidden md:block absolute left-1/2 h-full w-0.5 bg-gradient-to-b from-[var(--brand-gold)] via-[var(--brand-gold)]/50 to-[var(--brand-gold)]"></div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="space-y-16"
        >
          {milestones.map((milestone, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`relative flex flex-col md:flex-row ${
                index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
              } items-center`}
            >
              {/* Timeline dot - visible on tablet and desktop */}
              <div className="hidden md:flex absolute left-1/2 -translate-x-1/2 w-8 h-8 rounded-full bg-[var(--brand-gold)] items-center justify-center z-10">
                <motion.div
                  className="w-4 h-4 rounded-full bg-background dark:bg-black"
                  animate={{
                    scale: [1, 1.2, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse",
                    delay: index * 0.5,
                  }}
                />
              </div>

              {/* Content */}
              <div
                className={`w-full md:w-1/2 p-6 ${
                  index % 2 === 0 ? "md:pr-12 md:text-right" : "md:pl-12"
                }`}
              >
                <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/20 p-6 hover:border-primary/50 dark:hover:border-[var(--brand-gold)]/50 transition-all duration-300 hover:shadow-lg dark:hover:shadow-lg dark:hover:shadow-[var(--brand-gold)]/10">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-2 rounded-full bg-background/50 dark:bg-black/50 border border-border dark:border-[var(--brand-gold)]/20">
                      {milestone.icon}
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-foreground">
                        {milestone.title}
                      </h3>
                      <p className="text-[var(--brand-gold)] font-medium">
                        {milestone.year}
                      </p>
                    </div>
                  </div>
                  <p className="text-muted-foreground">{milestone.description}</p>
                </Card>
              </div>

              {/* Year badge - mobile only */}
              <div className="md:hidden flex items-center justify-center w-12 h-12 rounded-full bg-[var(--brand-gold)]/10 border border-[var(--brand-gold)]/30 my-4">
                <span className="text-sm font-bold text-[var(--brand-gold)]">
                  {milestone.year}
                </span>
              </div>

              {/* Empty div for layout on tablet/desktop */}
              <div className="hidden md:block w-1/2"></div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
