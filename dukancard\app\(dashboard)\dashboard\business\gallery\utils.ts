import { PlanType } from "@/lib/config/plans";

/**
 * Get the gallery image limit for a given plan
 * @param planType The plan type
 * @returns The gallery image limit
 */
export function getGalleryLimit(planType: PlanType | string | null | undefined): number {
  if (!planType) return 0;

  switch (planType) {
    case "free":
      return 1;
    case "basic":
      return 3;
    case "growth":
      return 10;
    case "pro":
      return 50;
    case "enterprise":
      return 100;
    case "trial":
      return 3; // Trial users get the same as basic plan
    default:
      return 0;
  }
}

/**
 * Check if a user can add more gallery images based on their plan and current count
 * @param planType The plan type
 * @param currentCount The current number of gallery images
 * @returns Whether the user can add more gallery images
 */
export function canAddMoreGalleryImages(
  planType: PlanType | string | null | undefined,
  currentCount: number
): boolean {
  const limit = getGalleryLimit(planType);
  return currentCount < limit;
}
