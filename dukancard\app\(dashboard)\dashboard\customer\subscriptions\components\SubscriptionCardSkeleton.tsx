"use client";

import { motion } from "framer-motion";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

interface SubscriptionCardSkeletonProps {
  index?: number;
}

export default function SubscriptionCardSkeleton({ index = 0 }: SubscriptionCardSkeletonProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.05 }}
      className={cn(
        "rounded-lg border p-0 overflow-hidden transition-all duration-300",
        "bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",
        "shadow-sm"
      )}
    >
      {/* Card with header image background */}
      <div className="relative">
        {/* Decorative header skeleton */}
        <Skeleton className="h-20 w-full" />

        {/* Avatar - positioned to overlap the header */}
        <div className="absolute -bottom-6 left-4">
          <div className="p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800">
            <Skeleton className="h-16 w-16 rounded-full" />
          </div>
        </div>
      </div>

      {/* Card content */}
      <div className="pt-8 px-4 pb-4">
        <div className="flex flex-col">
          <div className="mb-3">
            {/* Business name skeleton */}
            <Skeleton className="h-6 w-40 mb-2" />
            {/* Location skeleton */}
            <Skeleton className="h-4 w-32 mt-1" />
          </div>

          {/* Action button skeleton */}
          <Skeleton className="h-9 w-full mt-2 rounded-md" />
        </div>
      </div>
    </motion.div>
  );
}

export function SubscriptionListSkeleton() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, index) => (
        <SubscriptionCardSkeleton key={index} index={index} />
      ))}
    </div>
  );
}
