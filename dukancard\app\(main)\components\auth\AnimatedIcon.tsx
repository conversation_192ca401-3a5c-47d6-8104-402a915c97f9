"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";

interface AnimatedIconProps {
  icon: React.ReactNode;
  className?: string;
}

export default function AnimatedIcon({
  icon,
  className = "",
}: AnimatedIconProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="flex justify-center mb-3 sm:mb-4">
      <motion.div
        className={`p-2 sm:p-3 bg-primary/10 dark:bg-[var(--brand-gold)]/10 rounded-full relative ${className}`}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        {/* Pulsing ring effect */}
        {isClient && (
          <motion.div
            className="absolute inset-0 rounded-full bg-primary/5 dark:bg-[var(--brand-gold)]/5"
            animate={{
              scale: 1.2,
              opacity: 0.8
            }}
            initial={{
              scale: 1,
              opacity: 0.5
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
        )}

        {/* Icon */}
        <motion.div
          animate={{ rotate: 3 }}
          initial={{ rotate: -3 }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        >
          {icon}
        </motion.div>
      </motion.div>
    </div>
  );
}
