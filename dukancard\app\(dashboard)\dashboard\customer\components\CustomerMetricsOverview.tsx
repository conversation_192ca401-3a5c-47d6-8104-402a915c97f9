"use client";

import { motion } from "framer-motion";
import { Star, MessageSquare, Users, Heart } from "lucide-react";
import CustomerAnimatedMetricCard from "./CustomerAnimatedMetricCard";

interface CustomerMetricsOverviewProps {
  initialReviewCount: number;
  initialSubscriptionCount: number;
  initialLikesCount: number;
  userId: string;
}

export default function CustomerMetricsOverview({
  initialReviewCount,
  initialSubscriptionCount,
  initialLikesCount,
  userId: _userId,
}: CustomerMetricsOverviewProps) {
  // Since real-time is not enabled for these tables, we'll use the initial values
  const reviewCount = initialReviewCount;
  const subscriptionCount = initialSubscriptionCount;
  const likesCount = initialLikesCount;

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Activity Score - Above main metrics */}
      <div className="grid grid-cols-1 gap-4">
        <CustomerAnimatedMetricCard
          title="Activity Score"
          value={reviewCount + subscriptionCount * 2 + likesCount}
          icon={MessageSquare}
          description="Your engagement level"
          color="brand"
        />
      </div>

      {/* Customer Stats Section - 3 columns: Likes, Subscriptions, Reviews */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Likes Card */}
        <CustomerAnimatedMetricCard
          title="Likes"
          value={likesCount}
          icon={Heart}
          description="Businesses you've liked"
          color="red"
          href="/dashboard/customer/likes"
        />

        {/* Subscriptions Card */}
        <CustomerAnimatedMetricCard
          title="Followers"
          value={subscriptionCount}
          icon={Users}
          description="Businesses you're following"
          color="blue"
          href="/dashboard/customer/subscriptions"
        />

        {/* Reviews Card */}
        <CustomerAnimatedMetricCard
          title="Rating"
          value={reviewCount}
          icon={Star}
          description="Reviews you've left for businesses"
          color="yellow"
          href="/dashboard/customer/reviews"
        />
      </div>
    </motion.div>
  );
}
