import { Metadata } from "next";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import SettingsPageClient from "./components/SettingsPageClient";

export const metadata: Metadata = {
  title: "Settings - Dukancard Business",
  robots: "noindex, nofollow",
};

// Helper function to clean phone number from auth format
function cleanPhoneFromAuth(phone: string | undefined): string | null {
  if (!phone) return null;

  // Remove country code prefix if present
  // Supabase stores phone as: 918458060663 (without + prefix)
  // We want to display: 8458060663
  if (phone.startsWith('91') && phone.length === 12) {
    return phone.substring(2);
  }

  // If it starts with +91, remove that
  if (phone.startsWith('+91') && phone.length === 13) {
    return phone.substring(3);
  }

  return phone;
}

export default async function SettingsPage() {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    // This should ideally be handled by middleware, but double-check
    redirect("/login?message=Authentication required");
  }

  // Settings page is exempt from address validation
  // Users can access settings even without complete address

  // Clean phone number from auth format
  const cleanedPhone = cleanPhoneFromAuth(user?.phone);

  // Determine user registration type
  const isGoogleUser = user.app_metadata?.provider === "google";
  const hasEmail = !!user.email;
  const hasPhone = !!cleanedPhone;

  // Determine registration method
  let registrationType: 'google' | 'email' | 'phone' = 'email'; // default

  if (isGoogleUser) {
    registrationType = 'google';
  } else if (hasPhone && !hasEmail) {
    registrationType = 'phone';
  } else if (hasEmail) {
    registrationType = 'email';
  }

  return (
    <SettingsPageClient
      currentEmail={user?.email}
      currentPhone={cleanedPhone}
      registrationType={registrationType}
    />
  );
}
