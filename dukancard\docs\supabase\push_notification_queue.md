# Push Notification Queue Table

## Overview
The `push_notification_queue` table serves as a reliable queue system for processing push notifications. It ensures notifications are delivered even if the initial attempt fails and provides a way to batch and manage notification frequency.

## Table Structure

```sql
CREATE TABLE push_notification_queue (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type TEXT NOT NULL,
    data JSONB NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);
```

## Indexes

```sql
-- Efficient querying of unprocessed notifications
CREATE INDEX idx_push_notification_queue_unprocessed 
ON push_notification_queue (user_id, processed, created_at) 
WHERE processed = FALSE;

-- Efficient querying by notification type
CREATE INDEX idx_push_notification_queue_type 
ON push_notification_queue (notification_type, created_at);

-- Cleanup index for old processed notifications
CREATE INDEX idx_push_notification_queue_cleanup 
ON push_notification_queue (processed, processed_at) 
WHERE processed = TRUE;
```

## Row Level Security (RLS)

```sql
-- Enable RLS
ALTER TABLE push_notification_queue ENABLE ROW LEVEL SECURITY;

-- Users can view their own push notifications
CREATE POLICY "Users can view their own push notifications" ON push_notification_queue
    FOR SELECT USING (auth.uid() = user_id);

-- Service role can manage all push notifications
CREATE POLICY "Service role can manage all push notifications" ON push_notification_queue
    FOR ALL USING (auth.role() = 'service_role');
```

## Fields Description

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `user_id` | UUID | Foreign key to auth.users.id (recipient of notification) |
| `notification_type` | TEXT | Type of notification ('business_activity', 'post_interaction', etc.) |
| `data` | JSONB | Notification payload data |
| `processed` | BOOLEAN | Whether the notification has been processed and sent |
| `created_at` | TIMESTAMP | When the notification was queued |
| `processed_at` | TIMESTAMP | When the notification was processed (null if not processed) |

## Notification Data Structure

### Business Activity Notifications
```json
{
  "type": "like|subscribe|rating|visit",
  "businessId": "uuid",
  "userId": "uuid", 
  "userName": "John Doe",
  "userType": "customer|business",
  "businessSlug": "my-business",
  "ratingValue": 5,
  "postId": "uuid"
}
```

## Usage Examples

### Queue a Business Activity Notification
```sql
INSERT INTO push_notification_queue (
    user_id,
    notification_type,
    data
) VALUES (
    'business-user-uuid',
    'business_activity',
    '{
        "type": "like",
        "businessId": "business-uuid",
        "userId": "customer-uuid",
        "userName": "John Doe",
        "userType": "customer",
        "businessSlug": "johns-cafe"
    }'::jsonb
);
```

### Get Unprocessed Notifications
```sql
SELECT * 
FROM push_notification_queue 
WHERE processed = FALSE 
ORDER BY created_at ASC 
LIMIT 10;
```

### Mark Notification as Processed
```sql
UPDATE push_notification_queue 
SET 
    processed = TRUE,
    processed_at = NOW()
WHERE id = 'notification-uuid';
```

### Clean Up Old Processed Notifications
```sql
DELETE FROM push_notification_queue 
WHERE processed = TRUE 
  AND processed_at < NOW() - INTERVAL '7 days';
```

## Database Triggers

### Automatic Queue Population
```sql
-- Trigger function to queue business activity notifications
CREATE OR REPLACE FUNCTION send_business_activity_push_notification()
RETURNS TRIGGER AS $$
DECLARE
    business_user_id UUID;
    user_profile RECORD;
    business_profile RECORD;
    notification_data JSONB;
BEGIN
    -- Get the business user ID from the activity
    business_user_id := NEW.business_profile_id;
    
    -- Get user profile information for personalization
    IF NEW.user_id IS NOT NULL THEN
        -- First try to get customer profile
        SELECT name, 'customer' as user_type INTO user_profile
        FROM customer_profiles 
        WHERE id = NEW.user_id;
        
        -- If not found, try business profile
        IF user_profile IS NULL THEN
            SELECT business_name as name, 'business' as user_type INTO user_profile
            FROM business_profiles 
            WHERE id = NEW.user_id;
        END IF;
    END IF;
    
    -- Get business profile for business slug
    SELECT business_slug INTO business_profile
    FROM business_profiles 
    WHERE id = business_user_id;
    
    -- Create notification data
    notification_data := jsonb_build_object(
        'type', NEW.activity_type,
        'businessId', business_user_id,
        'userId', NEW.user_id,
        'userName', COALESCE(user_profile.name, 'Someone'),
        'userType', COALESCE(user_profile.user_type, 'customer'),
        'businessSlug', business_profile.business_slug,
        'ratingValue', NEW.rating_value,
        'postId', NEW.post_id
    );
    
    -- Insert into notification queue
    INSERT INTO push_notification_queue (
        user_id,
        notification_type,
        data,
        created_at
    ) VALUES (
        business_user_id,
        'business_activity',
        notification_data,
        NOW()
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER trigger_business_activity_push_notification
    AFTER INSERT ON business_activities
    FOR EACH ROW
    EXECUTE FUNCTION send_business_activity_push_notification();
```

## Queue Processing

The queue is processed by the `PushNotificationQueueProcessor` service which:

1. **Fetches unprocessed notifications** in batches
2. **Applies frequency controls** to prevent spam
3. **Sends notifications** via Expo push service
4. **Marks as processed** when successfully sent
5. **Handles errors** gracefully with logging

## Best Practices

1. **Batch Processing**: Process notifications in batches for efficiency
2. **Error Handling**: Log errors but don't block processing of other notifications
3. **Cleanup**: Regularly clean up old processed notifications
4. **Monitoring**: Monitor queue size and processing times
5. **Retry Logic**: Implement retry logic for failed notifications

## Related Tables

- `push_tokens` - User push notification tokens
- `business_activities` - Source of business activity notifications
- `notification_analytics` - Analytics for notification performance
