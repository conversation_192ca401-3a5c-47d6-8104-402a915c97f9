import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert<PERSON>riangle, Heart } from 'lucide-react';
import BusinessLikesPageClient from './components/BusinessLikesPageClient';
import { Suspense } from 'react';
import { LikeListSkeleton } from '@/app/components/shared/likes';

// Import the fetch functions
import { fetchBusinessLikesReceived, fetchBusinessMyLikes } from './actions';

export const metadata: Metadata = {
  title: "Business Likes - Dukancard",
  robots: "noindex, nofollow",
};

export default async function BusinessLikesPage({
  searchParams
}: {
  searchParams: Promise<{ search?: string; page?: string; tab?: string }>
}) {
  // Properly await searchParams
  const { search, page: pageParam, tab } = await searchParams;
  const supabase = await createClient();
  const page = pageParam ? parseInt(pageParam) : 1;
  const searchTerm = search || "";
  const activeTab = tab === 'my-likes' ? 'my-likes' : 'likes-received';

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/login?message=Please log in to view your likes.');
  }

  try {
    // Fetch data for both tabs
    const [likesReceivedResult, myLikesResult] = await Promise.all([
      fetchBusinessLikesReceived(user.id, activeTab === 'likes-received' ? page : 1, 10),
      fetchBusinessMyLikes(user.id, activeTab === 'my-likes' ? page : 1, 10, activeTab === 'my-likes' ? searchTerm : "")
    ]);

    // Get counts for both tabs
    const [likesReceivedCount, myLikesCount] = await Promise.all([
      fetchBusinessLikesReceived(user.id, 1, 1).then(result => result.totalCount),
      fetchBusinessMyLikes(user.id, 1, 1, "").then(result => result.totalCount)
    ]);

    // Wrap in a div to ensure proper layout with the skeleton
    return (
      <div className="relative space-y-6 max-w-6xl mx-auto">
        <Suspense fallback={
          <div className="relative z-10">
            <Card className="border shadow-md bg-white dark:bg-black border-neutral-200 dark:border-neutral-800">
              <CardHeader className="pb-4 border-b border-neutral-100 dark:border-neutral-800">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                  <div className="p-2 rounded-lg bg-rose-100 dark:bg-rose-900/30 text-rose-500 dark:text-rose-400 self-start">
                    <Heart className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                      Business Likes
                    </h3>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                      Manage your likes and see who liked your business
                    </p>
                  </div>
                </div>

                {/* Search skeleton */}
                <div className="mt-4">
                  <Skeleton className="h-10 w-full rounded-md" />
                </div>
              </CardHeader>

              <CardContent className="pt-4">
                <LikeListSkeleton />
              </CardContent>
            </Card>
          </div>
        }>
          <BusinessLikesPageClient
            initialLikesReceived={likesReceivedResult.items}
            likesReceivedCount={likesReceivedCount}
            likesReceivedCurrentPage={likesReceivedResult.currentPage}
            initialMyLikes={myLikesResult.items}
            myLikesCount={myLikesCount}
            myLikesCurrentPage={myLikesResult.currentPage}
            searchTerm={searchTerm}
            activeTab={activeTab}
          />
        </Suspense>
      </div>
    );
  } catch (_error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Could not load likes data. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }
}
