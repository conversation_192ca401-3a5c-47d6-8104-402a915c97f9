"use client";

import { useState } from "react";
import { getSubscriptionDetails } from "@/lib/actions/subscription";
import { SubscriptionState, SubscriptionUpdateOptions } from "./types";

/**
 * Custom hook for managing subscription state
 * @returns Subscription state and setter functions
 */
export const useSubscriptionState = (): SubscriptionState => {
  const [isLoading, setIsLoading] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [cancelImmediately, setCancelImmediately] = useState(false);
  const [isWithinRefundWindow, setIsWithinRefundWindow] = useState(false);
  const [subscriptionDetails, setSubscriptionDetails] = useState<Record<string, unknown> | null>(null);
  const [showUpdateOptionsDialog, setShowUpdateOptionsDialog] = useState(false);
  const [updateOptions, setUpdateOptions] = useState<SubscriptionUpdateOptions | null>(null);
  const [showTrialWarningDialog, setShowTrialWarningDialog] = useState(false);
  const [trialSubscriptionId, setTrialSubscriptionId] = useState<string>("");

  return {
    isLoading,
    setIsLoading,
    showCancelDialog,
    setShowCancelDialog,
    cancelImmediately,
    setCancelImmediately,
    isWithinRefundWindow,
    setIsWithinRefundWindow,
    subscriptionDetails,
    setSubscriptionDetails,
    showUpdateOptionsDialog,
    setShowUpdateOptionsDialog,
    updateOptions,
    setUpdateOptions,
    showTrialWarningDialog,
    setShowTrialWarningDialog,
    trialSubscriptionId,
    setTrialSubscriptionId,
  };
};

/**
 * Custom hook for fetching subscription details
 * @returns Function to fetch subscription details
 */
export const useSubscriptionDetails = (
  setSubscriptionDetails: (_details: Record<string, unknown> | null) => void,
  setIsWithinRefundWindow: (_within: boolean) => void
) => {
  const fetchSubscriptionDetails = async () => {
    try {
      const result = await getSubscriptionDetails();

      if (result.success && result.data) {
        setSubscriptionDetails(result.data);
        // Check if isWithinRefundWindow exists in the data
        if ("isWithinRefundWindow" in result.data) {
          setIsWithinRefundWindow(!!result.data.isWithinRefundWindow);
        }
      }
    } catch (error) {
      console.error("Error fetching subscription details:", error);
    }
  };

  return fetchSubscriptionDetails;
};
