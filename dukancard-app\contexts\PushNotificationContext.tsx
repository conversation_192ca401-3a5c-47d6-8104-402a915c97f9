import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import * as Notifications from 'expo-notifications';
import { router } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import PushNotificationService, { PushNotificationData } from '@/lib/services/pushNotificationService';
import PushNotificationQueueProcessor from '@/lib/services/pushNotificationQueue';

interface PushNotificationContextType {
  isInitialized: boolean;
  pushToken: string | null;
  hasPermissions: boolean;
  initializePushNotifications: () => Promise<void>;
  requestPermissions: () => Promise<boolean>;
  openSettings: () => Promise<void>;
}

const PushNotificationContext = createContext<PushNotificationContextType | undefined>(undefined);

interface PushNotificationProviderProps {
  children: React.ReactNode;
}

export const PushNotificationProvider: React.FC<PushNotificationProviderProps> = ({ children }) => {
  const { user, profileStatus } = useAuth();
  const [isInitialized, setIsInitialized] = useState(false);
  const [pushToken, setPushToken] = useState<string | null>(null);
  const [hasPermissions, setHasPermissions] = useState(false);
  const appState = useRef(AppState.currentState);
  const notificationListener = useRef<Notifications.EventSubscription | null>(null);
  const responseListener = useRef<Notifications.EventSubscription | null>(null);

  // Initialize push notifications when user is authenticated
  useEffect(() => {
    if (user && !isInitialized) {
      initializePushNotifications();
    }
  }, [user, isInitialized]);

  // Handle app state changes
  useEffect(() => {
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  // Set up notification listeners
  useEffect(() => {
    if (isInitialized) {
      setupNotificationListeners();
    }

    return () => {
      if (notificationListener.current) {
        notificationListener.current.remove();
      }
      if (responseListener.current) {
        responseListener.current.remove();
      }
    };
  }, [isInitialized]);

  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
      // App has come to the foreground
      checkPermissions();

      // Trigger queue processing when business user opens app
      if (user && profileStatus?.roleStatus?.hasBusinessProfile) {
        console.log('App came to foreground, triggering queue processing');
        PushNotificationQueueProcessor.triggerProcessing();
      }
    }
    appState.current = nextAppState;
  };

  const initializePushNotifications = async () => {
    try {
      // Initializing push notifications...
      
      // Initialize the service
      const token = await PushNotificationService.initialize();
      setPushToken(token);

      if (token && user) {
        // Determine user type
        const userType = profileStatus?.roleStatus?.hasBusinessProfile ? 'business' : 'customer';

        // Store token in database
        await PushNotificationService.storePushToken(user.id, userType);

        // Start queue processor for business users
        if (userType === 'business') {
          console.log('Starting push notification queue processor for business user');
          PushNotificationQueueProcessor.startProcessing();
        }
      }

      // Check permissions
      const permissions = await PushNotificationService.areNotificationsEnabled();
      setHasPermissions(permissions);

      setIsInitialized(true);
      // Push notifications initialized successfully
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
    }
  };

  const requestPermissions = async (): Promise<boolean> => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      const granted = status === 'granted';
      setHasPermissions(granted);
      
      if (granted && !pushToken) {
        // Re-initialize if we got permissions
        await initializePushNotifications();
      }
      
      return granted;
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return false;
    }
  };

  const checkPermissions = async () => {
    try {
      const permissions = await PushNotificationService.areNotificationsEnabled();
      setHasPermissions(permissions);
    } catch (error) {
      console.error('Error checking permissions:', error);
    }
  };

  const openSettings = async () => {
    try {
      await PushNotificationService.openNotificationSettings();
    } catch (error) {
      console.error('Error opening settings:', error);
    }
  };

  const setupNotificationListeners = () => {
    // Listen for notifications received while app is foregrounded
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
      
      // Handle foreground notification display
      // You can customize this based on notification type
      const data = notification.request.content.data as PushNotificationData;
      
      if (data?.type === 'business_activity') {
        // Handle business activity notifications
        console.log('Business activity notification received');
      } else if (data?.type === 'post_interaction') {
        // Handle post interaction notifications
        console.log('Post interaction notification received');
      }
    });

    // Listen for notification responses (when user taps notification)
    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response received:', response);
      
      const data = response.notification.request.content.data as PushNotificationData;
      handleNotificationResponse(data);
    });

    // Handle initial notification (app opened from notification)
    Notifications.getLastNotificationResponseAsync().then(response => {
      if (response?.notification) {
        const data = response.notification.request.content.data as PushNotificationData;
        handleNotificationResponse(data);
      }
    });
  };

  const handleNotificationResponse = (data: PushNotificationData) => {
    try {
      // Handle deep linking based on notification data
      if (data?.url) {
        // Navigate to specific URL
        router.push(data.url as any);
      } else if (data?.type === 'business_activity' && data?.businessId) {
        // Navigate to business dashboard
        router.push('/(dashboard)/business/');
      } else if (data?.type === 'post_interaction' && data?.postId) {
        // Navigate to specific post
        router.push(`/post/${data.postId}` as any);
      } else {
        // Default navigation based on user type
        if (profileStatus?.roleStatus?.hasBusinessProfile) {
          router.push('/(dashboard)/business/');
        } else {
          router.push('/(dashboard)/customer/');
        }
      }
    } catch (error) {
      console.error('Error handling notification response:', error);
    }
  };

  // Clean up when user logs out
  useEffect(() => {
    if (!user && pushToken) {
      // User logged out, clean up
      setPushToken(null);
      setIsInitialized(false);
      setHasPermissions(false);

      // Stop queue processor
      PushNotificationQueueProcessor.stopProcessing();
    }
  }, [user, pushToken]);

  const value: PushNotificationContextType = {
    isInitialized,
    pushToken,
    hasPermissions,
    initializePushNotifications,
    requestPermissions,
    openSettings,
  };

  return (
    <PushNotificationContext.Provider value={value}>
      {children}
    </PushNotificationContext.Provider>
  );
};

export const usePushNotifications = (): PushNotificationContextType => {
  const context = useContext(PushNotificationContext);
  if (context === undefined) {
    throw new Error('usePushNotifications must be used within a PushNotificationProvider');
  }
  return context;
};
