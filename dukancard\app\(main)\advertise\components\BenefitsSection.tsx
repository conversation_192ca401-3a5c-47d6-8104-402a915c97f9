"use client";

import { Card } from "@/components/ui/card";
import { Target, Map, Users, TrendingUp, Zap, Globe } from "lucide-react";
import SectionBackground from "@/app/(main)/components/SectionBackground";
import { useInView } from "react-intersection-observer";
import { motion } from "framer-motion";

interface BenefitCardProps {
  icon: React.ElementType;
  title: string;
  description: string;
  index: number;
}

function BenefitCard({ icon: Icon, title, description, index }: BenefitCardProps) {
  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  // Animation variants for the card
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        delay: index * 0.1
      }
    }
  };

  // Animation for the icon background
  const iconBackgroundVariants = {
    hidden: { scale: 0.8, opacity: 0.5 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.3,
        delay: index * 0.1 + 0.2
      }
    }
  };

  return (
    <motion.div
      ref={ref}
      className="h-full"
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={cardVariants}
    >
      <Card className="bg-white/90 dark:bg-black/40 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-800/50 p-6 h-full rounded-xl group relative overflow-hidden">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent to-neutral-100/50 dark:to-neutral-900/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Icon with animated background */}
        <div className="relative mb-4">
          <motion.div
            className="absolute -inset-2 bg-[var(--brand-gold)]/10 rounded-full blur-md"
            variants={iconBackgroundVariants}
          ></motion.div>
          <div className="relative bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 p-3 rounded-full inline-flex">
            <Icon className="h-6 w-6 text-[var(--brand-gold)]" />
          </div>
        </div>

        <h3 className="text-xl font-semibold text-foreground mb-2 relative z-10">{title}</h3>
        <p className="text-muted-foreground text-sm relative z-10">{description}</p>
      </Card>
    </motion.div>
  );
}

export default function BenefitsSection() {
  const benefits = [
    {
      icon: Target,
      title: "Locality-Based Targeting",
      description: "Target specific pincodes and localities to reach customers in your area of operation.",
    },
    {
      icon: Map,
      title: "Geographic Flexibility",
      description: "Advertise in multiple locations across India based on your business needs.",
    },
    {
      icon: Users,
      title: "Reach Engaged Users",
      description: "Connect with users actively looking for products and services in their area.",
    },
    {
      icon: TrendingUp,
      title: "Increase Visibility",
      description: "Boost your brand awareness and visibility among potential customers.",
    },
    {
      icon: Zap,
      title: "Quick Implementation",
      description: "Get your ads up and running quickly with our streamlined process.",
    },
    {
      icon: Globe,
      title: "Digital Presence",
      description: "Enhance your digital footprint and reach customers on their mobile devices.",
    },
  ];

  const { ref: sectionRef, inView: sectionInView } = useInView({
    threshold: 0.2,
    triggerOnce: true
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.2,
    triggerOnce: true
  });

  // Animation variants for the section
  const sectionVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  // Animation variants for the title
  const titleVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: 0.2
      }
    }
  };

  return (
    <motion.section
      ref={sectionRef}
      className="py-16 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto relative"
      initial="hidden"
      animate={sectionInView ? "visible" : "hidden"}
      variants={sectionVariants}
    >
      {/* Enhanced background with SectionBackground component */}
      <div className="absolute inset-0 -z-10">
        <SectionBackground variant="gold" intensity="low" />
      </div>

      <motion.div
        ref={titleRef}
        className="text-center mb-16"
        variants={titleVariants}
        initial="hidden"
        animate={titleInView ? "visible" : "hidden"}
      >
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Why Advertise with{" "}
          <span className="text-[var(--brand-gold)]">Dukancard</span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Our platform offers unique advantages for businesses looking to reach customers in specific localities.
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
        {benefits.map((benefit, index) => (
          <BenefitCard
            key={benefit.title}
            icon={benefit.icon}
            title={benefit.title}
            description={benefit.description}
            index={index}
          />
        ))}
      </div>
    </motion.section>
  );
}
