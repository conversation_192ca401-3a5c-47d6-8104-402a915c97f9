import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Maximize, Trash2 } from "lucide-react";
import { GalleryImage } from "../types";

interface StaticImageItemProps {
  image: GalleryImage;
  onViewImage: (_url: string) => void;
  onDeleteImage: (_image: GalleryImage) => void;
}

export default function StaticImageItem({
  image,
  onViewImage,
  onDeleteImage,
}: StaticImageItemProps) {
  return (
    <div className="relative group">
      <div className="aspect-square relative overflow-hidden rounded-lg border shadow-sm hover:shadow-md transition-all duration-300">
        <Image
          src={image.url}
          alt="Gallery image"
          fill
          className="object-cover transition-transform duration-500 group-hover:scale-110"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />

        {/* Overlay with actions - same as sortable version but without drag handle */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 pointer-events-none">
          <div className="absolute bottom-3 right-3 flex space-x-2 pointer-events-auto">
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 bg-white/20 backdrop-blur-sm text-white shadow-md hover:bg-white/30"
              onClick={(e) => {
                e.stopPropagation();
                onViewImage(image.url);
              }}
            >
              <Maximize className="h-4 w-4" />
            </Button>
            <Button
              variant="destructive"
              size="icon"
              className="h-8 w-8 bg-red-500/80 hover:bg-red-600/90 text-white shadow-md"
              onClick={(e) => {
                e.stopPropagation();
                onDeleteImage(image);
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
