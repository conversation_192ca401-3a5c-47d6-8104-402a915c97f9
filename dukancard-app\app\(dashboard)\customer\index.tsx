import { DashboardLayout } from '@/components/shared/layout/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import React, { useEffect, useState, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { createCustomerIndexStyles } from '@/styles/dashboard/customer/customer-index-styles';
import { UnifiedFeedList } from '@/components/feed/UnifiedFeedList';
import { getUnifiedFeedPostsWithAuthors, UnifiedPost } from '@/lib/actions/posts/unifiedFeed';
import { FeedFilterType } from '@/lib/types/posts';
import { LoadingSpinner } from '@/components/shared/ui/LoadingSpinner';
import { ErrorState } from '@/components/ui/ErrorState';
import { PostSkeleton } from '@/components/feed/PostSkeleton';
import { handleNetworkError, logError } from '@/lib/utils/errorHandling';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { supabase } from '@/lib/supabase';
import { offlineService } from '@/lib/services/offlineService';

export default function CustomerFeedScreen() {
  const { user, profileStatus } = useAuth();
  const colorScheme = useColorScheme();
  const [initialPosts, setInitialPosts] = useState<UnifiedPost[]>([]);
  const [initialHasMore, setInitialHasMore] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);
  const [userLocation, setUserLocation] = useState<{
    citySlug?: string;
    stateSlug?: string;
    localitySlug?: string;
    pincode?: string;
  }>({});

  // Get customer name from profile
  const customerName = profileStatus?.roleStatus?.hasCustomerProfile
    ? user?.user_metadata?.name || user?.email?.split('@')[0] || 'Valued Customer'
    : 'Valued Customer';

  // Theme colors - use pure black/white for feed
  const backgroundColor = colorScheme === 'dark' ? '#000000' : '#FFFFFF';

  // Load user location context like Next.js version
  const loadUserLocation = useCallback(async () => {
    if (!user) return;

    try {
      // Get the user's city from subscribed businesses (fallback method)
      const { data: subscriptions } = await supabase
        .from('subscriptions')
        .select(`
          business_profiles (
            city_slug,
            state_slug,
            locality_slug,
            pincode
          )
        `)
        .eq('user_id', user.id)
        .limit(1);

      let locationFromSubscriptions;
      if (subscriptions && subscriptions.length > 0 && subscriptions[0].business_profiles) {
        const profile = subscriptions[0].business_profiles;
        if (typeof profile === 'object' && profile !== null) {
          locationFromSubscriptions = {
            citySlug: 'city_slug' in profile ? profile.city_slug as string : undefined,
            stateSlug: 'state_slug' in profile ? profile.state_slug as string : undefined,
            localitySlug: 'locality_slug' in profile ? profile.locality_slug as string : undefined,
            pincode: 'pincode' in profile ? profile.pincode as string : undefined,
          };
        }
      }

      // Try to get user's location from both customer and business profiles
      const [customerProfile, businessProfile] = await Promise.all([
        supabase
          .from('customer_profiles')
          .select('city_slug, state_slug, locality_slug, pincode')
          .eq('id', user.id)
          .single(),
        supabase
          .from('business_profiles')
          .select('city_slug, state_slug, locality_slug, pincode')
          .eq('id', user.id)
          .single()
      ]);

      // Use customer profile first, then business profile, then subscription location
      const profileLocationData = customerProfile.data || businessProfile.data;

      if (profileLocationData) {
        setUserLocation({
          citySlug: profileLocationData.city_slug,
          stateSlug: profileLocationData.state_slug,
          localitySlug: profileLocationData.locality_slug,
          pincode: profileLocationData.pincode,
        });
      } else if (locationFromSubscriptions) {
        setUserLocation(locationFromSubscriptions);
      }
    } catch (err) {
      console.error('Error loading user location:', err);
      // Continue without location context
    }
  }, [user]);

  const loadInitialFeed = useCallback(async () => {
    if (!user) return;

    try {
      setError(null);

      // Use smart feed as the default filter
      const initialFilter: FeedFilterType = 'smart';

      // Create location context for cache key
      const locationContext = {
        citySlug: userLocation.citySlug,
        stateSlug: userLocation.stateSlug,
        localitySlug: userLocation.localitySlug,
        pincode: userLocation.pincode,
      };

      // Try to load from cache first to avoid showing skeleton
      try {
        const cachedPosts = await offlineService.getCachedFeedPosts(initialFilter, locationContext);
        if (cachedPosts && cachedPosts.length > 0) {
          setInitialPosts(cachedPosts);
          setInitialHasMore(true); // Assume more posts might be available
          setLoading(false);

          // Load fresh data in background without showing loading state
          const initialFeedResult = await getUnifiedFeedPostsWithAuthors({
            filter: initialFilter,
            page: 1,
            limit: 10,
            city_slug: userLocation.citySlug,
            state_slug: userLocation.stateSlug,
            locality_slug: userLocation.localitySlug,
            pincode: userLocation.pincode,
          });

          if (initialFeedResult.success && initialFeedResult.data) {
            setInitialPosts(initialFeedResult.data.items);
            setInitialHasMore(initialFeedResult.data.hasMore);
            // Cache the fresh data
            await offlineService.cacheFeedPosts(initialFeedResult.data.items, initialFilter, locationContext);
          }
          return;
        }
      } catch (cacheError) {
        console.error('Error loading cached feed data:', cacheError);
      }

      // No cache available, show loading and fetch fresh data
      setLoading(true);

      // Get initial posts using the unified smart feed algorithm with user location
      const initialFeedResult = await getUnifiedFeedPostsWithAuthors({
        filter: initialFilter,
        page: 1,
        limit: 10,
        city_slug: userLocation.citySlug,
        state_slug: userLocation.stateSlug,
        locality_slug: userLocation.localitySlug,
        pincode: userLocation.pincode,
      });

      if (initialFeedResult.success && initialFeedResult.data) {
        setInitialPosts(initialFeedResult.data.items);
        setInitialHasMore(initialFeedResult.data.hasMore);
        // Cache the fresh data
        await offlineService.cacheFeedPosts(initialFeedResult.data.items, initialFilter, locationContext);
      } else {
        const appError = handleNetworkError(new Error(initialFeedResult.error || 'Failed to load feed'));
        setError(appError);
        logError(appError, 'CustomerFeedScreen.loadInitialFeed');
      }
    } catch (err) {
      console.error('Error loading initial feed:', err);
      const appError = handleNetworkError(err);
      setError(appError);
      logError(appError, 'CustomerFeedScreen.loadInitialFeed');
    } finally {
      setLoading(false);
    }
  }, [user, userLocation]);

  useEffect(() => {
    loadUserLocation();
  }, [loadUserLocation]);

  useEffect(() => {
    if (user) {
      loadInitialFeed();
    }
  }, [loadInitialFeed, user]);

  if (loading) {
    return (
      <DashboardLayout
        userName={customerName}
        showNotifications={true}
      >
        <View style={[styles.loadingContainer, { backgroundColor }]}>
          {/* Show skeleton loaders for better UX */}
          <PostSkeleton index={0} showImage={true} />
          <PostSkeleton index={1} showImage={false} />
          <PostSkeleton index={2} showImage={true} showProducts={true} />
        </View>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout
        userName={customerName}
        showNotifications={true}
      >
        <ErrorState
          type={error.type || 'generic'}
          title={error.title}
          message={error.message}
          onRetry={loadInitialFeed}
          showRetry={true}
        />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      userName={customerName}
      showNotifications={true}
      fullWidth={true}
    >
      <UnifiedFeedList
        initialPosts={initialPosts}
        initialHasMore={initialHasMore}
        userName={customerName}
        userType="customer"
        citySlug={userLocation.citySlug}
        stateSlug={userLocation.stateSlug}
        localitySlug={userLocation.localitySlug}
        pincode={userLocation.pincode}
      />
    </DashboardLayout>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
  },
});
