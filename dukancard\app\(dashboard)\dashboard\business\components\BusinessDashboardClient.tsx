"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  LayoutDashboard,
} from "lucide-react";
import DashboardOverviewClient from "./DashboardOverviewClient";
import AnimatedSubscriptionStatus from "./AnimatedSubscriptionStatus";
import EnhancedQuickActions from "./EnhancedQuickActions";
import RecentActivities from "./RecentActivities";
import BusinessStatusAlert from "./BusinessStatusAlert";
import { PricingPlan } from "@/lib/PricingPlans";
import { getUnreadActivitiesCount } from "@/lib/actions/activities";

interface BusinessDashboardClientProps {
  initialProfile: {
    business_name: string;
    business_slug: string;
    plan_id: string | null;
    plan_cycle: string | null;
    has_active_subscription: boolean | null;
    trial_end_date: string | null;
    total_likes: number;
    total_subscriptions: number;
    average_rating: number;
    logo_url: string | null;
    title: string | null;
    status: string; // "online" or "offline"
  };
  userId: string;
  subscriptionStatus: "active" | "trial" | "inactive";
  planDetails: PricingPlan | undefined;
  subscription?: {
    subscription_status: string | null;
    plan_id: string | null;
    plan_cycle: string | null;
  } | null;
}

export default function BusinessDashboardClient({
  initialProfile,
  userId,
  subscriptionStatus,
  planDetails,
  subscription,
}: BusinessDashboardClientProps) {
  const [unreadActivitiesCount, setUnreadActivitiesCount] = useState(0);

  /**
   * Fetch Unread Activities Count
   *
   * This effect fetches the initial unread activities count and sets up an interval
   * to refresh it every minute. This ensures the dashboard shows accurate counts
   * even if the realtime subscription misses any events.
   *
   * Note: For realtime updates of the activities count, you need to enable realtime
   * for the business_activities table in Supabase:
   * 1. Go to Supabase Dashboard > Database > Replication
   * 2. Find the "business_activities" table in the list
   * 3. Enable realtime by toggling it on
   */
  useEffect(() => {
    const fetchUnreadCount = async () => {
      const { count } = await getUnreadActivitiesCount(userId);
      setUnreadActivitiesCount(count);
    };

    fetchUnreadCount();

    // Set up interval to refresh count every minute
    const intervalId = setInterval(fetchUnreadCount, 60000);

    return () => clearInterval(intervalId);
  }, [userId]);

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-6"
    >
      <motion.div variants={itemVariants} className="mb-6">
        {/* Main card container */}
        <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-5 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg relative overflow-hidden">
          {/* Content with relative positioning */}
          <div className="relative z-10">
            {/* Dashboard Overview Section */}
            <div className="flex items-center justify-between gap-2 mb-4 pb-3 border-b border-neutral-100 dark:border-neutral-800">
              <div className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-primary/10 text-primary">
                  <LayoutDashboard className="w-4 sm:w-5 h-4 sm:h-5" />
                </div>
                <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Business Dashboard
                </h3>
              </div>
            </div>

            {/* Business Status Alert - Show only when status is offline */}
            {initialProfile.status === "offline" && <BusinessStatusAlert />}

            {/* Dashboard Overview Client */}
            <div className="mb-6">
              <DashboardOverviewClient
                initialProfile={initialProfile}
                userId={userId}
                userPlan={initialProfile.plan_id}
              />
            </div>

            {/* Main Dashboard Content Area - No longer blurred for inactive users */}
            <div className="transition-opacity duration-300">

              {/* Subscription Status and Quick Actions in a single row */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-6">
                {/* Animated Subscription Status */}
                <AnimatedSubscriptionStatus
                  subscriptionStatus={subscriptionStatus}
                  planDetails={planDetails}
                  trialEndDate={initialProfile.trial_end_date}
                  planCycle={initialProfile.plan_cycle}
                  subscription={subscription}
                />

                {/* Enhanced Quick Actions */}
                <EnhancedQuickActions userPlan={initialProfile.plan_id} />
              </div>

              {/* Recent Activities */}
              <div className="grid grid-cols-1 gap-4 sm:gap-6">
                <RecentActivities
                  businessProfileId={userId}
                  unreadCount={unreadActivitiesCount}
                />
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
