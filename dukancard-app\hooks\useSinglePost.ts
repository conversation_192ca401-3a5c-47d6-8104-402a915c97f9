import { useState, useEffect, useCallback, useRef } from 'react';
import { fetchSinglePost, SinglePostResponse } from '@/lib/actions/posts/fetchSinglePost';
import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';

// Simple in-memory cache for posts
const postCache = new Map<string, { data: UnifiedPost; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Hook state interface
interface UseSinglePostState {
  post: UnifiedPost | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook for managing single post data in React Native
 * Handles loading states, error handling, and data caching
 */
export function useSinglePost(postId: string): UseSinglePostState {
  const [post, setPost] = useState<UnifiedPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const fetchPost = useCallback(async (forceRefresh = false) => {
    if (!postId) {
      setError('Invalid post ID');
      setLoading(false);
      return;
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Check cache first
    if (!forceRefresh) {
      const cached = postCache.get(postId);
      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        setPost(cached.data);
        setLoading(false);
        setError(null);
        return;
      }
    }

    try {
      setLoading(true);
      setError(null);

      abortControllerRef.current = new AbortController();
      const result: SinglePostResponse = await fetchSinglePost(postId);

      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      if (result.success && result.data) {
        setPost(result.data);
        setError(null);

        // Cache the result
        postCache.set(postId, {
          data: result.data,
          timestamp: Date.now()
        });
      } else {
        setPost(null);
        setError(result.message || 'Failed to fetch post');
      }
    } catch (err) {
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      console.error('Error in useSinglePost:', err);
      setPost(null);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      if (!abortControllerRef.current?.signal.aborted) {
        setLoading(false);
      }
    }
  }, [postId]);

  const refetch = useCallback(async () => {
    await fetchPost(true); // Force refresh on manual refetch
  }, [fetchPost]);

  useEffect(() => {
    fetchPost();

    // Cleanup on unmount
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchPost]);

  return {
    post,
    loading,
    error,
    refetch,
  };
}
