"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";

interface AnimatedButtonProps {
  children: React.ReactNode;
  className?: string;
  isLoading?: boolean;
  disabled?: boolean;
  type?: "button" | "submit" | "reset";
}

export default function AnimatedButton({
  children,
  className = "",
  isLoading = false,
  disabled,
  ...props
}: AnimatedButtonProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const isDisabled = disabled || isLoading;

  return (
    <div className="relative">
      {/* Button glow effect */}
      {isClient && !isDisabled && (
        <motion.div
          className="absolute -inset-1 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-full blur-md"
          initial={{ opacity: 0.3 }}
          animate={{ opacity: 0.6 }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
      )}

      <motion.div
        className={isDisabled ? "opacity-70" : ""}
        whileHover={!isDisabled ? { scale: 1.02 } : {}}
        whileTap={!isDisabled ? { scale: 0.98 } : {}}
      >
        <Button
          className={`cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base relative overflow-hidden ${className}`}
          disabled={isDisabled}
          {...props}
        >
          {/* Shimmer effect */}
          {isClient && !isDisabled && (
            <motion.div
              className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none"
              initial={{ x: "-100%" }}
              animate={{ x: "100%" }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          )}

          {children}
        </Button>
      </motion.div>
    </div>
  );
}
