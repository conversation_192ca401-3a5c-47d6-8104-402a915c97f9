import React, { useState, useCallback, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Image,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Search, Package, X, Check } from 'lucide-react-native';
import { searchBusinessProducts, ProductData } from '@/lib/actions/products';

interface ProductSelectorModalProps {
  visible: boolean;
  selectedProductIds: string[];
  onProductsChange: (productIds: string[]) => void;
  onClose: () => void;
}

interface ProductItemProps {
  product: ProductData;
  isSelected: boolean;
  onToggle: () => void;
  disabled?: boolean;
}

const ProductItem: React.FC<ProductItemProps> = ({ product, isSelected, onToggle, disabled }) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const mutedTextColor = isDark ? '#9CA3AF' : '#6B7280';
  const borderColor = isDark ? '#333333' : '#E5E7EB';
  const primaryColor = '#D4AF37';

  const formatPrice = (price: number | null | undefined) => {
    if (price === null || price === undefined) return 'N/A';
    return `₹${price.toLocaleString('en-IN')}`;
  };

  return (
    <TouchableOpacity
      style={[
        styles.productItem,
        { borderColor },
        disabled && styles.productItemDisabled
      ]}
      onPress={onToggle}
      disabled={disabled}
      activeOpacity={0.7}
    >
      {/* Product Image */}
      <View style={[styles.productImage, { backgroundColor: borderColor }]}>
        {product.image_url ? (
          <Image source={{ uri: product.image_url }} style={styles.productImageContent} />
        ) : (
          <Package size={20} color={mutedTextColor} />
        )}
      </View>

      {/* Product Details */}
      <View style={styles.productDetails}>
        <Text style={[styles.productName, { color: textColor }]} numberOfLines={1}>
          {product.name}
        </Text>
        <View style={styles.priceContainer}>
          {product.discounted_price ? (
            <>
              <Text style={[styles.discountedPrice, { color: primaryColor }]}>
                {formatPrice(product.discounted_price)}
              </Text>
              <Text style={[styles.originalPrice, { color: mutedTextColor }]}>
                {formatPrice(product.base_price)}
              </Text>
            </>
          ) : (
            <Text style={[styles.price, { color: textColor }]}>
              {formatPrice(product.base_price)}
            </Text>
          )}
        </View>
      </View>

      {/* Check Icon */}
      <View style={styles.checkContainer}>
        {isSelected && <Check size={16} color={primaryColor} />}
      </View>
    </TouchableOpacity>
  );
};

export function ProductSelectorModal({ 
  visible, 
  selectedProductIds, 
  onProductsChange, 
  onClose 
}: ProductSelectorModalProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<ProductData[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const searchTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Theme colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const cardColor = isDark ? '#1A1A1A' : '#F8F9FA';
  const borderColor = isDark ? '#333333' : '#E5E7EB';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const mutedTextColor = isDark ? '#9CA3AF' : '#6B7280';
  const primaryColor = '#D4AF37';

  // Search products with debouncing
  const searchProducts = useCallback(async (query: string) => {
    if (query.length < 2) {
      setSearchResults([]);
      setHasSearched(false);
      return;
    }

    setIsSearching(true);
    try {
      const result = await searchBusinessProducts(query);
      if (result.success && result.data) {
        setSearchResults(result.data);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Error searching products:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
      setHasSearched(true);
    }
  }, []);

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      searchProducts(text);
    }, 300);
  };

  const toggleProduct = (product: ProductData) => {
    const isSelected = selectedProductIds.includes(product.id);
    let newSelectedIds: string[];

    if (isSelected) {
      newSelectedIds = selectedProductIds.filter(id => id !== product.id);
    } else {
      if (selectedProductIds.length >= 5) {
        Alert.alert('Limit Reached', 'You can only select up to 5 products per post.');
        return;
      }
      newSelectedIds = [...selectedProductIds, product.id];
    }

    onProductsChange(newSelectedIds);
  };

  const renderSearchResult = ({ item }: { item: ProductData }) => (
    <ProductItem
      product={item}
      isSelected={selectedProductIds.includes(item.id)}
      onToggle={() => toggleProduct(item)}
      disabled={selectedProductIds.length >= 5 && !selectedProductIds.includes(item.id)}
    />
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor }]}>
        {/* Header */}
        <View style={[styles.modalHeader, { borderBottomColor: borderColor }]}>
          <Text style={[styles.modalTitle, { color: textColor }]}>Select Products</Text>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={textColor} />
          </TouchableOpacity>
        </View>

        {/* Search Input */}
        <View style={[styles.searchContainer, { backgroundColor: cardColor, borderColor }]}>
          <Search size={16} color={mutedTextColor} />
          <TextInput
            style={[styles.searchInput, { color: textColor }]}
            placeholder="Search your products..."
            placeholderTextColor={mutedTextColor}
            value={searchQuery}
            onChangeText={handleSearchChange}
            autoFocus
          />
        </View>

        {/* Search Results */}
        <View style={styles.resultsContainer}>
          {isSearching ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={primaryColor} />
              <Text style={[styles.loadingText, { color: mutedTextColor }]}>
                Searching products...
              </Text>
            </View>
          ) : searchQuery.length < 2 ? (
            <View style={styles.emptyContainer}>
              <Package size={48} color={mutedTextColor} />
              <Text style={[styles.emptyText, { color: mutedTextColor }]}>
                Type at least 2 characters to search
              </Text>
            </View>
          ) : searchResults.length === 0 && hasSearched ? (
            <View style={styles.emptyContainer}>
              <Package size={48} color={mutedTextColor} />
              <Text style={[styles.emptyText, { color: mutedTextColor }]}>
                No products found
              </Text>
              <Text style={[styles.emptySubtext, { color: mutedTextColor }]}>
                Try a different search term
              </Text>
            </View>
          ) : (
            <FlatList
              data={searchResults}
              renderItem={renderSearchResult}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.searchResultsList}
            />
          )}
        </View>

        {/* Footer */}
        <View style={[styles.modalFooter, { borderTopColor: borderColor }]}>
          <Text style={[styles.footerText, { color: mutedTextColor }]}>
            {selectedProductIds.length}/5 products selected
          </Text>
          {selectedProductIds.length >= 5 && (
            <Text style={[styles.limitText, { color: '#EF4444' }]}>
              Maximum limit reached
            </Text>
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  resultsContainer: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  loadingText: {
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  searchResultsList: {
    gap: 8,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 12,
  },
  productItemDisabled: {
    opacity: 0.5,
  },
  productImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  productImageContent: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  productDetails: {
    flex: 1,
    gap: 4,
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  discountedPrice: {
    fontSize: 12,
    fontWeight: '600',
  },
  originalPrice: {
    fontSize: 12,
    textDecorationLine: 'line-through',
  },
  price: {
    fontSize: 12,
    fontWeight: '500',
  },
  checkContainer: {
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  footerText: {
    fontSize: 14,
  },
  limitText: {
    fontSize: 12,
    fontWeight: '500',
  },
});
