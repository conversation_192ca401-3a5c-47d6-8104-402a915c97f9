"use client";

import { motion } from "framer-motion";
import { PlusCircle, ShoppingBag } from "lucide-react";
import Link from "next/link";
import EnhancedGlowButton from "@/app/(dashboard)/dashboard/business/plan/components/EnhancedGlowButton";
import { itemVariants } from "../../types";
import { useProducts } from "../../context/ProductsContext";

export default function ProductHeader() {
  const {
    canAddMore,
    isPending,
    isLoading
  } = useProducts();

  return (
    <motion.div
      className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 pt-3 sm:pt-4 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800 relative z-10"
      variants={itemVariants}
    >
      <div className="flex items-center gap-2 sm:gap-3">
        <div className="p-2 rounded-lg bg-primary/10 text-primary">
          <ShoppingBag className="w-4 sm:w-5 h-4 sm:h-5" />
        </div>
        <div>
          <h1 className="text-lg sm:text-xl md:text-2xl font-semibold text-neutral-800 dark:text-neutral-100">
            Product Management
          </h1>
          <p className="text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 mt-0.5">
            Organize and manage your products and services inventory
          </p>
        </div>
      </div>

      <div className="w-full sm:w-auto">
        <Link href="/dashboard/business/products/add">
          <EnhancedGlowButton
            disabled={!canAddMore || isPending || isLoading}
            className="font-medium w-full sm:w-auto"
            roundedFull
            size="sm"
          >
            <div className="flex items-center justify-center">
              <PlusCircle className="mr-1.5 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4" />
              <span className="whitespace-nowrap">Add New Item</span>
            </div>
          </EnhancedGlowButton>
        </Link>
      </div>
    </motion.div>
  );
}
