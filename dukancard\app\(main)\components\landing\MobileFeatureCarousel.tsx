"use client";

import React, { useState, useEffect, useRef } from "react";
import { ChevronLeft, ChevronRight, LucideIcon } from "lucide-react";
import { Button } from "@/components/ui/button";

interface Feature {
  icon: LucideIcon;
  title: string;
  description: string;
}

interface MobileFeatureCarouselProps {
  features: Feature[];
}

export default function MobileFeatureCarousel({ features }: MobileFeatureCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const autoPlayTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Simple auto-play functionality with reduced frequency
  useEffect(() => {
    autoPlayTimerRef.current = setTimeout(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % features.length);
    }, 8000); // Longer interval to reduce CPU usage

    return () => {
      if (autoPlayTimerRef.current) {
        clearTimeout(autoPlayTimerRef.current);
      }
    };
  }, [currentIndex, features.length]);

  const handleManualNavigation = (newIndex: number) => {
    if (autoPlayTimerRef.current) {
      clearTimeout(autoPlayTimerRef.current);
    }
    setCurrentIndex(newIndex);
  };

  const nextSlide = () => {
    const newIndex = (currentIndex + 1) % features.length;
    handleManualNavigation(newIndex);
  };

  const prevSlide = () => {
    const newIndex = (currentIndex - 1 + features.length) % features.length;
    handleManualNavigation(newIndex);
  };

  return (
    <div className="relative w-full overflow-hidden py-4">
      {/* Carousel container */}
      <div className="relative w-full h-[280px] overflow-hidden rounded-xl">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full max-w-xs mx-auto p-6 bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200 dark:border-neutral-800 shadow-md">
            <div className="flex flex-col items-center text-center">
              {/* Icon */}
              <div className="p-4 bg-[var(--brand-gold)]/10 rounded-full mb-4 relative">
                {React.createElement(features[currentIndex].icon, {
                  className: "h-8 w-8 text-[var(--brand-gold)]",
                })}
              </div>

              {/* Title */}
              <h3 className="text-xl font-bold mb-2">
                {features[currentIndex].title}
              </h3>

              {/* Description */}
              <p className="text-neutral-600 dark:text-neutral-400">
                {features[currentIndex].description}
              </p>
            </div>
          </div>
        </div>

        {/* Navigation buttons */}
        <Button
          variant="ghost"
          size="icon"
          className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-black/50 backdrop-blur-sm rounded-full h-8 w-8 shadow-md z-10"
          onClick={prevSlide}
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>

        <Button
          variant="ghost"
          size="icon"
          className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-black/50 backdrop-blur-sm rounded-full h-8 w-8 shadow-md z-10"
          onClick={nextSlide}
        >
          <ChevronRight className="h-5 w-5" />
        </Button>
      </div>

      {/* Dots indicator */}
      <div className="flex justify-center mt-4 gap-1.5">
        {features.map((_, index) => (
          <button
            key={index}
            className={`h-2 rounded-full transition-all duration-300 ${
              index === currentIndex
                ? "w-6 bg-[var(--brand-gold)]"
                : "w-2 bg-neutral-300 dark:bg-neutral-700"
            }`}
            onClick={() => handleManualNavigation(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
