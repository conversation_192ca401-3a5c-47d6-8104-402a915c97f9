import React from 'react';
import { View, Text } from 'react-native';
import { Heart, Users, Star } from 'lucide-react-native';
import { BusinessDiscoveryData } from '../../lib/services/businessDiscovery';
import { BusinessInteractionStatus } from '../../lib/services/businessInteractions';
import { formatIndianNumberShort } from '@/lib/utils';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface BusinessStatsProps {
  businessData: BusinessDiscoveryData;
  interactionStatus: BusinessInteractionStatus | null;
  isDark: boolean;
}

export default function BusinessStats({ businessData, interactionStatus, isDark }: BusinessStatsProps) {
  const styles = createPublicCardViewStyles(isDark);

  return (
    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Heart color={isDark ? '#D4AF37' : '#D4AF37'} size={20} />
        <Text style={styles.statNumber}>
          {formatIndianNumberShort(interactionStatus?.likeCount ?? businessData.total_likes ?? 0)}
        </Text>
        <Text style={styles.statLabel}>Likes</Text>
      </View>
      <View style={styles.statItem}>
        <Users color={isDark ? '#D4AF37' : '#D4AF37'} size={20} />
        <Text style={styles.statNumber}>
          {formatIndianNumberShort(interactionStatus?.subscriptionCount ?? businessData.total_subscriptions ?? 0)}
        </Text>
        <Text style={styles.statLabel}>Followers</Text>
      </View>
      <View style={styles.statItem}>
        <Star color={isDark ? '#D4AF37' : '#D4AF37'} size={20} />
        <Text style={styles.statNumber}>
          {(interactionStatus?.averageRating ?? businessData.average_rating ?? 0).toFixed(1)}
        </Text>
        <Text style={styles.statLabel}>Rating</Text>
      </View>
    </View>
  );
}
