"use client";

import { useState, useEffect } from 'react';
import { MapPin, Loader2 } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { cn } from '@/lib/utils';

interface LocationDisplayProps {
  className?: string;
}

interface LocationData {
  city?: string;
  state?: string;
  locality?: string;
  pincode?: string;
}

export default function LocationDisplay({ className }: LocationDisplayProps) {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserLocation = async () => {
      try {
        const supabase = createClient();
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError || !user) {
          setError('Authentication required');
          return;
        }

        // Try to get business profile first, then customer profile
        const { data: businessProfile } = await supabase
          .from('business_profiles')
          .select('city, state, locality, pincode')
          .eq('id', user.id)
          .single();

        if (businessProfile) {
          setLocation(businessProfile);
          return;
        }

        // Fallback to customer profile
        const { data: customerProfile } = await supabase
          .from('customer_profiles')
          .select('city, state, locality, pincode')
          .eq('id', user.id)
          .single();

        if (customerProfile) {
          setLocation(customerProfile);
        } else {
          setError('Location not found in profile');
        }
      } catch (err) {
        console.error('Error fetching location:', err);
        setError('Failed to load location');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserLocation();
  }, []);

  if (isLoading) {
    return (
      <div className={cn(
        "flex items-center gap-2 text-sm text-muted-foreground",
        className
      )}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>Loading location...</span>
      </div>
    );
  }

  if (error || !location) {
    return (
      <div className={cn(
        "flex items-center gap-2 text-sm text-muted-foreground",
        className
      )}>
        <MapPin className="h-4 w-4" />
        <span>Location not available</span>
      </div>
    );
  }

  // Format location string
  const locationParts = [
    location.locality,
    location.city,
    location.state,
    location.pincode
  ].filter(Boolean);

  const locationString = locationParts.join(', ');

  return (
    <div className={cn(
      "flex items-center gap-2 text-sm text-muted-foreground bg-muted/50 px-3 py-2 rounded-lg border",
      className
    )}>
      <MapPin className="h-4 w-4 text-[var(--brand-gold)]" />
      <span>
        <span className="font-medium">Posting from:</span> {locationString}
      </span>
    </div>
  );
}
