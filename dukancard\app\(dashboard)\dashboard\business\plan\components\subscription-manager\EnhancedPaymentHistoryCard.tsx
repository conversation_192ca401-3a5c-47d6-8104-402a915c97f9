"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  AlertCircle,
  CheckCircle2,
  RefreshCw,
  XCircle,
  ChevronDown,
  ChevronUp,
  CreditCard,
  Download,
  Calendar,
  Clock
} from "lucide-react";
import { format } from "date-fns";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { PaymentStatusType } from "@/lib/types/subscription";

interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: PaymentStatusType;
  date: string;
  refundId?: string;
  refundStatus?: string;
  refundAmount?: number;
  invoiceUrl?: string;
}

interface EnhancedPaymentHistoryCardProps {
  subscriptionId: string;
  payments: Payment[];
  isLoading?: boolean;
  hasError?: boolean;
  onRefresh?: () => void;
  onRetryPayment?: (_paymentId: string) => void;
  onRequestRefund?: (_paymentId: string) => void;
}

export default function EnhancedPaymentHistoryCard({
  payments,
  isLoading = false,
  hasError = false,
  onRefresh,
  onRetryPayment,
  onRequestRefund,
  subscriptionId: _subscriptionId
}: EnhancedPaymentHistoryCardProps) {
  const [expandedPaymentId, setExpandedPaymentId] = useState<string | null>(null);

  // Toggle payment details expansion
  const togglePaymentDetails = (paymentId: string) => {
    setExpandedPaymentId(expandedPaymentId === paymentId ? null : paymentId);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, "PPP");
    } catch (_error) {
      return "Invalid date";
    }
  };

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

      if (diffInDays === 0) return "Today";
      if (diffInDays === 1) return "Yesterday";
      if (diffInDays < 30) return `${diffInDays} days ago`;

      const diffInMonths = Math.floor(diffInDays / 30);
      if (diffInMonths === 1) return "1 month ago";
      if (diffInMonths < 12) return `${diffInMonths} months ago`;

      const diffInYears = Math.floor(diffInMonths / 12);
      if (diffInYears === 1) return "1 year ago";
      return `${diffInYears} years ago`;
    } catch (_error) {
      return "";
    }
  };

  // Get status badge color and icon
  const getStatusInfo = (status: PaymentStatusType) => {
    switch (status) {
      case "SUCCESS":
        return {
          color: "bg-green-500 hover:bg-green-600",
          bgColor: "bg-green-50 dark:bg-green-900/20",
          textColor: "text-green-700 dark:text-green-300",
          borderColor: "border-green-200 dark:border-green-800",
          icon: <CheckCircle2 className="h-4 w-4 mr-1" />,
          text: "Success"
        };
      case "PENDING":
        return {
          color: "bg-yellow-500 hover:bg-yellow-600",
          bgColor: "bg-yellow-50 dark:bg-yellow-900/20",
          textColor: "text-yellow-700 dark:text-yellow-300",
          borderColor: "border-yellow-200 dark:border-yellow-800",
          icon: <Clock className="h-4 w-4 mr-1" />,
          text: "Pending"
        };
      case "FAILED":
        return {
          color: "bg-red-500 hover:bg-red-600",
          bgColor: "bg-red-50 dark:bg-red-900/20",
          textColor: "text-red-700 dark:text-red-300",
          borderColor: "border-red-200 dark:border-red-800",
          icon: <XCircle className="h-4 w-4 mr-1" />,
          text: "Failed"
        };
      case "CANCELLED":
        return {
          color: "bg-gray-500 hover:bg-gray-600",
          bgColor: "bg-gray-50 dark:bg-gray-900/20",
          textColor: "text-gray-700 dark:text-gray-300",
          borderColor: "border-gray-200 dark:border-gray-800",
          icon: <XCircle className="h-4 w-4 mr-1" />,
          text: "Cancelled"
        };
      case "REFUNDED":
        return {
          color: "bg-blue-500 hover:bg-blue-600",
          bgColor: "bg-blue-50 dark:bg-blue-900/20",
          textColor: "text-blue-700 dark:text-blue-300",
          borderColor: "border-blue-200 dark:border-blue-800",
          icon: <RefreshCw className="h-4 w-4 mr-1" />,
          text: "Refunded"
        };
      default:
        // Convert status to a more readable format (e.g., "paid" -> "Paid")
        const formattedStatus = status.toLowerCase();
        const displayText = formattedStatus.charAt(0).toUpperCase() + formattedStatus.slice(1);

        return {
          color: "bg-gray-500 hover:bg-gray-600",
          bgColor: "bg-gray-50 dark:bg-gray-900/20",
          textColor: "text-gray-700 dark:text-gray-300",
          borderColor: "border-gray-200 dark:border-gray-800",
          icon: <AlertCircle className="h-4 w-4 mr-1" />,
          text: displayText
        };
    }
  };



  // Sort payments by date (newest first)
  const sortedPayments = [...payments].sort((a, b) => {
    const dateA = new Date(a.date).getTime();
    const dateB = new Date(b.date).getTime();
    return dateB - dateA;
  });

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
        duration: 0.5,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.05,
        duration: 0.3,
      },
    }),
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={cardVariants}
      className="w-full"
    >
      <Card className="overflow-hidden backdrop-blur-sm bg-white/80 dark:bg-black/50 border border-neutral-200/80 dark:border-neutral-800/80 shadow-sm">
        <CardHeader className="pb-2 bg-gradient-to-br from-white/90 to-white/70 dark:from-black/70 dark:to-black/50 border-b border-neutral-200/80 dark:border-neutral-800/80">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
                className="p-2 rounded-full bg-primary/10 text-primary"
              >
                <CreditCard className="h-5 w-5" />
              </motion.div>
              <div>
                <CardTitle className="text-xl">Payment History</CardTitle>
                <CardDescription>
                  View your payment history and download invoices
                </CardDescription>
              </div>
            </div>
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={isLoading}
                className="bg-white/80 dark:bg-black/30"
              >
                <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="p-0">

          {/* Payment List */}
          <div className="p-4">
            {hasError ? (
              <div className="text-center py-6 text-muted-foreground">
                <AlertCircle className="h-8 w-8 mx-auto mb-2 text-red-500" />
                <p>Failed to load payment history.</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={onRefresh}
                >
                  Try Again
                </Button>
              </div>
            ) : sortedPayments.length === 0 ? (
              <div className="text-center py-6 text-muted-foreground">
                No payment records found for this subscription.
              </div>
            ) : (
              <div className="space-y-4">
                <AnimatePresence>
                  {sortedPayments.map((payment, index) => {
                    const statusInfo = getStatusInfo(payment.status);
                    const isExpanded = expandedPaymentId === payment.id;

                    return (
                      <motion.div
                        key={payment.id}
                        custom={index}
                        initial="hidden"
                        animate="visible"
                        exit="hidden"
                        variants={itemVariants}
                      >
                        <Collapsible
                          open={isExpanded}
                          className="border rounded-lg overflow-hidden bg-white dark:bg-black/20"
                        >
                          <div className={`p-3 flex items-center justify-between ${statusInfo.bgColor} border-b ${statusInfo.borderColor}`}>
                            <div className="flex items-center gap-3">
                              <div className={`w-10 h-10 rounded-full ${statusInfo.color} flex items-center justify-center text-white`}>
                                {statusInfo.icon}
                              </div>
                              <div>
                                <div className="font-medium">
                                  {payment.currency} {payment.amount.toLocaleString()}
                                </div>
                                <div className={`text-xs ${statusInfo.textColor}`}>
                                  {statusInfo.text}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              <div className="text-right">
                                <div className="text-sm">{formatDate(payment.date)}</div>
                                <div className="text-xs text-muted-foreground">
                                  {formatRelativeTime(payment.date)}
                                </div>
                              </div>
                              <CollapsibleTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => togglePaymentDetails(payment.id)}
                                  className="rounded-full h-8 w-8 p-0"
                                >
                                  {isExpanded ? (
                                    <ChevronUp className="h-4 w-4" />
                                  ) : (
                                    <ChevronDown className="h-4 w-4" />
                                  )}
                                </Button>
                              </CollapsibleTrigger>
                            </div>
                          </div>

                          <CollapsibleContent>
                            <AnimatePresence>
                              {isExpanded && (
                                <motion.div
                                  initial={{ opacity: 0, height: 0 }}
                                  animate={{ opacity: 1, height: "auto" }}
                                  exit={{ opacity: 0, height: 0 }}
                                  transition={{ duration: 0.3 }}
                                >
                                  <div className="p-4 space-y-4">
                                    <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                                      <div className="text-muted-foreground">Payment ID:</div>
                                      <div className="font-mono text-xs">{payment.id}</div>


                                      <div className="text-muted-foreground">Date:</div>
                                      <div className="flex items-center gap-1">
                                        <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                                        <span>{formatDate(payment.date)}</span>
                                      </div>

                                      {payment.refundId && (
                                        <>
                                          <div className="text-muted-foreground">Refund ID:</div>
                                          <div className="font-mono text-xs">{payment.refundId}</div>

                                          <div className="text-muted-foreground">Refund Status:</div>
                                          <div>{payment.refundStatus}</div>

                                          <div className="text-muted-foreground">Refund Amount:</div>
                                          <div>
                                            {payment.currency} {payment.refundAmount?.toLocaleString() || payment.amount.toLocaleString()}
                                          </div>
                                        </>
                                      )}
                                    </div>

                                    <Separator />

                                    <div className="flex justify-between">
                                      <div className="space-x-2">
                                        {payment.status === "FAILED" && onRetryPayment && (
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => onRetryPayment(payment.id)}
                                          >
                                            <RefreshCw className="h-4 w-4 mr-1" />
                                            Retry Payment
                                          </Button>
                                        )}

                                        {payment.status === "SUCCESS" && !payment.refundId && onRequestRefund && (
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => onRequestRefund(payment.id)}
                                          >
                                            <RefreshCw className="h-4 w-4 mr-1" />
                                            Request Refund
                                          </Button>
                                        )}
                                      </div>

                                      {payment.invoiceUrl && (
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() => window.open(payment.invoiceUrl, '_blank')}
                                        >
                                          <Download className="h-4 w-4 mr-1" />
                                          Download Invoice
                                        </Button>
                                      )}
                                    </div>
                                  </div>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </CollapsibleContent>
                        </Collapsible>
                      </motion.div>
                    );
                  })}
                </AnimatePresence>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
