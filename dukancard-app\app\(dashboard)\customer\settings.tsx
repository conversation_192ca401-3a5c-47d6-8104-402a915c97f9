import { DashboardLayout } from '@/components/shared/layout/DashboardLayout';
import React, { useEffect, useState } from 'react';
import { Text, View, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { createCustomerSettingsStyles } from '@/styles/dashboard/customer/settings-styles';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'expo-router';
import { useColorScheme } from '@/hooks/useColorScheme';
import {
  Settings,
  Mail,
  Phone,
  KeyRound,
  LogOut,
  ChevronRight,
  User,
  Shield
} from 'lucide-react-native';
import SettingsPageClient from './settings/components/SettingsPageClient';

export default function CustomerSettingsScreen() {
  const { user, profileStatus, signOut } = useAuth();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createCustomerSettingsStyles();

  // Get customer name from profile
  const customerName = profileStatus?.roleStatus?.hasCustomerProfile
    ? user?.user_metadata?.name || user?.email?.split('@')[0] || 'Valued Customer'
    : 'Valued Customer';

  // Determine user registration type
  const isGoogleUser = user?.app_metadata?.provider === "google";
  const hasEmail = !!user?.email;
  const hasPhone = !!user?.phone;

  let registrationType: 'google' | 'email' | 'phone' = 'email'; // default

  if (isGoogleUser) {
    registrationType = 'google';
  } else if (hasPhone && !hasEmail) {
    registrationType = 'phone';
  } else if (hasEmail) {
    registrationType = 'email';
  }

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
              router.replace('/login');
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };

  const SettingItem = ({ 
    icon, 
    title, 
    subtitle, 
    onPress, 
    showChevron = true,
    iconColor = '#D4AF37',
    iconBgColor = undefined 
  }: {
    icon: React.ReactNode;
    title: string;
    subtitle: string;
    onPress: () => void;
    showChevron?: boolean;
    iconColor?: string;
    iconBgColor?: string;
  }) => (
    <TouchableOpacity 
      style={[styles.settingItem, { borderBottomColor: isDark ? '#333' : '#f0f0f0' }]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.settingLeft}>
        <View style={[
          styles.settingIcon, 
          { 
            backgroundColor: iconBgColor || (isDark ? '#2a2a2a' : '#f8f9fa') 
          }
        ]}>
          {icon}
        </View>
        <View style={styles.settingContent}>
          <Text style={[styles.settingTitle, { color: isDark ? '#fff' : '#000' }]}>
            {title}
          </Text>
          <Text style={[styles.settingSubtitle, { color: isDark ? '#999' : '#666' }]}>
            {subtitle}
          </Text>
        </View>
      </View>
      {showChevron && (
        <ChevronRight size={20} color={isDark ? '#666' : '#999'} />
      )}
    </TouchableOpacity>
  );

  return (
    <DashboardLayout
      userName={customerName}
      showNotifications={true}
    >
      <SettingsPageClient
        currentEmail={user?.email}
        currentPhone={user?.phone}
        registrationType={registrationType}
      />

      {/* Additional Settings Section */}
      <ScrollView style={styles.additionalSettings} showsVerticalScrollIndicator={false}>
        {/* Account Actions Section */}
        <View style={[styles.section, { backgroundColor: isDark ? '#1a1a1a' : '#fff' }]}>
          <Text style={[styles.sectionTitle, { color: isDark ? '#D4AF37' : '#D4AF37' }]}>
            Account Actions
          </Text>

          {/* Sign Out */}
          <SettingItem
            icon={<LogOut size={20} color="#EF4444" />}
            title="Sign Out"
            subtitle="Sign out of your account"
            onPress={handleLogout}
            showChevron={false}
            iconBgColor={isDark ? '#7F1D1D20' : '#FEE2E2'}
          />
        </View>

        {/* Account Info */}
        <View style={styles.accountInfo}>
          <Text style={[styles.accountInfoText, { color: isDark ? '#999' : '#666' }]}>
            Registration Type: {registrationType.charAt(0).toUpperCase() + registrationType.slice(1)}
          </Text>
          <Text style={[styles.accountInfoText, { color: isDark ? '#999' : '#666' }]}>
            User ID: {user?.id?.slice(0, 8)}...
          </Text>
        </View>
      </ScrollView>
    </DashboardLayout>
  );
}


