"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Star, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import BusinessReviewListClient from "./BusinessReviewListClient";
import BusinessMyReviewListClient from "./BusinessMyReviewListClient";
import { cn } from "@/lib/utils";
import { formatIndianNumberShort } from "@/lib/utils";

interface BusinessReviewsPageClientProps {
  businessProfileId: string;
  reviewsReceivedCount: number;
  myReviewsCount: number;
}

export default function BusinessReviewsPageClient({
  businessProfileId,
  reviewsReceivedCount,
  myReviewsCount
}: BusinessReviewsPageClientProps) {
  const [activeTab, setActiveTab] = useState("received");

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-6 max-w-6xl mx-auto"
    >
      <motion.div variants={itemVariants} className="mb-6 relative">
        {/* Main card container - matching business dashboard style */}
        <div className={cn(
          "rounded-xl border border-neutral-200 dark:border-neutral-800",
          "bg-white dark:bg-black", // Using black for dark mode and white for light mode
          "shadow-md p-4 sm:p-5 md:p-6 mb-4",
          "transition-all duration-300 hover:shadow-lg",
          "relative overflow-hidden"
        )}>

          {/* Card background with subtle pattern */}
          <div
            className="absolute inset-0 pointer-events-none opacity-5 dark:opacity-10"
            style={{
              backgroundImage: `url("/decorative/card-texture.svg")`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }}
          ></div>

          {/* Content with relative positioning */}
          <div className="relative z-10">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
              <div className="p-2 rounded-lg bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 self-start">
                <Star className="w-4 sm:w-5 h-4 sm:h-5" />
              </div>
              <div className="flex-1">
                <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Business Reviews
                </h3>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                  Manage reviews for your business
                </p>
              </div>
            </div>

            {/* Tabs */}
            <div className="mt-4 flex space-x-1 border-b border-neutral-200 dark:border-neutral-800 mb-6">
              <Button
                variant={activeTab === 'received' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('received')}
                className={cn(
                  "rounded-b-none border-b-2 border-transparent",
                  activeTab === 'received'
                    ? "border-b-[var(--brand-gold)] bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/20"
                    : "hover:bg-neutral-100 dark:hover:bg-neutral-800"
                )}
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Reviews Received ({formatIndianNumberShort(reviewsReceivedCount)})
              </Button>
              <Button
                variant={activeTab === 'given' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('given')}
                className={cn(
                  "rounded-b-none border-b-2 border-transparent",
                  activeTab === 'given'
                    ? "border-b-[var(--brand-gold)] bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/20"
                    : "hover:bg-neutral-100 dark:hover:bg-neutral-800"
                )}
              >
                <Star className="w-4 h-4 mr-2" />
                My Reviews ({formatIndianNumberShort(myReviewsCount)})
              </Button>
            </div>

            {/* Tab Content */}
            {activeTab === 'received' && (
              <BusinessReviewListClient businessProfileId={businessProfileId} />
            )}
            {activeTab === 'given' && (
              <BusinessMyReviewListClient />
            )}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
