"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";

import { useSubscriptionProcessing } from "../../context/SubscriptionProcessingContext";
import { toast } from "sonner";
import WebhookWaitingIndicator from "../WebhookWaitingIndicator";
import { <PERSON>rk<PERSON> } from "lucide-react";

import ModernSubscriptionStatusCard from "./ModernSubscriptionStatusCard";
// Unused import removed
// import EnhancedPaymentHistoryCard from "./EnhancedPaymentHistoryCard";
import EnhancedInvoiceHistoryCard from "../EnhancedInvoiceHistoryCard";
import { SubscriptionStatusType, PaymentStatusType } from "@/lib/types/subscription";
import EnhancedSubscriptionActionCard from "./EnhancedSubscriptionActionCard";
import EnhancedGlowButton from "../EnhancedGlowButton";
import SubscriptionTabsToggle from "./SubscriptionTabsToggle";

interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: PaymentStatusType;
  date: string;
  paymentMethod: string;
  refundId?: string;
  refundStatus?: string;
  refundAmount?: number;
  invoiceUrl?: string;
}

interface SubscriptionManagerProps {
  userId: string;
  subscriptionId: string | null;
  status: string | null;
  planName: string;
  planId: string; // Added planId to check if user is on free plan
  planCycle: "monthly" | "yearly";
  amount: number;
  currency?: string;
  nextBillingDate?: string | null;
  lastPaymentDate?: string | null;
  lastPaymentStatus?: string | null;
  lastPaymentMethod?: string | null;
  createdAt?: string | null;
  expiresAt?: string | null;
  pausedAt?: string | null;
  cancellationRequestedAt?: string | null;
  cancelledAt?: string | null;
  isEligibleForRefund?: boolean;
  // Additional date fields
  subscriptionStartDate?: string | null;
  subscriptionChargeTime?: string | null;
  trialEndDate?: string | null;
}

export default function SubscriptionManager({
  subscriptionId,
  status,
  planName,
  planId,
  planCycle,
  amount,
  currency = "INR",
  nextBillingDate,
  lastPaymentDate,
  lastPaymentStatus: _lastPaymentStatus,
  lastPaymentMethod,
  createdAt,
  expiresAt,
  pausedAt,
  cancellationRequestedAt,
  cancelledAt,
  isEligibleForRefund = false,
  // Additional date fields
  subscriptionStartDate,
  subscriptionChargeTime,
  trialEndDate,
}: SubscriptionManagerProps) {
  const router = useRouter();
  const [_isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const [_payments, setPayments] = useState<Payment[]>([]);
  const [_isLoadingPayments, setIsLoadingPayments] = useState(false);
  const [hasPaymentError, setHasPaymentError] = useState(false);
  const paymentErrorShown = useRef(false);

  const {
    status: processingStatus,
    setSubscriptionCancelled,
    setSubscriptionPaused,
    setSubscriptionResumed,
    setWaitingForWebhook,
    completeProcessing,
    resetProcessing
  } = useSubscriptionProcessing();

  // Map status string to SubscriptionStatusType
  const mapStatus = (statusString: string | null): SubscriptionStatusType => {
    if (!statusString) return "unknown";

    const lowerStatus = statusString.toLowerCase();

    if (lowerStatus.includes("active")) return "active";
    if (lowerStatus.includes("authenticated")) return "authenticated";
    if (lowerStatus.includes("pending")) return "pending";
    if (lowerStatus.includes("halted")) return "paused"; // Map halted to paused for UI consistency
    if (lowerStatus.includes("paused")) return "paused";
    if (lowerStatus.includes("cancelled")) return "cancelled";
    if (lowerStatus.includes("completed")) return "completed";
    if (lowerStatus.includes("expired")) return "expired";
    if (lowerStatus.includes("initialized")) return "initialized";
    if (lowerStatus.includes("failed")) return "payment_failed";

    return "unknown";
  };

  // Fetch payment history
  const fetchPaymentHistory = useCallback(async () => {
    if (!subscriptionId || hasPaymentError) return;

    setIsLoadingPayments(true);

    try {
      const response = await fetch(`/api/subscription/${subscriptionId}/payments`);

      if (!response.ok) {
        setHasPaymentError(true);
        throw new Error("Failed to fetch payment history");
      }

      const data = await response.json();

      if (!data.success) {
        setHasPaymentError(true);
        throw new Error(data.error || "Failed to fetch payment history");
      }

      setPayments(data.data || []);
      setHasPaymentError(false);
    } catch (error) {
      console.error("Error fetching payment history:", error);
      setHasPaymentError(true);

      // Only show the toast once to avoid spamming
      if (!paymentErrorShown.current) {
        toast.error("Failed to load payment history. Please try again.");
        paymentErrorShown.current = true;
      }
    } finally {
      setIsLoadingPayments(false);
    }
  }, [subscriptionId, hasPaymentError]);

  // Fetch payment history on mount and when subscription ID changes
  useEffect(() => {
    if (subscriptionId && !hasPaymentError) {
      fetchPaymentHistory();
    }
  }, [fetchPaymentHistory, subscriptionId, hasPaymentError]);

  // Handle cancel subscription
  const handleCancelSubscription = async (cancelImmediately: boolean = false): Promise<void> => {
    if (!subscriptionId) return;

    setIsLoading(true);
    setSubscriptionCancelled("Cancelling your subscription...");

    // Close any open dialogs first
    const closeDialogs = async () => {
      // This will close any open dialogs by simulating an Escape key press
      const escapeEvent = new KeyboardEvent('keydown', {
        key: 'Escape',
        code: 'Escape',
        keyCode: 27,
        which: 27,
        bubbles: true
      });
      document.dispatchEvent(escapeEvent);

      // Wait a short time for dialogs to close
      await new Promise(resolve => setTimeout(resolve, 300));

      // Now show the webhook waiting indicator
      setWaitingForWebhook("Waiting for confirmation from Razorpay. Please don't refresh the page.");
    };

    // Close dialogs before proceeding
    await closeDialogs();

    try {
      // Import the cancelSubscription action
      const { cancelSubscription } = await import("@/lib/actions/subscription");

      // Cancel the subscription with the immediate parameter
      const result = await cancelSubscription(cancelImmediately);

      if (!result.success) {
        throw new Error(result.error || "Failed to cancel subscription");
      }

      // Subscription cancelled successfully
      const message = cancelImmediately
        ? "Your subscription has been cancelled immediately."
        : "Your subscription will be cancelled at the end of the billing cycle.";

      completeProcessing(true, message);

      // Reset the webhook waiting state immediately
      resetProcessing();

      // Refresh the page after a short delay
      setTimeout(() => {
        router.refresh();
      }, 2000);
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      completeProcessing(
        false,
        error instanceof Error ? error.message : "Failed to cancel subscription"
      );

      // Reset the webhook waiting state after a short delay
      setTimeout(() => {
        resetProcessing();
      }, 3000);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle pause subscription
  const handlePauseSubscription = async (): Promise<void> => {
    if (!subscriptionId) return;

    setIsLoading(true);
    setSubscriptionPaused("Pausing your subscription...");

    try {
      // Import the pauseSubscription action
      const { pauseSubscription } = await import("@/lib/actions/subscription");

      // Pause the subscription
      const result = await pauseSubscription();

      if (!result.success) {
        throw new Error(result.error || "Failed to pause subscription");
      }

      // Subscription paused successfully
      completeProcessing(true, "Your subscription has been paused successfully.");

      // Refresh the page after a short delay
      setTimeout(() => {
        router.refresh();
      }, 2000);
    } catch (error) {
      console.error("Error pausing subscription:", error);
      completeProcessing(
        false,
        error instanceof Error ? error.message : "Failed to pause subscription"
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle resume subscription
  const handleResumeSubscription = async (): Promise<void> => {
    if (!subscriptionId) return;

    setIsLoading(true);
    setSubscriptionResumed("Resuming your subscription...");

    try {
      // Import the resumeSubscription action
      const { activateSubscription } = await import("@/lib/actions/subscription");

      // Resume the subscription
      const result = await activateSubscription();

      // Check if result exists and has success property
      if (!result || result.success === undefined) {
        console.error("Invalid response from activateSubscription:", result);
        throw new Error("Received invalid response from server");
      }

      if (!result.success) {
        throw new Error(result.error || "Failed to resume subscription");
      }

      // Subscription resumed successfully
      toast.success("Your subscription has been resumed successfully.");

      // Dismiss any existing processing toast
      toast.dismiss();

      // Reset processing state
      resetProcessing();

      // Refresh the page after a short delay
      setTimeout(() => {
        router.refresh();
      }, 2000);
    } catch (error) {
      console.error("Error resuming subscription:", error);
      completeProcessing(
        false,
        error instanceof Error ? error.message : "Failed to resume subscription"
      );
    } finally {
      setIsLoading(false);
    }
  };



  // Handle request refund
  const handleRequestRefund = async (_speed: "normal" | "optimum"): Promise<void> => {
    // Show a toast message directing users to contact support
    toast.info(
      "Refund Request Process",
      {
        description: "Please contact our support team via email to request a refund for payment issues or subscription problems.",
        duration: 5000,
      }
    );

    // Reset any processing state
    resetProcessing();
  };

  // If no subscription ID, show a message
  if (!subscriptionId) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold mb-4">No Active Subscription</h2>
        <p className="text-muted-foreground mb-6">
          You don&apos;t have an active subscription. Subscribe to a plan to get started.
        </p>
        <div className="flex justify-center">
          <EnhancedGlowButton
            onClick={() => router.push("/dashboard/business/plan")}
            size="lg"
            className="px-8 py-2"
            showArrow={true}
            roundedFull={true}
          >
            View Plans
          </EnhancedGlowButton>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Show webhook waiting indicator when waiting for webhook response */}
      <WebhookWaitingIndicator
        isVisible={processingStatus === "waiting_for_webhook"}
        message="Waiting for confirmation from Razorpay"
        description="Please wait while we receive webhook confirmation. This may take a moment."
      />

      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
          <Sparkles className="w-4 sm:w-5 h-4 sm:h-5" />
        </div>
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
            My Subscription
          </h3>
          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
            Manage your subscription details and payment information
          </p>
        </div>
      </div>

      <div className="mb-8 w-full flex justify-center">
        <SubscriptionTabsToggle
          activeTab={activeTab}
          onChange={setActiveTab}
        />
      </div>
      {activeTab === "overview" && (
        <div className="space-y-6">
          <ModernSubscriptionStatusCard
            subscriptionId={subscriptionId}
            status={mapStatus(status)}
            planName={planName}
            planCycle={planCycle}
            amount={amount}
            currency={currency}
            nextBillingDate={nextBillingDate}
            lastPaymentDate={lastPaymentDate}
            lastPaymentMethod={lastPaymentMethod}
            createdAt={createdAt}
            expiresAt={expiresAt}
            pausedAt={pausedAt}
            cancellationRequestedAt={cancellationRequestedAt}
            cancelledAt={cancelledAt}
            isEligibleForRefund={isEligibleForRefund}
            // Additional date fields
            subscriptionStartDate={subscriptionStartDate}
            subscriptionChargeTime={subscriptionChargeTime}
            trialEndDate={trialEndDate}
          />

          <EnhancedSubscriptionActionCard
            subscriptionId={subscriptionId}
            status={mapStatus(status)}
            isEligibleForRefund={isEligibleForRefund}
            onCancelSubscription={handleCancelSubscription}
            onPauseSubscription={handlePauseSubscription}
            onResumeSubscription={handleResumeSubscription}
            onRequestRefund={handleRequestRefund}
            planId={planId}
          />
        </div>
      )}

      {activeTab === "payments" && (
        <div className="space-y-6">
          {/* Only show invoice history for paid plans */}
          {planId !== "free" ? (
            <EnhancedInvoiceHistoryCard
              subscriptionId={subscriptionId}
              planId={planId}
            />
          ) : (
            <div className="text-center py-12">
              <h2 className="text-xl font-bold mb-4">No Invoice History</h2>
              <p className="text-muted-foreground mb-6">
                Free plan users don&apos;t have invoices. Upgrade to a paid plan to see your invoice history.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
