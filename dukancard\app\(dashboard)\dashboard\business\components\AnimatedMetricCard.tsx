"use client";

import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface AnimatedMetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description: string;
  color: "rose" | "blue" | "amber" | "red" | "yellow";
  isUpdated?: boolean;
}

export default function AnimatedMetricCard({
  title,
  value,
  icon: Icon,
  description,
  color,
  isUpdated = false,
}: AnimatedMetricCardProps) {
  // Define color variants with enhanced glow effects
  const colorVariants = {
    rose: {
      bgLight: "bg-rose-100",
      bgDark: "dark:bg-rose-900/30",
      textLight: "text-rose-600",
      textDark: "dark:text-rose-400",
      borderHover: "group-hover:border-rose-300 dark:group-hover:border-rose-700",
      glowColor: "rgba(244, 63, 94, 0.4)",
      borderColor: "border-rose-200/50 dark:border-rose-700/50",
      innerGlow: "bg-rose-500/5 dark:bg-rose-500/10",
      circleColor: "bg-rose-500/30",
      circleGlow: "shadow-rose-500/60",
      shadowGlow: "shadow-rose-500/40",
    },
    blue: {
      bgLight: "bg-blue-100",
      bgDark: "dark:bg-blue-900/30",
      textLight: "text-blue-600",
      textDark: "dark:text-blue-400",
      borderHover: "group-hover:border-blue-300 dark:group-hover:border-blue-700",
      glowColor: "rgba(59, 130, 246, 0.4)",
      borderColor: "border-blue-200/50 dark:border-blue-700/50",
      innerGlow: "bg-blue-500/5 dark:bg-blue-500/10",
      circleColor: "bg-blue-500/30",
      circleGlow: "shadow-blue-500/60",
      shadowGlow: "shadow-blue-500/40",
    },
    amber: {
      bgLight: "bg-amber-100",
      bgDark: "dark:bg-amber-900/30",
      textLight: "text-amber-600",
      textDark: "dark:text-amber-400",
      borderHover: "group-hover:border-amber-300 dark:group-hover:border-amber-700",
      glowColor: "rgba(245, 158, 11, 0.4)",
      borderColor: "border-amber-200/50 dark:border-amber-700/50",
      innerGlow: "bg-amber-500/5 dark:bg-amber-500/10",
      circleColor: "bg-amber-500/30",
      circleGlow: "shadow-amber-500/60",
      shadowGlow: "shadow-amber-500/40",
    },
    red: {
      bgLight: "bg-red-100",
      bgDark: "dark:bg-red-900/30",
      textLight: "text-red-500",
      textDark: "dark:text-red-400",
      borderHover: "group-hover:border-red-300 dark:group-hover:border-red-700",
      glowColor: "rgba(239, 68, 68, 0.4)",
      borderColor: "border-red-200/50 dark:border-red-700/50",
      innerGlow: "bg-red-500/5 dark:bg-red-500/10",
      circleColor: "bg-red-500/30",
      circleGlow: "shadow-red-500/60",
      shadowGlow: "shadow-red-500/40",
    },
    yellow: {
      bgLight: "bg-yellow-100",
      bgDark: "dark:bg-yellow-900/30",
      textLight: "text-yellow-500",
      textDark: "dark:text-yellow-400",
      borderHover: "group-hover:border-yellow-300 dark:group-hover:border-yellow-700",
      glowColor: "rgba(234, 179, 8, 0.4)",
      borderColor: "border-yellow-200/50 dark:border-yellow-700/50",
      innerGlow: "bg-yellow-500/5 dark:bg-yellow-500/10",
      circleColor: "bg-yellow-500/30",
      circleGlow: "shadow-yellow-500/60",
      shadowGlow: "shadow-yellow-500/40",
    },
  };

  const selectedColor = colorVariants[color];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
  };

  const counterVariants = {
    initial: { scale: 1 },
    update: {
      scale: 1.05,
      transition: { duration: 0.3 },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      className={`
        group relative overflow-hidden rounded-xl p-4
        bg-white dark:bg-black
        border ${selectedColor.borderColor}
        ${selectedColor.shadowGlow} shadow-lg
        hover:shadow-xl hover:${selectedColor.shadowGlow}
        transition-all duration-300
        cursor-default
      `}
    >
      {/* Strong inner glow effect that fills the card */}
      <div className={`absolute inset-0 ${selectedColor.innerGlow} opacity-80 group-hover:opacity-100 transition-opacity duration-300`} />

      {/* Content */}
      <div className="relative z-10">
        {/* Icon and Title */}
        <div className="flex items-center gap-2 mb-3">
          <div className={cn("p-1.5 sm:p-2 rounded-lg self-start", selectedColor.bgLight, selectedColor.bgDark, selectedColor.textLight, selectedColor.textDark)}>
            <Icon className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          </div>
          <h3 className="text-sm sm:text-base font-semibold text-neutral-800 dark:text-neutral-100 truncate">
            {title}
          </h3>
        </div>

        {/* Value */}
        <div className="px-1 pb-2">
          <motion.div
            className="text-xl sm:text-2xl md:text-3xl font-bold group-hover:scale-105 transition-transform duration-300"
            variants={counterVariants}
            initial="initial"
            animate={isUpdated ? "update" : "initial"}
          >
            {/* Enhanced glow effect to the value */}
            <span className="relative">
              <span className="relative z-10">{value}</span>
              <div
                className="absolute inset-0 blur-sm opacity-50 z-0"
                style={{ backgroundColor: selectedColor.glowColor }}
              />
            </span>
          </motion.div>
          <p className="text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 mt-1">
            {description}
          </p>
        </div>
      </div>

      {/* Strong decorative colored glow elements */}
      <div className={`absolute -top-2 -right-2 w-10 h-10 ${selectedColor.circleColor} rounded-full ${selectedColor.circleGlow} opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md`} />
      <div className={`absolute -bottom-2 -left-2 w-8 h-8 ${selectedColor.circleColor} rounded-full ${selectedColor.circleGlow} opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md`} />

      {/* Shimmer effect - only on hover */}
      <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100 group-hover:animate-[shimmer_0.6s_ease-in-out] transition-opacity duration-300" />
    </motion.div>
  );
}
