import { Loader2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { DeleteState } from "../types/galleryTypes";

interface DeleteDialogProps {
  deleteState: DeleteState;
  onOpenChange: (_open: boolean) => void;
  onDelete: () => void;
}

export default function DeleteDialog({
  deleteState,
  onOpenChange,
  onDelete,
}: DeleteDialogProps) {
  return (
    <AlertDialog open={deleteState.deleteDialogOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent className="border-neutral-200 dark:border-neutral-800 shadow-lg">
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Gallery Image</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete this image? This action cannot be
            undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel
            disabled={deleteState.isDeleting}
            className="border-neutral-200 hover:bg-neutral-100 dark:border-neutral-800 dark:hover:bg-neutral-800"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.preventDefault();
              onDelete();
            }}
            disabled={deleteState.isDeleting}
            className="bg-red-500 hover:bg-red-600 text-white"
          >
            {deleteState.isDeleting ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </div>
            ) : (
              "Delete"
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
