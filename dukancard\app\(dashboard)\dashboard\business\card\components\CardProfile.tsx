import Image from "next/image";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { User, Building, Info, Loader2 } from "lucide-react";

type LogoUploadStatus = "idle" | "uploading" | "success" | "error";

interface CardProfileProps {
  logo_url?: string | null;
  localPreviewUrl?: string | null;
  logoUploadStatus: LogoUploadStatus;
  member_name?: string;
  business_name?: string;
  title?: string;
  about_bio?: string;
  finalThemeColor: string;
}

export default function CardProfile({
  logo_url,
  localPreviewUrl,
  logoUploadStatus,
  member_name: _member_name,
  business_name,
  title: _title,
  about_bio,
  finalThemeColor,
}: CardProfileProps) {
  return (
    <div className="flex flex-col items-center">
      <div className="relative w-20 h-20 sm:w-24 sm:h-24 rounded-full border-3 border-[var(--theme-color)] overflow-hidden flex items-center justify-center shadow-lg mb-2 sm:mb-3 bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-neutral-700 dark:to-neutral-800 transform hover:scale-105 transition-transform duration-300">
        {/* Only render Image if we have a valid URL */}
        {(localPreviewUrl || (logo_url && typeof logo_url === 'string' && logo_url.trim() !== "")) && (
          <Image
            src={localPreviewUrl || (logo_url || "")}
            alt={`${business_name} logo`}
            width={96}
            height={96}
            className="object-cover w-full h-full"
            onError={(e) => (e.currentTarget.style.display = "none")}
          />
        )}
        {!localPreviewUrl &&
          (!logo_url || (typeof logo_url === 'string' && logo_url.trim() === "")) &&
          logoUploadStatus !== "uploading" && (
            <User
              className="w-12 h-12 opacity-50"
              color={finalThemeColor}
            />
          )}
        {logoUploadStatus === "uploading" && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <Loader2 className="w-8 h-8 text-white animate-spin" />
          </div>
        )}

        {/* Subtle glow effect for the profile image */}
        <div className="absolute inset-0 bg-gradient-to-tr from-[var(--theme-color-10)] via-transparent to-[var(--theme-color-10)] opacity-40"></div>
        <div className="absolute -bottom-1 -right-1 w-full h-full rounded-full bg-black/5 blur-sm -z-10"></div>
      </div>

      {/* Business name only */}
      <h3 className="text-base sm:text-lg font-bold text-[--theme-color] mb-2 tracking-wide px-2 text-center">
        {business_name ? (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="line-clamp-1 relative cursor-default">
                  {/* Simple text without shadow effect */}
                  <span className="relative">
                    {business_name}
                  </span>
                </span>
              </TooltipTrigger>
              <TooltipContent>
                <p>{business_name}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          <Building
            className="inline-block h-5 w-5 opacity-50"
            color={finalThemeColor}
          />
        )}
      </h3>

      {about_bio && (
        <div className="flex items-start text-xs text-neutral-600 dark:text-neutral-300 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 rounded-lg max-w-xs mx-auto mb-2 sm:mb-3">
          <Info className="w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]" />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <p className="text-start line-clamp-2 cursor-default">
                  {about_bio}
                </p>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>{about_bio}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      )}
    </div>
  );
}