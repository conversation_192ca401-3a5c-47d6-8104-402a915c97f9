"use server";

import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { getCustomAdImagePath } from "@/lib/utils/storage-paths";

export interface CustomAdUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface CustomAdUpdateResult {
  success: boolean;
  error?: string;
}

/**
 * Upload custom ad image with compression and auto-save to database
 */
export async function uploadCustomAdImage(
  formData: FormData
): Promise<CustomAdUploadResult> {
  try {
    // Create admin client for storage operations
    const adminSupabase = createAdminClient();

    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // Extract the cropped image file from FormData
    const imageFile = formData.get("image") as File;
    if (!imageFile) {
      return {
        success: false,
        error: "No image file provided",
      };
    }

    // Validate file type
    if (!imageFile.type.startsWith("image/")) {
      return {
        success: false,
        error: "Invalid file type. Please upload an image.",
      };
    }

    // Validate file size (max 15MB before compression)
    if (imageFile.size > 15 * 1024 * 1024) {
      return {
        success: false,
        error: "File too large. Maximum size is 15MB.",
      };
    }

    const bucketName = "business";
    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const imagePath = getCustomAdImagePath(user.id, timestamp);

    // File is already compressed on client-side, just upload it
    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

    // Upload to Supabase Storage using admin client
    const { error: uploadError } = await adminSupabase.storage
      .from(bucketName)
      .upload(imagePath, fileBuffer, {
        contentType: imageFile.type, // Use original file type (already compressed)
        upsert: true
      });

    if (uploadError) {
      console.error("Custom Ad Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError.message}`,
      };
    }

    // Get the public URL
    const { data: urlData } = adminSupabase.storage
      .from(bucketName)
      .getPublicUrl(imagePath);

    if (!urlData?.publicUrl) {
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    // Auto-save to database - update custom_ads field
    const { error: updateError } = await supabase
      .from("business_profiles")
      .update({
        custom_ads: {
          enabled: true,
          image_url: urlData.publicUrl,
          link_url: "", // Will be updated separately
          uploaded_at: new Date().toISOString(),
        }
      })
      .eq("id", user.id);

    if (updateError) {
      console.error("Database update error:", updateError);
      // Image uploaded successfully but database update failed
      // We could delete the image here, but let's keep it and return success
      // The user can try again
    }

    return {
      success: true,
      url: urlData.publicUrl,
    };

  } catch (error) {
    console.error("Custom ad upload error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during upload.",
    };
  }
}

/**
 * Update custom ad link URL
 */
export async function updateCustomAdLink(linkUrl: string): Promise<CustomAdUpdateResult> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // Validate URL if provided
    if (linkUrl && linkUrl.trim()) {
      try {
        new URL(linkUrl);
      } catch {
        return {
          success: false,
          error: "Invalid URL format",
        };
      }
    }

    // Get current custom_ads data
    const { data: profile, error: fetchError } = await supabase
      .from("business_profiles")
      .select("custom_ads")
      .eq("id", user.id)
      .single();

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current ad data",
      };
    }

    // Update only the link_url field
    const updatedCustomAds = {
      ...profile.custom_ads,
      link_url: linkUrl.trim(),
    };

    const { error: updateError } = await supabase
      .from("business_profiles")
      .update({ custom_ads: updatedCustomAds })
      .eq("id", user.id);

    if (updateError) {
      return {
        success: false,
        error: "Failed to update ad link",
      };
    }

    return { success: true };

  } catch (error) {
    console.error("Custom ad link update error:", error);
    return {
      success: false,
      error: "An unexpected error occurred",
    };
  }
}

/**
 * Toggle custom ad enabled/disabled state
 */
export async function toggleCustomAd(enabled: boolean): Promise<CustomAdUpdateResult> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // Get current custom_ads data
    const { data: profile, error: fetchError } = await supabase
      .from("business_profiles")
      .select("custom_ads")
      .eq("id", user.id)
      .single();

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current ad data",
      };
    }

    // Update only the enabled field
    const updatedCustomAds = {
      ...profile.custom_ads,
      enabled,
    };

    const { error: updateError } = await supabase
      .from("business_profiles")
      .update({ custom_ads: updatedCustomAds })
      .eq("id", user.id);

    if (updateError) {
      return {
        success: false,
        error: "Failed to toggle ad state",
      };
    }

    return { success: true };

  } catch (error) {
    console.error("Custom ad toggle error:", error);
    return {
      success: false,
      error: "An unexpected error occurred",
    };
  }
}

/**
 * Delete custom ad image and reset data
 */
export async function deleteCustomAd(): Promise<CustomAdUpdateResult> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // First, get the current custom ad data to extract the image URL
    const { data: profile, error: fetchError } = await supabase
      .from("business_profiles")
      .select("custom_ads")
      .eq("id", user.id)
      .single();

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current ad data",
      };
    }

    const currentCustomAds = profile?.custom_ads;
    const imageUrl = currentCustomAds?.image_url;

    // Delete the image from storage if it exists
    if (imageUrl) {
      try {
        // Extract the file path from the URL
        // URL format: https://domain.supabase.co/storage/v1/object/public/business/users/xx/xx/userId/ads/custom_ad_timestamp.webp
        const urlParts = imageUrl.split('/storage/v1/object/public/business/');
        if (urlParts.length === 2) {
          const filePath = urlParts[1];

          // Use admin client to delete from storage
          const adminSupabase = createAdminClient();
          const { error: deleteError } = await adminSupabase.storage
            .from("business")
            .remove([filePath]);

          if (deleteError) {
            console.error("Storage deletion error:", deleteError);
            // Continue with database update even if storage deletion fails
          }
        }
      } catch (storageError) {
        console.error("Error deleting custom ad from storage:", storageError);
        // Continue with database update even if storage deletion fails
      }
    }

    // Reset custom_ads data in database
    const { error: updateError } = await supabase
      .from("business_profiles")
      .update({
        custom_ads: {
          enabled: false,
          image_url: "",
          link_url: "",
          uploaded_at: null,
        }
      })
      .eq("id", user.id);

    if (updateError) {
      return {
        success: false,
        error: "Failed to delete custom ad",
      };
    }

    return { success: true };

  } catch (error) {
    console.error("Custom ad delete error:", error);
    return {
      success: false,
      error: "An unexpected error occurred",
    };
  }
}
